//****************************************************************************
// @Module        Timer 2
// @Filename      T2.H
// @Project       CL2.0.dav
//----------------------------------------------------------------------------
// @Controller    Infineon XC886CLM-8FF
//
// @Compiler      Keil
//
// @Codegenerator 1.9
//
// @Description:  This file contains all function prototypes and macros for 
//                the T2 module.
//
//----------------------------------------------------------------------------
// @Date          2013-7-5 07:00:09
//
//****************************************************************************

// USER CODE BEGIN (T2_Header,1)

// USER CODE END



#ifndef _T2_H_
#define _T2_H_

//****************************************************************************
// @Project Includes
//****************************************************************************

// USER CODE BEGIN (T2_Header,2)

// USER CODE END


//****************************************************************************
// @Macros
//****************************************************************************

// USER CODE BEGIN (T2_Header,3)

// USER CODE END


//****************************************************************************
// @Defines
//****************************************************************************

// USER CODE BEGIN (T2_Header,4)

// USER CODE END

//****************************************************************************
// @Typedefs
//****************************************************************************

// USER CODE BEGIN (T2_Header,5)

// USER CODE END


//****************************************************************************
// @Imported Global Variables
//****************************************************************************

// USER CODE BEGIN (T2_Header,6)

// USER CODE END


//****************************************************************************
// @Global Variables
//****************************************************************************

// USER CODE BEGIN (T2_Header,7)

// USER CODE END


//****************************************************************************
// @Prototypes Of Global Functions
//****************************************************************************

void T2_vInit(void);

// USER CODE BEGIN (T2_Header,8)

// USER CODE END


//****************************************************************************
// @Interrupt Vectors
//****************************************************************************


//   Shared interrupt vector definitions are defined in SHARED_INT.H

// USER CODE BEGIN (T2_Header,9)

// USER CODE END

#endif  // ifndef _T2_H_

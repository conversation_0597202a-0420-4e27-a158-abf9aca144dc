//****************************************************************************
// File Name     XC8Memory.c
//----------------------------------------------------------------------------
// Derivatives   Infineon XC88x AA and AB
//
// Description   Sets of function to handle the memory in the XC88x AA.
//               In particular, the Flash handling is taken care of here.
//
//----------------------------------------------------------------------------
// Date          14.03.2006 10:48:38
// Copyright     (c) 2006 Infineon Technologies
//****************************************************************************

#include "XC8Memory.h"

/*
// **************************************************************************
// Function Name:    void LoadXD2WLBuf (unsigned char xdata *address)
// Description:      Load  32byte of data from the XDATA to the WLBuf
//                   WLBuf is a buffer that is located in the IDATA-space
// Input Parameter:  *address ==>  pointer to the address
//                                 in the XDATA memory
// Output Parameter: none

// NOTE: Execution time and code can saved, 
//       if you have the possibility to write directly in
//       the buffer
// **************************************************************************
void LoadXD2WLBuf(unsigned char xdata *address)
{
// Loads Wordline Buffer with 32bytes from given address in XDATA
unsigned char i;
 for (i=0; i<BYTES_PER_WORDLINE; i++) {WLBuf[i] = *address++;}
}

// ***********************************************************************
// Function Name:    void LoadConst2WLBuf (unsigned char code *address)
// Description:      Load 32byte of data from the CODE to the WLBuf
//                   WLBuf is a buffer that is located in the IDATA-space
//                   This is used to copy data from one Flash location to another.
// Input Parameter:  *address ==>  pointer to the address
//            in the CODE memory
// Output Parameter: none

// NOTE: Execution time and code can saved, 
//       if you have the possibility to write directly in
//       the buffer
// ***********************************************************************
void LoadConst2WLBuf(unsigned char code *address)
{
// Loads Wordline Buffer with 32bytes from given address in CODE
unsigned char i;
 for (i=0; i<BYTES_PER_WORDLINE; i++) 
 { WLBuf[i] = *address++;
 }
}

// **************************************************************************************
// Function Name:    bit ProgWL (unsigned char code *AdrBnkXSecYWLZ)
// Description:      Program the data from the WLBuf to the location in the Flash
//                   which is pointed by *AdrBnkXSecYWLZ
// Input Parameter:  *AdrBnkXSecYWLZ ==> pointer to the location in the FLASH.
//                                       The address must be aligned to a 32byte boundary
// Output Parameter: 1 = Success
//                   0 = Fail
// Note:             used Stack Size = 12
// **************************************************************************************
bit ProgWL(unsigned char code *AdrBnkXSecYWLZ)
{
unsigned int i;
 i = AdrBnkXSecYWLZ;
 MEM_DPH = i>>8;
 MEM_DPL = i;
 return (FlProg(&WLBuf[0]));
}

// *************************************************************************************
// Function Name:    void LoadConst2XD (unsigned char xdata *dstaddress, 
//                   unsigned char code *srcaddress, unsigned int length)
// Description:      Copy the data from the CODE memory to XDATA memory
// Input Parameter:  *srcaddress ==> pointer to the location in the FLASH/EEPROM.
//                   lenght      ==> The number of byte to be copied
// Output Parameter: *dstaddress ==> pointer to the location in the XDATA memory.
//                                   The data will be copied to this location

// NOTE: Execution time and code can saved, 
//       if you have the possibility to write directly in
//       the buffer
// *************************************************************************************
void LoadConst2XD(unsigned char xdata *dstaddress, unsigned char code *srcaddress, unsigned char length)
{
// Loads number of constants to XDATA at given addresses
unsigned char i;
 for (i=0; i<length; i++) 
 { *dstaddress++ = *srcaddress++;
 }
}

// *************************************************************************************
// Function Name:    void LoadXD2XD (unsigned char xdata *dstaddress, 
//                   unsigned char xdata *srcaddress, unsigned int length)
// Description:      Copy the data from the XDATA memory to XDATA memory
// Input Parameter:  *srcaddress ==> pointer to the location in the XDATA memory.
//                                   No byte alignment is necessary
//                   lenght      ==> The number of byte to be copied
// Output Parameter: *dstaddress ==> pointer to the location in the XDATA memory.
//                                   The data will be copied to this location
//
// NOTE: Execution time and code can saved, 
//       if you have the possibility to write directly in
//       the buffer
// *************************************************************************************
void LoadXD2XD(unsigned char xdata *dstaddress, unsigned char xdata *srcaddress, unsigned int length)
{
// Copies number of bytes in XDATA to XDATA at given addresses
unsigned int i;
 for (i=0; i<length; i++) 
 { *dstaddress++ = *srcaddress++;
 }
}

// *************************************************************************************
// Function Name:    unsigned char ReadConst (unsigned char code *address) 
// Description:      Read from CODE memory
// Input Parameter:  *address ==> pointer to the location in the CODE memory.
//                                No byte alignment is necessary
// Return Value:  Data Byte
// *************************************************************************************
unsigned char ReadConst(unsigned char code *address)
{
 return (*address);
}

*/
BL51 BANKED LINKER/LOCATER V6.22                                                        07/06/2013  19:40:47  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
C:\KEIL\C51\BIN\BL51.EXE START_XC.obj, MAIN.obj, CAN.obj, boot.obj, XC88x_FLHANDLER.obj TO bootload RAMSIZE (256)


MEMORY MODEL: SMALL


INPUT MODULES INCLUDED:
  START_XC.obj (?C_STARTUP)
  MAIN.obj (MAIN)
  CAN.obj (CAN)
  boot.obj (BOOT)
  XC88x_FLHANDLER.obj (XC88X_FLHANDLER)
  C:\KEIL\C51\LIB\C51S.LIB (?C?COPY)
  C:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  C:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  C:\KEIL\C51\LIB\C51S.LIB (?C?CILDOPTR)
  C:\KEIL\C51\LIB\C51S.LIB (?C?CSTPTR)
  C:\KEIL\C51\LIB\C51S.LIB (?C?CSTOPTR)
  C:\KEIL\C51\LIB\C51S.LIB (?C?ULCMP)
  C:\KEIL\C51\LIB\C51S.LIB (?C?ULSHR)
  C:\KEIL\C51\LIB\C51S.LIB (?C?LLDPTR)
  C:\KEIL\C51\LIB\C51S.LIB (?C?LLDIDATA)
  C:\KEIL\C51\LIB\C51S.LIB (?C?LLDXDATA)
  C:\KEIL\C51\LIB\C51S.LIB (?C?LLDPDATA)
  C:\KEIL\C51\LIB\C51S.LIB (?C?LLDCODE)


LINK MAP OF MODULE:  bootload (?C_STARTUP)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0006H     UNIT         ?DT?CAN
                    000EH     000AH                  *** GAP ***
            REG     0018H     0008H     ABSOLUTE     "REG BANK 3"
                    0020H     0016H                  *** GAP ***
            DATA    0036H     0008H     ABSOLUTE     
            DATA    003EH     002EH     UNIT         _DATA_GROUP_
                    006CH     0014H                  *** GAP ***
            IDATA   0080H     0020H     ABSOLUTE     
            IDATA   00A0H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     02C1H     UNIT         ?PR?_CAN_UBREADFIFO?CAN
            CODE    02C4H     021BH     UNIT         ?C?LIB_CODE
            CODE    04DFH     01F3H     UNIT         ?PR?CAN_VINIT?CAN
            CODE    06D2H     0160H     UNIT         ?PR?BOOTMAIN?BOOT
            CODE    0832H     00C3H     UNIT         ?PR?FLASHREAD?BOOT
            CODE    08F5H     00A5H     UNIT         ?PR?_CAN_VLOADDATA?CAN
            CODE    099AH     005BH     UNIT         ?C_C51STARTUP
            CODE    09F5H     0054H     UNIT         ?PR?_CAN_UBREQUESTMSGOBJ?CAN
            CODE    0A49H     0041H     UNIT         ?PR?_CAN_VTRANSMIT?CAN
            CODE    0A8AH     0040H     UNIT         ?PR?_CAN_WAITTRANSMIT?BOOT
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 2


            CODE    0ACAH     003AH     UNIT         ?PR?_CAN_SENDACK?BOOT
            CODE    0B04H     002DH     UNIT         ?PR?_DFLERASE?XC88X_FLHANDLER
            CODE    0B31H     0028H     UNIT         ?PR?_CAN_VWRITEAMDATA?CAN
            CODE    0B59H     0028H     UNIT         ?PR?_FLPROG?XC88X_FLHANDLER
            CODE    0B81H     0024H     UNIT         ?PR?FLASH_WAIT?BOOT
            CODE    0BA5H     0017H     UNIT         ?PR?_CAN_VSETLISTCOMMAND?CAN
            CODE    0BBCH     0017H     UNIT         ?PR?CAN_SETWDTRESET?BOOT
            CODE    0BD3H     0016H     UNIT         ?PR?MAIN_VINIT?MAIN
            CODE    0BE9H     0008H     UNIT         ?PR?MAIN?MAIN
            CODE    0BF1H     0008H     UNIT         ?CO?BOOT



OVERLAY MAP OF MODULE:   bootload (?C_STARTUP)


SEGMENT                                 DATA_GROUP 
  +--> CALLED SEGMENT                 START    LENGTH
-----------------------------------------------------
?C_C51STARTUP                         -----    -----
  +--> ?PR?MAIN?MAIN

?PR?MAIN?MAIN                         -----    -----
  +--> ?PR?MAIN_VINIT?MAIN
  +--> ?PR?BOOTMAIN?BOOT

?PR?MAIN_VINIT?MAIN                   -----    -----
  +--> ?PR?CAN_VINIT?CAN

?PR?CAN_VINIT?CAN                     -----    -----
  +--> ?PR?_CAN_VSETLISTCOMMAND?CAN
  +--> ?PR?_CAN_VWRITEAMDATA?CAN

?PR?_CAN_VSETLISTCOMMAND?CAN          -----    -----
  +--> ?PR?_CAN_VWRITEAMDATA?CAN

?PR?_CAN_VWRITEAMDATA?CAN             003EH    0004H

?PR?BOOTMAIN?BOOT                     003EH    001EH
  +--> ?PR?_CAN_UBREADFIFO?CAN
  +--> ?PR?FLASHREAD?BOOT
  +--> ?PR?_DFLERASE?XC88X_FLHANDLER
  +--> ?PR?FLASH_WAIT?BOOT
  +--> ?PR?_CAN_SENDACK?BOOT
  +--> ?PR?CAN_SETWDTRESET?BOOT
  +--> ?PR?_FLPROG?XC88X_FLHANDLER

?PR?_CAN_UBREADFIFO?CAN               005CH    0006H

?PR?FLASHREAD?BOOT                    005CH    000BH
  +--> ?PR?_CAN_VLOADDATA?CAN
  +--> ?PR?_CAN_VTRANSMIT?CAN
  +--> ?PR?_CAN_WAITTRANSMIT?BOOT

?PR?_CAN_WAITTRANSMIT?BOOT            0067H    0005H
  +--> ?PR?_CAN_UBREQUESTMSGOBJ?CAN

BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 3


?PR?FLASH_WAIT?BOOT                   -----    -----
  +--> ?PR?CAN_SETWDTRESET?BOOT

?PR?_CAN_SENDACK?BOOT                 005CH    000AH
  +--> ?CO?BOOT
  +--> ?PR?_CAN_VLOADDATA?CAN
  +--> ?PR?_CAN_VTRANSMIT?CAN
  +--> ?PR?_CAN_WAITTRANSMIT?BOOT



SYMBOL TABLE OF MODULE:  bootload (?C_STARTUP)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ?C_STARTUP
  C:099AH         SEGMENT       ?C_C51STARTUP
  I:00A0H         SEGMENT       ?STACK
  D:00A2H         PUBLIC        ?C?DPSEL
  C:0000H         PUBLIC        ?C_STARTUP
  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  D:00BAH         SYMBOL        CMCON
  C:09ADH         SYMBOL        DELAYXTAL
  C:09ABH         SYMBOL        DELAYXTAL0
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0000H         SYMBOL        IBPSTACK
  N:0100H         SYMBOL        IBPSTACKTOP
  N:0100H         SYMBOL        IDATALEN
  C:09DEH         SYMBOL        IDATALOOP
  N:0001H         SYMBOL        LIN_BSL
  N:0001H         SYMBOL        LIN_NAC
  N:0001H         SYMBOL        LIN_NAD
  D:0096H         SYMBOL        MEX3
  N:000AH         SYMBOL        NDIV
  N:0002H         SYMBOL        NDIV_XC86X
  N:0018H         SYMBOL        NDIV_XC87X_PLL_CON
  N:0020H         SYMBOL        NDIV_XC87X_PLL_CON1
  N:000AH         SYMBOL        NDIV_XC88X
  N:0000H         SYMBOL        NR_XC87X
  N:0000H         SYMBOL        OD_XC87X
  C:09B1H         SYMBOL        OSCR_NOTSET
  D:00B6H         SYMBOL        OSC_CON
  D:00BBH         SYMBOL        PASSWD
  N:0000H         SYMBOL        PBPSTACK
  N:0100H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:F000H         SYMBOL        PDATASTART
  D:00B7H         SYMBOL        PLL_CON
  D:00EAH         SYMBOL        PLL_CON1
  N:00F0H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00BFH         SYMBOL        SCU_PAGE
  D:0081H         SYMBOL        SP
  C:099AH         SYMBOL        STARTUP1
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 4


  C:09CEH         SYMBOL        WAIT_LOCK
  D:00B3H         SYMBOL        XADDRH
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XC82X_CHIP
  N:0000H         SYMBOL        XC864_CHIP
  N:0000H         SYMBOL        XC866_CHIP
  N:0000H         SYMBOL        XC874_CHIP_13FF
  N:0000H         SYMBOL        XC874_CHIP_16FF
  N:0000H         SYMBOL        XC878_CHIP_13FF
  N:0000H         SYMBOL        XC878_CHIP_16FF
  N:0001H         SYMBOL        XC88X_CHIP
  N:0600H         SYMBOL        XDATALEN
  C:09E9H         SYMBOL        XDATALOOP
  N:F000H         SYMBOL        XDATASTART
  N:0001H         SYMBOL        XTAL
  C:0000H         LINE#         312
  C:099AH         LINE#         367
  C:099DH         LINE#         368
  C:09A0H         LINE#         369
  C:09A3H         LINE#         370
  C:09A6H         LINE#         371
  C:09A9H         LINE#         373
  C:09ABH         LINE#         375
  C:09ADH         LINE#         377
  C:09AFH         LINE#         378
  C:09B1H         LINE#         382
  C:09B3H         LINE#         383
  C:09B6H         LINE#         388
  C:09B8H         LINE#         389
  C:09BAH         LINE#         390
  C:09BDH         LINE#         394
  C:09C0H         LINE#         395
  C:09C3H         LINE#         396
  C:09C6H         LINE#         397
  C:09C9H         LINE#         400
  C:09CCH         LINE#         401
  C:09CEH         LINE#         409
  C:09D0H         LINE#         410
  C:09D2H         LINE#         411
  C:09D5H         LINE#         413
  C:09D8H         LINE#         414
  C:09DBH         LINE#         419
  C:09DDH         LINE#         420
  C:09DEH         LINE#         421
  C:09DFH         LINE#         422
  C:09E1H         LINE#         439
  C:09E4H         LINE#         440
  C:09E6H         LINE#         444
  C:09E8H         LINE#         446
  C:09E9H         LINE#         447
  C:09EAH         LINE#         448
  C:09EBH         LINE#         449
  C:09EDH         LINE#         450
  C:09EFH         LINE#         486
  C:09F2H         LINE#         488
  -------         ENDMOD        ?C_STARTUP
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 5



  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00F9H         PUBLIC        IPH1
  C:0BD3H         PUBLIC        MAIN_vInit
  D:00B8H         PUBLIC        IP
  C:0BE9H         PUBLIC        main
  D:00F8H         PUBLIC        IP1
  D:00B9H         PUBLIC        IPH
  D:00BFH         PUBLIC        SCU_PAGE
  D:00BAH         PUBLIC        CMCON
  -------         PROC          MAIN_VINIT
  C:0BD3H         LINE#         122
  C:0BD3H         LINE#         123
  C:0BD3H         LINE#         138
  C:0BD6H         LINE#         140
  C:0BD9H         LINE#         142
  C:0BDCH         LINE#         151
  C:0BDFH         LINE#         156
  C:0BE2H         LINE#         157
  C:0BE4H         LINE#         158
  C:0BE6H         LINE#         159
  C:0BE8H         LINE#         172
  -------         ENDPROC       MAIN_VINIT
  -------         PROC          MAIN
  C:0BE9H         LINE#         196
  C:0BE9H         LINE#         197
  C:0BE9H         LINE#         202
  C:0BECH         LINE#         208
  C:0BECH         LINE#         209
  C:0BECH         LINE#         212
  C:0BEFH         LINE#         215
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        CAN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00DBH         PUBLIC        CAN_DATA0
  D:00DCH         PUBLIC        CAN_DATA1
  D:00DDH         PUBLIC        CAN_DATA2
  D:00DAH         PUBLIC        CAN_ADH
  C:0A49H         PUBLIC        _CAN_vTransmit
  D:00DEH         PUBLIC        CAN_DATA3
  D:00D9H         PUBLIC        CAN_ADL
  D:0008H         SYMBOL        aubFIFOReadPtr
  D:00D8H         PUBLIC        CAN_ADCON
  D:000BH         SYMBOL        aubFIFOWritePtr
  C:08F5H         PUBLIC        _CAN_vLoadData
  C:0003H         PUBLIC        _CAN_ubReadFIFO
  D:00DBH         PUBLIC        CAN_DATA01
  D:00DDH         PUBLIC        CAN_DATA23
  C:0BABH         PUBLIC        _CAN_vSetListCommand
  C:09F5H         PUBLIC        _CAN_ubRequestMsgObj
  C:04DFH         PUBLIC        CAN_vInit
  D:0091H         PUBLIC        P1_DIR
  C:0B3CH         PUBLIC        _CAN_vWriteAMData
  D:00B2H         PUBLIC        PORT_PAGE
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 6


  D:00D9H         PUBLIC        CAN_ADLH
  D:0090H         PUBLIC        P1_ALTSEL0
  D:0091H         PUBLIC        P1_ALTSEL1
  -------         PROC          CAN_VINIT
  C:04DFH         LINE#         127
  C:04DFH         LINE#         128
  C:04DFH         LINE#         143
  C:04E5H         LINE#         144
  C:04E8H         LINE#         145
  C:04EDH         LINE#         147
  C:04F0H         LINE#         148
  C:04F2H         LINE#         159
  C:04F8H         LINE#         160
  C:04FBH         LINE#         161
  C:0503H         LINE#         166
  C:0506H         LINE#         171
  C:0509H         LINE#         172
  C:050BH         LINE#         173
  C:0513H         LINE#         187
  C:0516H         LINE#         188
  C:0518H         LINE#         189
  C:0520H         LINE#         207
  C:0523H         LINE#         208
  C:0526H         LINE#         209
  C:052EH         LINE#         220
  C:0531H         LINE#         221
  C:0539H         LINE#         234
  C:053CH         LINE#         235
  C:053EH         LINE#         236
  C:0540H         LINE#         237
  C:0548H         LINE#         250
  C:054EH         LINE#         251
  C:0551H         LINE#         252
  C:0559H         LINE#         262
  C:055CH         LINE#         263
  C:055FH         LINE#         264
  C:0562H         LINE#         266
  C:0565H         LINE#         267
  C:0568H         LINE#         274
  C:056DH         LINE#         277
  C:0576H         LINE#         278
  C:057BH         LINE#         279
  C:0580H         LINE#         280
  C:058BH         LINE#         294
  C:0591H         LINE#         296
  C:0596H         LINE#         299
  C:0599H         LINE#         308
  C:05A4H         LINE#         311
  C:05A4H         LINE#         316
  C:05A7H         LINE#         319
  C:05A7H         LINE#         323
  C:05AAH         LINE#         326
  C:05ADH         LINE#         333
  C:05B8H         LINE#         336
  C:05B8H         LINE#         342
  C:05BBH         LINE#         345
  C:05BEH         LINE#         354
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 7


  C:05C9H         LINE#         357
  C:05CCH         LINE#         360
  C:05CFH         LINE#         367
  C:05D8H         LINE#         378
  C:05DEH         LINE#         380
  C:05E9H         LINE#         383
  C:05ECH         LINE#         392
  C:05F7H         LINE#         395
  C:05F7H         LINE#         400
  C:05FAH         LINE#         403
  C:05FAH         LINE#         407
  C:05FDH         LINE#         410
  C:0600H         LINE#         417
  C:060BH         LINE#         420
  C:060EH         LINE#         426
  C:0619H         LINE#         429
  C:0619H         LINE#         434
  C:061CH         LINE#         437
  C:061CH         LINE#         445
  C:061FH         LINE#         456
  C:0625H         LINE#         458
  C:0630H         LINE#         461
  C:0633H         LINE#         470
  C:063EH         LINE#         473
  C:063EH         LINE#         478
  C:0641H         LINE#         481
  C:0641H         LINE#         485
  C:0644H         LINE#         488
  C:0647H         LINE#         495
  C:0652H         LINE#         498
  C:0655H         LINE#         504
  C:0660H         LINE#         507
  C:0660H         LINE#         512
  C:0663H         LINE#         515
  C:0663H         LINE#         523
  C:0666H         LINE#         534
  C:066CH         LINE#         536
  C:0677H         LINE#         539
  C:067AH         LINE#         548
  C:0685H         LINE#         551
  C:0685H         LINE#         556
  C:0688H         LINE#         559
  C:0688H         LINE#         563
  C:068BH         LINE#         566
  C:068EH         LINE#         573
  C:0699H         LINE#         576
  C:069CH         LINE#         582
  C:06A7H         LINE#         585
  C:06A7H         LINE#         590
  C:06AAH         LINE#         593
  C:06ADH         LINE#         600
  C:06B8H         LINE#         734
  C:06BEH         LINE#         735
  C:06C6H         LINE#         736
  C:06C9H         LINE#         737
  C:06D1H         LINE#         746
  -------         ENDPROC       CAN_VINIT
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 8


  C:0B31H         SYMBOL        L?0095
  C:0B36H         SYMBOL        L?0096
  -------         PROC          L?0094
  -------         ENDPROC       L?0094
  C:0B31H         SYMBOL        L?0095
  C:0B36H         SYMBOL        L?0096
  -------         PROC          _CAN_VWRITEAMDATA
  D:0004H         SYMBOL        ulValue
  -------         DO            
  D:003EH         SYMBOL        ulData
  -------         ENDDO         
  C:0B3CH         LINE#         785
  C:0B3CH         LINE#         786
  C:0B3CH         LINE#         788
  C:0B44H         LINE#         790
  C:0B47H         LINE#         791
  C:0B4AH         LINE#         792
  C:0B4DH         LINE#         793
  C:0B50H         LINE#         794
  C:0B58H         LINE#         795
  -------         ENDPROC       _CAN_VWRITEAMDATA
  -------         PROC          L?0097
  -------         ENDPROC       L?0097
  -------         PROC          _CAN_VSETLISTCOMMAND
  D:0004H         SYMBOL        ulVal
  C:0BABH         LINE#         820
  C:0BABH         LINE#         821
  C:0BABH         LINE#         822
  C:0BAEH         LINE#         823
  C:0BB1H         LINE#         824
  C:0BB6H         LINE#         826
  C:0BB9H         LINE#         827
  C:0BBBH         LINE#         828
  -------         ENDPROC       _CAN_VSETLISTCOMMAND
  -------         PROC          _CAN_UBREQUESTMSGOBJ
  D:0007H         SYMBOL        ubObjNr
  -------         DO            
  D:0005H         SYMBOL        ubReturn
  -------         ENDDO         
  C:09F5H         LINE#         863
  C:09F5H         LINE#         864
  C:09F5H         LINE#         865
  C:09F7H         LINE#         867
  C:0A08H         LINE#         869
  C:0A18H         LINE#         870
  C:0A20H         LINE#         872
  C:0A25H         LINE#         873
  C:0A25H         LINE#         874
  C:0A38H         LINE#         875
  C:0A3AH         LINE#         876
  C:0A3AH         LINE#         877
  C:0A46H         LINE#         879
  C:0A48H         LINE#         880
  -------         ENDPROC       _CAN_UBREQUESTMSGOBJ
  -------         PROC          _CAN_VTRANSMIT
  D:0007H         SYMBOL        ubObjNr
  C:0A49H         LINE#         909
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 9


  C:0A49H         LINE#         910
  C:0A49H         LINE#         911
  C:0A5AH         LINE#         913
  C:0A6AH         LINE#         914
  C:0A7DH         LINE#         916
  C:0A89H         LINE#         918
  -------         ENDPROC       _CAN_VTRANSMIT
  -------         PROC          _CAN_VLOADDATA
  D:0007H         SYMBOL        ubObjNr
  D:0001H         SYMBOL        ulpubData
  C:08F5H         LINE#         949
  C:08F5H         LINE#         950
  C:08F5H         LINE#         951
  C:0906H         LINE#         953
  C:0916H         LINE#         954
  C:0928H         LINE#         957
  C:092BH         LINE#         960
  C:092EH         LINE#         963
  C:0931H         LINE#         965
  C:094DH         LINE#         968
  C:0950H         LINE#         970
  C:0957H         LINE#         972
  C:0973H         LINE#         975
  C:0976H         LINE#         978
  C:0979H         LINE#         980
  C:098DH         LINE#         982
  C:0999H         LINE#         984
  -------         ENDPROC       _CAN_VLOADDATA
  -------         PROC          _CAN_UBREADFIFO
  D:005CH         SYMBOL        ubObjNr
  D:005DH         SYMBOL        pstObj
  -------         DO            
  D:0060H         SYMBOL        j
  D:0007H         SYMBOL        ubTemp
  D:0061H         SYMBOL        ubReturn
  -------         ENDDO         
  C:0003H         LINE#         1023
  C:000BH         LINE#         1024
  C:000BH         LINE#         1027
  C:000EH         LINE#         1029
  C:001FH         LINE#         1031
  C:0030H         LINE#         1032
  C:0038H         LINE#         1033
  C:0040H         LINE#         1034
  C:0040H         LINE#         1035
  C:0048H         LINE#         1036
  C:004BH         LINE#         1038
  C:005CH         LINE#         1039
  C:0064H         LINE#         1040
  C:006CH         LINE#         1041
  C:006CH         LINE#         1042
  C:007FH         LINE#         1044
  C:0087H         LINE#         1045
  C:008CH         LINE#         1046
  C:008CH         LINE#         1048
  C:009DH         LINE#         1049
  C:00A5H         LINE#         1051
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 10


  C:00ACH         LINE#         1052
  C:00AEH         LINE#         1054
  C:00AEH         LINE#         1056
  C:00B5H         LINE#         1057
  C:00B5H         LINE#         1059
  C:00C6H         LINE#         1060
  C:00CEH         LINE#         1062
  C:00D3H         LINE#         1063
  C:00D3H         LINE#         1064
  C:00E6H         LINE#         1065
  C:00E9H         LINE#         1066
  C:00E9H         LINE#         1069
  C:00FAH         LINE#         1070
  C:0102H         LINE#         1071
  C:010DH         LINE#         1074
  C:011EH         LINE#         1076
  C:0126H         LINE#         1077
  C:0134H         LINE#         1078
  C:013CH         LINE#         1079
  C:0144H         LINE#         1080
  C:014CH         LINE#         1083
  C:014FH         LINE#         1087
  C:0157H         LINE#         1088
  C:0165H         LINE#         1089
  C:016DH         LINE#         1090
  C:0175H         LINE#         1091
  C:017DH         LINE#         1093
  C:018EH         LINE#         1094
  C:0196H         LINE#         1095
  C:019BH         LINE#         1096
  C:019BH         LINE#         1097
  C:01AEH         LINE#         1098
  C:01AEH         LINE#         1101
  C:01B1H         LINE#         1103
  C:01B9H         LINE#         1104
  C:01BEH         LINE#         1105
  C:01BEH         LINE#         1106
  C:01CEH         LINE#         1107
  C:01D6H         LINE#         1108
  C:01DEH         LINE#         1109
  C:01E6H         LINE#         1110
  C:01F5H         LINE#         1111
  C:01F7H         LINE#         1113
  C:01F7H         LINE#         1114
  C:020BH         LINE#         1115
  C:0215H         LINE#         1116
  C:021CH         LINE#         1117
  C:0223H         LINE#         1119
  C:0223H         LINE#         1120
  C:0231H         LINE#         1121
  C:0242H         LINE#         1122
  C:024FH         LINE#         1123
  C:024FH         LINE#         1125
  C:0260H         LINE#         1126
  C:0268H         LINE#         1128
  C:0276H         LINE#         1129
  C:027EH         LINE#         1132
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 11


  C:028FH         LINE#         1133
  C:0297H         LINE#         1134
  C:029CH         LINE#         1135
  C:029CH         LINE#         1136
  C:02AFH         LINE#         1137
  C:02B2H         LINE#         1138
  C:02B2H         LINE#         1139
  C:02B5H         LINE#         1140
  C:02B5H         LINE#         1141
  C:02B5H         LINE#         1142
  C:02C1H         LINE#         1144
  C:02C3H         LINE#         1145
  -------         ENDPROC       _CAN_UBREADFIFO
  -------         ENDMOD        CAN

  -------         MODULE        BOOT
  C:0000H         SYMBOL        _ICE_DUMMY_
  B:00A8H.7       PUBLIC        EA
  D:00BCH         PUBLIC        NMISR
  D:008FH         PUBLIC        SYSCON0
  C:06D2H         PUBLIC        BootMain
  I:0080H         PUBLIC        WLBuf
  C:0BF1H         SYMBOL        _?ix1000
  C:0A8AH         PUBLIC        _CAN_waitTransmit
  D:00BBH         PUBLIC        WDTCON
  D:00BCH         PUBLIC        WDTREL
  D:00BBH         PUBLIC        PASSWD
  B:00C0H.2       PUBLIC        TR2
  C:0B81H         PUBLIC        Flash_Wait
  D:00BFH         PUBLIC        SCU_PAGE
  C:0832H         PUBLIC        FlashRead
  C:0AD2H         PUBLIC        _CAN_sendAck
  C:0BBCH         PUBLIC        CAN_setWDTReset
  -------         PROC          CAN_SETWDTRESET
  C:0BBCH         LINE#         8
  C:0BBCH         LINE#         9
  C:0BBCH         LINE#         11
  C:0BBFH         LINE#         12
  C:0BC2H         LINE#         13
  C:0BC5H         LINE#         14
  C:0BC8H         LINE#         15
  C:0BCBH         LINE#         16
  C:0BCEH         LINE#         17
  C:0BD1H         LINE#         18
  -------         ENDPROC       CAN_SETWDTRESET
  -------         PROC          _CAN_WAITTRANSMIT
  D:0067H         SYMBOL        RgMsgobj
  -------         DO            
  D:0068H         SYMBOL        i
  -------         ENDDO         
  C:0A8AH         LINE#         22
  C:0A8CH         LINE#         23
  C:0A8CH         LINE#         25
  C:0AC9H         LINE#         26
  -------         ENDPROC       _CAN_WAITTRANSMIT
  -------         PROC          L?0064
  -------         ENDPROC       L?0064
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 12


  -------         PROC          _CAN_SENDACK
  D:005CH         SYMBOL        Ack0
  D:005DH         SYMBOL        Ack1
  -------         DO            
  D:005EH         SYMBOL        Arrtmp
  -------         ENDDO         
  C:0AD2H         LINE#         33
  C:0AD6H         LINE#         34
  C:0AD6H         LINE#         35
  C:0AE9H         LINE#         37
  C:0AECH         LINE#         38
  C:0AEFH         LINE#         40
  C:0AFAH         LINE#         41
  C:0AFFH         LINE#         42
  -------         ENDPROC       _CAN_SENDACK
  -------         PROC          FLASH_WAIT
  -------         DO            
  D:0005H         SYMBOL        ubCount0
  D:0006H         SYMBOL        ubCount1
  D:0007H         SYMBOL        ubCount2
  -------         ENDDO         
  C:0B81H         LINE#         51
  C:0B81H         LINE#         52
  C:0B81H         LINE#         55
  C:0B84H         LINE#         56
  C:0B87H         LINE#         57
  C:0B88H         LINE#         58
  C:0B88H         LINE#         59
  C:0B8AH         LINE#         60
  C:0B8AH         LINE#         61
  C:0B8CH         LINE#         62
  C:0B8CH         LINE#         63
  C:0B91H         LINE#         64
  C:0B91H         LINE#         65
  C:0B95H         LINE#         67
  C:0B95H         LINE#         68
  C:0B99H         LINE#         69
  C:0B9DH         LINE#         70
  C:0BA1H         LINE#         71
  C:0BA4H         LINE#         72
  -------         ENDPROC       FLASH_WAIT
  -------         PROC          FLASHREAD
  -------         DO            
  D:005CH         SYMBOL        i
  D:005DH         SYMBOL        pBootCode
  D:005FH         SYMBOL        tmp
  -------         ENDDO         
  C:0832H         LINE#         75
  C:0832H         LINE#         76
  C:0832H         LINE#         81
  C:0838H         LINE#         83
  C:083BH         LINE#         84
  C:083BH         LINE#         85
  C:084EH         LINE#         86
  C:0861H         LINE#         87
  C:0874H         LINE#         88
  C:0887H         LINE#         89
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 13


  C:089AH         LINE#         90
  C:08ADH         LINE#         91
  C:08C0H         LINE#         92
  C:08D3H         LINE#         94
  C:08DEH         LINE#         95
  C:08E3H         LINE#         96
  C:08E8H         LINE#         97
  C:08F4H         LINE#         99
  -------         ENDPROC       FLASHREAD
  -------         PROC          BOOTMAIN
  -------         DO            
  D:003EH         SYMBOL        StrBootRx
  D:0052H         SYMBOL        ulCANData
  D:005AH         SYMBOL        i
  D:005BH         SYMBOL        RgRtnReadFIFO
  -------         ENDDO         
  C:06D2H         LINE#         103
  C:06D2H         LINE#         104
  C:06D2H         LINE#         108
  C:06D5H         LINE#         110
  C:06E0H         LINE#         111
  C:06E9H         LINE#         112
  C:06E9H         LINE#         113
  C:06ECH         LINE#         114
  C:06EFH         LINE#         115
  C:06F2H         LINE#         116
  C:06F5H         LINE#         117
  C:06F8H         LINE#         118
  C:06FBH         LINE#         119
  C:06FEH         LINE#         120
  C:0701H         LINE#         123
  C:0703H         LINE#         124
  C:0705H         LINE#         127
  C:070EH         LINE#         128
  C:070EH         LINE#         129
  C:0710H         LINE#         131
  C:0713H         LINE#         132
  C:0713H         LINE#         134
  C:071CH         LINE#         135
  C:071CH         LINE#         136
  C:071FH         LINE#         137
  C:0721H         LINE#         138
  C:0724H         LINE#         139
  C:072EH         LINE#         140
  C:0731H         LINE#         141
  C:0733H         LINE#         142
  C:0733H         LINE#         143
  C:0735H         LINE#         145
  C:073FH         LINE#         146
  C:073FH         LINE#         147
  C:0746H         LINE#         148
  C:0749H         LINE#         149
  C:0749H         LINE#         151
  C:0753H         LINE#         152
  C:0753H         LINE#         153
  C:0755H         LINE#         154
  C:0757H         LINE#         155
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 14


  C:075AH         LINE#         157
  C:075DH         LINE#         158
  C:075DH         LINE#         159
  C:0764H         LINE#         160
  C:076BH         LINE#         161
  C:0770H         LINE#         162
  C:0773H         LINE#         163
  C:077AH         LINE#         164
  C:077DH         LINE#         165
  C:077DH         LINE#         167
  C:0789H         LINE#         168
  C:0789H         LINE#         169
  C:0789H         LINE#         170
  C:0789H         LINE#         171
  C:078CH         LINE#         172
  C:078FH         LINE#         173
  C:0799H         LINE#         174
  C:079CH         LINE#         175
  C:07A3H         LINE#         176
  C:07A6H         LINE#         178
  C:07ADH         LINE#         179
  C:07B0H         LINE#         180
  C:07B0H         LINE#         181
  C:07B7H         LINE#         182
  C:07BEH         LINE#         183
  C:07C2H         LINE#         184
  C:07C2H         LINE#         185
  C:07C2H         LINE#         186
  C:07C2H         LINE#         187
  C:07C2H         LINE#         188
  C:07C4H         LINE#         190
  C:07D0H         LINE#         191
  C:07D0H         LINE#         192
  C:07D0H         LINE#         193
  C:07D0H         LINE#         194
  C:07D3H         LINE#         195
  C:07D6H         LINE#         196
  C:07E0H         LINE#         197
  C:07E3H         LINE#         198
  C:07EAH         LINE#         199
  C:07EDH         LINE#         201
  C:07F4H         LINE#         202
  C:07F7H         LINE#         203
  C:07F7H         LINE#         204
  C:07FEH         LINE#         205
  C:0805H         LINE#         206
  C:0809H         LINE#         207
  C:080EH         LINE#         208
  C:0811H         LINE#         209
  C:0814H         LINE#         210
  C:0818H         LINE#         211
  C:081AH         LINE#         214
  C:081AH         LINE#         215
  C:081FH         LINE#         216
  C:081FH         LINE#         217
  C:0823H         LINE#         218
  C:0825H         LINE#         219
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 15


  C:082AH         LINE#         220
  C:082AH         LINE#         221
  C:0831H         LINE#         222
  C:0831H         LINE#         224
  -------         ENDPROC       BOOTMAIN
  -------         ENDMOD        BOOT

  -------         MODULE        XC88X_FLHANDLER
  C:0B04H         SEGMENT       ?PR?_DFLERASE?XC88X_FLHANDLER
  C:0B59H         SEGMENT       ?PR?_FLPROG?XC88X_FLHANDLER
  C:0B04H         PUBLIC        _DFLERASE
  C:0B59H         PUBLIC        _FLPROG
  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  C:DFF9H         SYMBOL        FLASH_ERASE
  C:DFF3H         SYMBOL        FLASH_ERASE_ABORT
  C:DFF6H         SYMBOL        FLASH_PROG
  C:DB06H         SYMBOL        FLASH_PROTECT
  C:DFF0H         SYMBOL        FLASH_READ_STATUS
  D:00D0H         SYMBOL        PSW
  C:0B29H         SYMBOL        _DERASE_FAIL
  C:0B79H         SYMBOL        _PROG_FAIL
  C:0B04H         LINE#         64
  C:0B06H         LINE#         65
  C:0B08H         LINE#         67
  C:0B0AH         LINE#         68
  C:0B0CH         LINE#         70
  C:0B0DH         LINE#         71
  C:0B0FH         LINE#         72
  C:0B11H         LINE#         73
  C:0B13H         LINE#         75
  C:0B15H         LINE#         76
  C:0B17H         LINE#         77
  C:0B19H         LINE#         87
  C:0B1CH         LINE#         90
  C:0B1FH         LINE#         91
  C:0B21H         LINE#         92
  C:0B23H         LINE#         93
  C:0B25H         LINE#         94
  C:0B27H         LINE#         95
  C:0B28H         LINE#         96
  C:0B29H         LINE#         98
  C:0B2BH         LINE#         99
  C:0B2DH         LINE#         100
  C:0B2FH         LINE#         101
  C:0B30H         LINE#         102
  C:0B59H         LINE#         121
  C:0B5BH         LINE#         124
  C:0B5DH         LINE#         125
  C:0B5FH         LINE#         126
  C:0B61H         LINE#         127
  C:0B63H         LINE#         129
  C:0B65H         LINE#         130
  C:0B67H         LINE#         131
  C:0B69H         LINE#         141
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 16


  C:0B6CH         LINE#         144
  C:0B6FH         LINE#         145
  C:0B71H         LINE#         146
  C:0B73H         LINE#         147
  C:0B75H         LINE#         148
  C:0B77H         LINE#         149
  C:0B78H         LINE#         150
  C:0B79H         LINE#         152
  C:0B7BH         LINE#         153
  C:0B7DH         LINE#         154
  C:0B7FH         LINE#         155
  C:0B80H         LINE#         156
  -------         ENDMOD        XC88X_FLHANDLER

  -------         MODULE        ?C?COPY
  C:0394H         PUBLIC        ?C?COPY
  -------         ENDMOD        ?C?COPY

  -------         MODULE        ?C?CLDPTR
  C:03BAH         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:03D3H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CILDOPTR
  C:0400H         PUBLIC        ?C?CILDOPTR
  -------         ENDMOD        ?C?CILDOPTR

  -------         MODULE        ?C?CSTPTR
  C:0433H         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:0445H         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR

  -------         MODULE        ?C?ULCMP
  C:0467H         PUBLIC        ?C?ULCMP
  -------         ENDMOD        ?C?ULCMP

  -------         MODULE        ?C?ULSHR
  C:0478H         PUBLIC        ?C?ULSHR
  -------         ENDMOD        ?C?ULSHR

  -------         MODULE        ?C?LLDPTR
  C:048BH         PUBLIC        ?C?LLDPTR
  -------         ENDMOD        ?C?LLDPTR

  -------         MODULE        ?C?LLDIDATA
  C:04ABH         PUBLIC        ?C?LLDIDATA
  -------         ENDMOD        ?C?LLDIDATA

  -------         MODULE        ?C?LLDXDATA
  C:04B7H         PUBLIC        ?C?LLDXDATA
  -------         ENDMOD        ?C?LLDXDATA
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  19:40:47  PAGE 17



  -------         MODULE        ?C?LLDPDATA
  C:04C3H         PUBLIC        ?C?LLDPDATA
  -------         ENDMOD        ?C?LLDPDATA

  -------         MODULE        ?C?LLDCODE
  C:04CFH         PUBLIC        ?C?LLDCODE
  -------         ENDMOD        ?C?LLDCODE

Program Size: data=109.0 xdata=0 code=3065
LINK/LOCATE RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)

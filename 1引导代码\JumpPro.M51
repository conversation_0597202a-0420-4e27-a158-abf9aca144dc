<PERSON><PERSON><PERSON> BANKED LINKER/LOCATER V6.22                                                        07/06/2013  21:44:15  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
C:\KEIL\C51\BIN\BL51.EXE START_XC.obj, MAIN.obj TO JumpPro RAMSIZE (256)


MEMORY MODEL: SMALL


INPUT MODULES INCLUDED:
  START_XC.obj (?C_STARTUP)
  MAIN.obj (MAIN)


LINK MAP OF MODULE:  JumpPro (?C_STARTUP)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            IDATA   0008H     0001H     UNIT         ?STACK

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     005BH     UNIT         ?C_C51STARTUP
            CODE    005EH     0012H     UNIT         ?PR?MAIN_VINIT?MAIN
            CODE    0070H     0005H     UNIT         ?PR?MAIN?MAIN



OVERLAY MAP OF MODULE:   JumpPro (?C_STARTUP)


SEGMENT
  +--> CALLED SEGMENT
---------------------
?C_C51STARTUP
  +--> ?PR?MAIN?MAIN

?PR?MAIN?MAIN
  +--> ?PR?MAIN_VINIT?MAIN



SYMBOL TABLE OF MODULE:  JumpPro (?C_STARTUP)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ?C_STARTUP
  C:0003H         SEGMENT       ?C_C51STARTUP
  I:0008H         SEGMENT       ?STACK
  D:00A2H         PUBLIC        ?C?DPSEL
  C:0000H         PUBLIC        ?C_STARTUP
  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  D:00BAH         SYMBOL        CMCON
  C:0016H         SYMBOL        DELAYXTAL
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  21:44:15  PAGE 2


  C:0014H         SYMBOL        DELAYXTAL0
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0000H         SYMBOL        IBPSTACK
  N:0100H         SYMBOL        IBPSTACKTOP
  N:0100H         SYMBOL        IDATALEN
  C:0047H         SYMBOL        IDATALOOP
  N:0001H         SYMBOL        LIN_BSL
  N:0001H         SYMBOL        LIN_NAC
  N:0001H         SYMBOL        LIN_NAD
  D:0096H         SYMBOL        MEX3
  N:000AH         SYMBOL        NDIV
  N:0002H         SYMBOL        NDIV_XC86X
  N:0018H         SYMBOL        NDIV_XC87X_PLL_CON
  N:0020H         SYMBOL        NDIV_XC87X_PLL_CON1
  N:000AH         SYMBOL        NDIV_XC88X
  N:0000H         SYMBOL        NR_XC87X
  N:0000H         SYMBOL        OD_XC87X
  C:001AH         SYMBOL        OSCR_NOTSET
  D:00B6H         SYMBOL        OSC_CON
  D:00BBH         SYMBOL        PASSWD
  N:0000H         SYMBOL        PBPSTACK
  N:0100H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:F000H         SYMBOL        PDATASTART
  D:00B7H         SYMBOL        PLL_CON
  D:00EAH         SYMBOL        PLL_CON1
  N:00F0H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00BFH         SYMBOL        SCU_PAGE
  D:0081H         SYMBOL        SP
  C:0003H         SYMBOL        STARTUP1
  C:0037H         SYMBOL        WAIT_LOCK
  D:00B3H         SYMBOL        XADDRH
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XC82X_CHIP
  N:0000H         SYMBOL        XC864_CHIP
  N:0000H         SYMBOL        XC866_CHIP
  N:0000H         SYMBOL        XC874_CHIP_13FF
  N:0000H         SYMBOL        XC874_CHIP_16FF
  N:0000H         SYMBOL        XC878_CHIP_13FF
  N:0000H         SYMBOL        XC878_CHIP_16FF
  N:0001H         SYMBOL        XC88X_CHIP
  N:0600H         SYMBOL        XDATALEN
  C:0052H         SYMBOL        XDATALOOP
  N:F000H         SYMBOL        XDATASTART
  N:0001H         SYMBOL        XTAL
  C:0000H         LINE#         312
  C:0003H         LINE#         367
  C:0006H         LINE#         368
  C:0009H         LINE#         369
  C:000CH         LINE#         370
  C:000FH         LINE#         371
  C:0012H         LINE#         373
  C:0014H         LINE#         375
  C:0016H         LINE#         377
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  21:44:15  PAGE 3


  C:0018H         LINE#         378
  C:001AH         LINE#         382
  C:001CH         LINE#         383
  C:001FH         LINE#         388
  C:0021H         LINE#         389
  C:0023H         LINE#         390
  C:0026H         LINE#         394
  C:0029H         LINE#         395
  C:002CH         LINE#         396
  C:002FH         LINE#         397
  C:0032H         LINE#         400
  C:0035H         LINE#         401
  C:0037H         LINE#         409
  C:0039H         LINE#         410
  C:003BH         LINE#         411
  C:003EH         LINE#         413
  C:0041H         LINE#         414
  C:0044H         LINE#         419
  C:0046H         LINE#         420
  C:0047H         LINE#         421
  C:0048H         LINE#         422
  C:004AH         LINE#         439
  C:004DH         LINE#         440
  C:004FH         LINE#         444
  C:0051H         LINE#         446
  C:0052H         LINE#         447
  C:0053H         LINE#         448
  C:0054H         LINE#         449
  C:0056H         LINE#         450
  C:0058H         LINE#         486
  C:005BH         LINE#         488
  -------         ENDMOD        ?C_STARTUP

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00F9H         PUBLIC        IPH1
  C:005EH         PUBLIC        MAIN_vInit
  D:00B8H         PUBLIC        IP
  C:0070H         PUBLIC        main
  D:00F8H         PUBLIC        IP1
  D:00B9H         PUBLIC        IPH
  D:00BFH         PUBLIC        SCU_PAGE
  D:00BAH         PUBLIC        CMCON
  -------         PROC          MAIN_VINIT
  C:005EH         LINE#         122
  C:005EH         LINE#         123
  C:005EH         LINE#         138
  C:0061H         LINE#         140
  C:0064H         LINE#         142
  C:0067H         LINE#         153
  C:0069H         LINE#         154
  C:006BH         LINE#         155
  C:006DH         LINE#         156
  C:006FH         LINE#         169
  -------         ENDPROC       MAIN_VINIT
  -------         PROC          MAIN
  C:0070H         LINE#         193
BL51 BANKED LINKER/LOCATER V6.22                                                      07/06/2013  21:44:15  PAGE 4


  C:0070H         LINE#         194
  C:0070H         LINE#         199
  C:0073H         LINE#         205
  C:0073H         LINE#         206
  C:0073H         LINE#         212
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

Program Size: data=9.0 xdata=0 code=117
LINK/LOCATE RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)

//****************************************************************************
// @Module        High Speed Synchronous Serial Interface(SSC)
// @Filename      SSC.H
// @Project       CL2.0.dav
//----------------------------------------------------------------------------
// @Controller    Infineon XC886CLM-8FF
//
// @Compiler      Keil
//
// @Codegenerator 1.9
//
// @Description:  This file contains all function prototypes and macros for 
//                the SSC module.
//
//----------------------------------------------------------------------------
// @Date          2013-7-5 07:00:09
//
//****************************************************************************

// USER CODE BEGIN (SSC_Header,1)

// USER CODE END



#ifndef _SSC_H_
#define _SSC_H_

//****************************************************************************
// @Project Includes
//****************************************************************************

// USER CODE BEGIN (SSC_Header,2)

// USER CODE END


//****************************************************************************
// @Macros
//****************************************************************************

// USER CODE BEGIN (SSC_Header,3)

// USER CODE END


//****************************************************************************
// @Defines
//****************************************************************************

// USER CODE BEGIN (SSC_Header,4)

// USER CODE END


//****************************************************************************
// @Typedefs
//****************************************************************************

// USER CODE BEGIN (SSC_Header,5)

// USER CODE END


//****************************************************************************
// @Imported Global Variables
//****************************************************************************

// USER CODE BEGIN (SSC_Header,6)

// USER CODE END


//****************************************************************************
// @Global Variables
//****************************************************************************

// USER CODE BEGIN (SSC_Header,7)

// USER CODE END


//****************************************************************************
// @Prototypes Of Global Functions
//****************************************************************************

void SSC_vInit(void);
void SSC_vSendData(ubyte Data);
ubyte SSC_vGetData(void);


// USER CODE BEGIN (SSC_Header,8)

// USER CODE END


//****************************************************************************
// @Interrupt Vectors
//****************************************************************************


// USER CODE BEGIN (SSC_Header,9)

// USER CODE END


#endif  // ifndef _SSC_H_

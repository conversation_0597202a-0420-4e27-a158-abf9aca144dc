<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>LX51 Static Call Analysis</title>
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<style type="text/css">
<!--
p            { font-family: Verdana; font-size: 8pt; margin-top: 2; margin-bottom: 2 }
.LI2         { margin-top: 2; margin-bottom: 2 }
.Release     { background-color: #CCCCCC; font-weight: bold; padding: 6 }
.ToolT       { font-size: 8pt; color: #808080 }
.TinyT       { font-size: 8pt; text-align: Center }
ul           { font-family: Verdana; font-size: 8pt; list-style-type: square;  margin-top: 2; margin-bottom: 2 }
ol           { font-family: Verdana; font-size: 8pt }
h1           { font-family: Verdana; font-size: 14pt; color: #000080; font-weight: bold; text-align: Center; margin-right: 3 }
h2           { font-family: Verdana; font-size: 8pt; color: #000080; font-weight: bold; margin-top: 12; margin-bottom: 3; background-color: #CCCCCC; padding: 6 }
h3           { font-family: Verdana; font-size: 8pt; font-weight: bold; margin-top: 12; margin-bottom: 3; background-color: #CCCCCC; padding: 6 }
pre          { font-family: Courier New; font-size: 8pt; background-color: #CCFFCC; margin-left: 24; margin-right: 24 }
li           { margin-top: 3; margin-bottom: 0 }
-->
</style>
</head>
<body>
<h1>Static Call Analysis for<br>.\output\CL2.0 (?C_STARTUP)</h1>

<p class="TinyT">
Created from: <b>LX51 LINKER/LOCATER V4.58</b><br>
Creation time: <b>07/13/2013  18:09:34</b><p>

<hr>


<h2><a name="S2"></a>?PR?MAIN?MAIN (C:0021DBH - C:0021FFH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=18 Bytes (<a href="#S46">?PR?CANERRORPROCESS?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S46">?PR?CANERRORPROCESS?USER</a></li>
<li><a href="#S52">?PR?CANRXTX?USER</a></li>
<li><a href="#S33">?PR?INITSW?SWDRIVER</a></li>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S11"></a>?PR?SHINT_VIXINTR5ISR?SHARED_INT (C:0037B6H - C:00380FH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=19 Bytes (<a href="#S45">?PR?RTCPROCESS?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S45">?PR?RTCPROCESS?USER</a></li>
</ul>

<h2><a name="S49"></a>?PR?CANTRANSMIT?USER (C:0034EDH - C:0034FEH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=12 Bytes (<a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S22">?PR?_CAN_VLOADDATA?CAN</a></li>
<li><a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
<li><a href="#S48">?PR?PASSWORD?USER</a></li>
</ul>

<h2><a name="S5"></a>?PR?MAIN_VINIT?MAIN (C:00251AH - C:00253BH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S13">?PR?CAN_VINIT?CAN</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S13">?PR?CAN_VINIT?CAN</a></li>
<li><a href="#S9">?PR?IO_VINIT?IO</a></li>
<li><a href="#S10">?PR?SHINT_VINIT?SHARED_INT</a></li>
<li><a href="#S6">?PR?SSC_VINIT?SSC</a></li>
<li><a href="#S24">?PR?T2_VINIT?T2</a></li>
<li><a href="#S25">?PR?WDT_VINIT?WDT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S6"></a>?PR?SSC_VINIT?SSC (C:0027FBH - C:00281EH)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S5">?PR?MAIN_VINIT?MAIN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S7"></a>?PR?_SSC_VSENDDATA?SSC (C:00281FH - C:002821H)</h2>
<p><br><b>Maximum Stack:</b> Top=14 Bytes (<a href="#S40">?PR?_MC33993DATASENDRCV?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S40">?PR?_MC33993DATASENDRCV?SWDRIVER</a></li>
</ul>

<h2><a name="S9"></a>?PR?IO_VINIT?IO (C:0027F4H - C:0027FAH)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S5">?PR?MAIN_VINIT?MAIN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S10"></a>?PR?SHINT_VINIT?SHARED_INT (C:0027F0H - C:0027F3H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S5">?PR?MAIN_VINIT?MAIN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S13"></a>?PR?CAN_VINIT?CAN (C:00253CH - C:00266EH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S122">?L?COM004B</a>) Top=10 Bytes (<a href="#S46">?PR?CANERRORPROCESS?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S77">?L?COM0004</a></li>
<li><a href="#S106">?L?COM003A</a></li>
<li><a href="#S122">?L?COM004B</a></li>
<li><a href="#S130">?L?COM0053</a></li>
<li><a href="#S131">?L?COM0054</a></li>
<li><a href="#S16">?PR?_CAN_VSETLISTCOMMAND?CAN</a></li>
<li><a href="#S14">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S46">?PR?CANERRORPROCESS?USER</a></li>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S14"></a>?PR?_CAN_VWRITEAMDATA?CAN (C:00266FH - C:00269CH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S71">?C?LIB_CODE</a>) Top=16 Bytes (<a href="#S16">?PR?_CAN_VSETLISTCOMMAND?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S77">?L?COM0004</a></li>
<li><a href="#S16">?PR?_CAN_VSETLISTCOMMAND?CAN</a></li>
<li><a href="#S13">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S16"></a>?PR?_CAN_VSETLISTCOMMAND?CAN (C:0026C3H - C:0026D2H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S14">?PR?_CAN_VWRITEAMDATA?CAN</a>) Top=14 Bytes (<a href="#S122">?L?COM004B</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S14">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S122">?L?COM004B</a></li>
<li><a href="#S13">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S17"></a>?PR?_CAN_VGETMSGOBJ?CAN (C:002E84H - C:003048H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S109">?L?COM003D</a>) Top=6 Bytes (<a href="#S142">?L?COM005F</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
<li><a href="#S76">?L?COM0001</a></li>
<li><a href="#S85">?L?COM0017</a></li>
<li><a href="#S107">?L?COM003B</a></li>
<li><a href="#S108">?L?COM003C</a></li>
<li><a href="#S109">?L?COM003D</a></li>
<li><a href="#S111">?L?COM0040</a></li>
<li><a href="#S116">?L?COM0045</a></li>
<li><a href="#S117">?L?COM0046</a></li>
<li><a href="#S123">?L?COM004C</a></li>
<li><a href="#S124">?L?COM004D</a></li>
<li><a href="#S125">?L?COM004E</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S142">?L?COM005F</a></li>
</ul>

<h2><a name="S19"></a>?PR?_CAN_UBREQUESTMSGOBJ?CAN (C:0030AFH - C:0030EEH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S85">?L?COM0017</a>) Top=6 Bytes (<a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S85">?L?COM0017</a></li>
<li><a href="#S129">?L?COM0052</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<h2><a name="S20"></a>?PR?_CAN_UBNEWDATA?CAN (C:00377BH - C:0037ADH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S85">?L?COM0017</a>) Top=4 Bytes (<a href="#S52">?PR?CANRXTX?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S85">?L?COM0017</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S52">?PR?CANRXTX?USER</a></li>
</ul>

<h2><a name="S21"></a>?PR?_CAN_VTRANSMIT?CAN (C:0034FFH - C:003531H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S85">?L?COM0017</a>) Top=6 Bytes (<a href="#S61">?PR?_CAN_SENDACK?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S85">?L?COM0017</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S61">?PR?_CAN_SENDACK?BOOT</a></li>
</ul>

<h2><a name="S22"></a>?PR?_CAN_VLOADDATA?CAN (C:003684H - C:0036F0H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S104">?L?COM0037</a>) Top=6 Bytes (<a href="#S61">?PR?_CAN_SENDACK?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S85">?L?COM0017</a></li>
<li><a href="#S104">?L?COM0037</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S61">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S49">?PR?CANTRANSMIT?USER</a></li>
</ul>

<h2><a name="S23"></a>?PR?_CAN_VRELEASEOBJ?CAN (C:003082H - C:0030AEH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S85">?L?COM0017</a>) Top=4 Bytes (<a href="#S52">?PR?CANRXTX?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S85">?L?COM0017</a></li>
<li><a href="#S129">?L?COM0052</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S52">?PR?CANRXTX?USER</a></li>
</ul>

<h2><a name="S24"></a>?PR?T2_VINIT?T2 (C:003810H - C:003821H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S5">?PR?MAIN_VINIT?MAIN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S25"></a>?PR?WDT_VINIT?WDT (C:0026F5H - C:0026F9H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S115">?L?COM0044</a>) Top=4 Bytes (<a href="#S5">?PR?MAIN_VINIT?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S115">?L?COM0044</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S26"></a>?PR?WDT_VDISABLE?WDT (C:003822H - C:00383CH)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S64">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S27"></a>?PR?_SWSTASAMPLE?SWDRIVER (C:002A46H - C:002AFAH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a>) Top=4 Bytes (<a href="#S54">?PR?SWSAMPLE?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
<li><a href="#S76">?L?COM0001</a></li>
<li><a href="#S87">?L?COM001C</a></li>
<li><a href="#S90">?L?COM0021</a></li>
<li><a href="#S91">?L?COM0024</a></li>
<li><a href="#S95">?L?COM002C</a></li>
<li><a href="#S107">?L?COM003B</a></li>
<li><a href="#S123">?L?COM004C</a></li>
<li><a href="#S145">?L?COM0062</a></li>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S29"></a>?PR?_LEDLIGHT?SWDRIVER (C:002B0FH - C:002DEDH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a>) Top=4 Bytes (<a href="#S50">?PR?LEDDR?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S76">?L?COM0001</a></li>
<li><a href="#S83">?L?COM0014</a></li>
<li><a href="#S87">?L?COM001C</a></li>
<li><a href="#S91">?L?COM0024</a></li>
<li><a href="#S98">?L?COM0030</a></li>
<li><a href="#S107">?L?COM003B</a></li>
<li><a href="#S128">?L?COM0051</a></li>
<li><a href="#S132">?L?COM0055</a></li>
<li><a href="#S133">?L?COM0056</a></li>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S50">?PR?LEDDR?USER</a></li>
</ul>

<h2><a name="S31"></a>?PR?INITMC33993SEC?SWDRIVER (C:0029A7H - C:002A22H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S93">?L?COM0029</a>) Top=6 Bytes (<a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S93">?L?COM0029</a></li>
<li><a href="#S94">?L?COM002B</a></li>
<li><a href="#S101">?L?COM0033</a></li>
<li><a href="#S102">?L?COM0034</a></li>
<li><a href="#S118">?L?COM0047</a></li>
<li><a href="#S119">?L?COM0048</a></li>
<li><a href="#S134">?L?COM0057</a></li>
<li><a href="#S135">?L?COM0058</a></li>
<li><a href="#S136">?L?COM0059</a></li>
<li><a href="#S137">?L?COM005A</a></li>
<li><a href="#S138">?L?COM005B</a></li>
<li><a href="#S139">?L?COM005C</a></li>
<li><a href="#S140">?L?COM005D</a></li>
<li><a href="#S141">?L?COM005E</a></li>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
</ul>

<h2><a name="S33"></a>?PR?INITSW?SWDRIVER (C:00295FH - C:0029A6H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S90">?L?COM0021</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S90">?L?COM0021</a></li>
<li><a href="#S95">?L?COM002C</a></li>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S35"></a>?PR?_MC33993CMDSENDONEBIT?SWDRIVER (C:00383DH - C:003A75H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=6 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>) Top=6 Bytes (<a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S86">?L?COM001A</a></li>
<li><a href="#S89">?L?COM001F</a></li>
<li><a href="#S97">?L?COM002F</a></li>
<li><a href="#S100">?L?COM0032</a></li>
<li><a href="#S112">?L?COM0041</a></li>
<li><a href="#S118">?L?COM0047</a></li>
<li><a href="#S119">?L?COM0048</a></li>
<li><a href="#S127">?L?COM0050</a></li>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
</ul>

<h2><a name="S38"></a>?PR?_CHANGECSMC33993?SWDRIVER (C:002E73H - C:002E83H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<h2><a name="S40"></a>?PR?_MC33993DATASENDRCV?SWDRIVER (C:002822H - C:00284FH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S7">?PR?_SSC_VSENDDATA?SSC</a>) Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S7">?PR?_SSC_VSENDDATA?SSC</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<h2><a name="S41"></a>?PR?_MC33993CMDSEND?SWDRIVER (C:002853H - C:00294EH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S40">?PR?_MC33993DATASENDRCV?SWDRIVER</a>) Top=10 Bytes (<a href="#S93">?L?COM0029</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S82">?L?COM0012</a></li>
<li><a href="#S118">?L?COM0047</a></li>
<li><a href="#S119">?L?COM0048</a></li>
<li><a href="#S134">?L?COM0057</a></li>
<li><a href="#S135">?L?COM0058</a></li>
<li><a href="#S136">?L?COM0059</a></li>
<li><a href="#S137">?L?COM005A</a></li>
<li><a href="#S138">?L?COM005B</a></li>
<li><a href="#S139">?L?COM005C</a></li>
<li><a href="#S140">?L?COM005D</a></li>
<li><a href="#S141">?L?COM005E</a></li>
<li><a href="#S38">?PR?_CHANGECSMC33993?SWDRIVER</a></li>
<li><a href="#S40">?PR?_MC33993DATASENDRCV?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S90">?L?COM0021</a></li>
<li><a href="#S93">?L?COM0029</a></li>
<li><a href="#S95">?L?COM002C</a></li>
<li><a href="#S101">?L?COM0033</a></li>
<li><a href="#S145">?L?COM0062</a></li>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
<li><a href="#S33">?PR?INITSW?SWDRIVER</a></li>
</ul>

<h2><a name="S45"></a>?PR?RTCPROCESS?USER (C:003AD4H - C:003B06H)</h2>
<p><br><b>Maximum Stack:</b> Top=19 Bytes (<a href="#S11">?PR?SHINT_VIXINTR5ISR?SHARED_INT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S11">?PR?SHINT_VIXINTR5ISR?SHARED_INT</a></li>
</ul>

<h2><a name="S46"></a>?PR?CANERRORPROCESS?USER (C:003B07H - C:003B82H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=16 Bytes (<a href="#S13">?PR?CAN_VINIT?CAN</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S13">?PR?CAN_VINIT?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S48"></a>?PR?PASSWORD?USER (C:0035A1H - C:003600H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S144">?L?COM0061</a>) Top=2 Bytes (<a href="#S49">?PR?CANTRANSMIT?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S84">?L?COM0015</a></li>
<li><a href="#S144">?L?COM0061</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S49">?PR?CANTRANSMIT?USER</a></li>
</ul>

<h2><a name="S50"></a>?PR?LEDDR?USER (C:003B83H - C:003D0CH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a>) Top=4 Bytes (<a href="#S52">?PR?CANRXTX?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S121">?L?COM004A</a></li>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S52">?PR?CANRXTX?USER</a></li>
</ul>

<h2><a name="S51"></a>?PR?CANTX?USER (C:003378H - C:0034ECH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S92">?L?COM0026</a>) Top=4 Bytes (<a href="#S54">?PR?SWSAMPLE?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S79">?L?COM000C</a></li>
<li><a href="#S92">?L?COM0026</a></li>
<li><a href="#S99">?L?COM0031</a></li>
<li><a href="#S105">?L?COM0038</a></li>
<li><a href="#S110">?L?COM003F</a></li>
<li><a href="#S113">?L?COM0042</a></li>
<li><a href="#S126">?L?COM004F</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S52">?PR?CANRXTX?USER</a></li>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S52"></a>?PR?CANRXTX?USER (C:003709H - C:00377AH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=14 Bytes (<a href="#S142">?L?COM005F</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S142">?L?COM005F</a></li>
<li><a href="#S20">?PR?_CAN_UBNEWDATA?CAN</a></li>
<li><a href="#S23">?PR?_CAN_VRELEASEOBJ?CAN</a></li>
<li><a href="#S51">?PR?CANTX?USER</a></li>
<li><a href="#S50">?PR?LEDDR?USER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S54"></a>?PR?SWSAMPLE?USER (C:003143H - C:00332EH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=12 Bytes (<a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
<li><a href="#S78">?L?COM0009</a></li>
<li><a href="#S80">?L?COM000E</a></li>
<li><a href="#S81">?L?COM0010</a></li>
<li><a href="#S88">?L?COM001E</a></li>
<li><a href="#S96">?L?COM002E</a></li>
<li><a href="#S103">?L?COM0036</a></li>
<li><a href="#S120">?L?COM0049</a></li>
<li><a href="#S143">?L?COM0060</a></li>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
<li><a href="#S51">?PR?CANTX?USER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S58"></a>?PR?CAN_SETWDTRESET?BOOT (C:002707H - C:00270FH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S115">?L?COM0044</a>) Top=4 Bytes (<a href="#S63">?PR?FLASH_WAIT?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S114">?L?COM0043</a></li>
<li><a href="#S115">?L?COM0044</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S63">?PR?FLASH_WAIT?BOOT</a></li>
</ul>

<h2><a name="S59"></a>?PR?_CAN_WAITTRANSMIT?BOOT (C:0030F8H - C:00312AH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S19">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a>) Top=4 Bytes (<a href="#S61">?PR?_CAN_SENDACK?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
<li><a href="#S78">?L?COM0009</a></li>
<li><a href="#S81">?L?COM0010</a></li>
<li><a href="#S19">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S61">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S49">?PR?CANTRANSMIT?USER</a></li>
</ul>

<h2><a name="S61"></a>?PR?_CAN_SENDACK?BOOT (C:003647H - C:003683H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=12 Bytes (<a href="#S22">?PR?_CAN_VLOADDATA?CAN</a>) Top=4 Bytes (<a href="#S64">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
<li><a href="#S22">?PR?_CAN_VLOADDATA?CAN</a></li>
<li><a href="#S21">?PR?_CAN_VTRANSMIT?CAN</a></li>
<li><a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S63"></a>?PR?FLASH_WAIT?BOOT (C:00271DH - C:002741H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S58">?PR?CAN_SETWDTRESET?BOOT</a>) Top=4 Bytes (<a href="#S64">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S58">?PR?CAN_SETWDTRESET?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S64"></a>?PR?BOOTMAIN?BOOT (C:002742H - C:0027EFH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=14 Bytes (<a href="#S142">?L?COM005F</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S142">?L?COM005F</a></li>
<li><a href="#S61">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S20">?PR?_CAN_UBNEWDATA?CAN</a></li>
<li><a href="#S23">?PR?_CAN_VRELEASEOBJ?CAN</a></li>
<li><a href="#S69">?PR?_DFLERASE?XC88X_FLHANDLER</a></li>
<li><a href="#S70">?PR?_FLPROG?XC88X_FLHANDLER</a></li>
<li><a href="#S58">?PR?CAN_SETWDTRESET?BOOT</a></li>
<li><a href="#S63">?PR?FLASH_WAIT?BOOT</a></li>
<li><a href="#S26">?PR?WDT_VDISABLE?WDT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S69"></a>?PR?_DFLERASE?XC88X_FLHANDLER (C:0024CDH - C:0024F9H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S64">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S70"></a>?PR?_FLPROG?XC88X_FLHANDLER (C:0024FAH - C:002519H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S64">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S71"></a>?C?LIB_CODE (C:002200H - C:00245FH)</h2>
<p><br><b>Maximum Stack:</b> Top=18 Bytes (<a href="#S14">?PR?_CAN_VWRITEAMDATA?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S80">?L?COM000E</a></li>
<li><a href="#S81">?L?COM0010</a></li>
<li><a href="#S83">?L?COM0014</a></li>
<li><a href="#S87">?L?COM001C</a></li>
<li><a href="#S91">?L?COM0024</a></li>
<li><a href="#S104">?L?COM0037</a></li>
<li><a href="#S108">?L?COM003C</a></li>
<li><a href="#S109">?L?COM003D</a></li>
<li><a href="#S116">?L?COM0045</a></li>
<li><a href="#S123">?L?COM004C</a></li>
<li><a href="#S125">?L?COM004E</a></li>
<li><a href="#S61">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
<li><a href="#S14">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
<li><a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S76"></a>?L?COM0001 (C:002AFBH - C:002B0EH)</h2>
<p><br><b>Maximum Stack:</b> Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
</ul>

<h2><a name="S77"></a>?L?COM0004 (C:00269DH - C:0026C2H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S14">?PR?_CAN_VWRITEAMDATA?CAN</a>) Top=12 Bytes (<a href="#S13">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S14">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S13">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S78"></a>?L?COM0009 (C:00332FH - C:003353H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S79"></a>?L?COM000C (C:003532H - C:003544H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S51">?PR?CANTX?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S51">?PR?CANTX?USER</a></li>
</ul>

<h2><a name="S80"></a>?L?COM000E (C:00335CH - C:003377H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S71">?C?LIB_CODE</a>) Top=4 Bytes (<a href="#S54">?PR?SWSAMPLE?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S81"></a>?L?COM0010 (C:00312BH - C:003142H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S59">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S82"></a>?L?COM0012 (C:00294FH - C:00295EH)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<h2><a name="S83"></a>?L?COM0014 (C:002E03H - C:002E12H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
</ul>

<h2><a name="S84"></a>?L?COM0015 (C:003601H - C:003619H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S48">?PR?PASSWORD?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S48">?PR?PASSWORD?USER</a></li>
</ul>

<h2><a name="S85"></a>?L?COM0017 (C:003049H - C:003059H)</h2>
<p><br><b>Maximum Stack:</b> Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S20">?PR?_CAN_UBNEWDATA?CAN</a></li>
<li><a href="#S19">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a></li>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
<li><a href="#S22">?PR?_CAN_VLOADDATA?CAN</a></li>
<li><a href="#S23">?PR?_CAN_VRELEASEOBJ?CAN</a></li>
<li><a href="#S21">?PR?_CAN_VTRANSMIT?CAN</a></li>
</ul>

<h2><a name="S86"></a>?L?COM001A (C:003A76H - C:003A90H)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
</ul>

<h2><a name="S87"></a>?L?COM001C (C:002DEEH - C:002DF9H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
</ul>

<h2><a name="S88"></a>?L?COM001E (C:003566H - C:003578H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S54">?PR?SWSAMPLE?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S89"></a>?L?COM001F (C:003AA0H - C:003AABH)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
</ul>

<h2><a name="S90"></a>?L?COM0021 (C:002A2FH - C:002A3CH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=6 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>) Top=6 Bytes (<a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
<li><a href="#S33">?PR?INITSW?SWDRIVER</a></li>
</ul>

<h2><a name="S91"></a>?L?COM0024 (C:002E13H - C:002E1EH)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
</ul>

<h2><a name="S92"></a>?L?COM0026 (C:003545H - C:00355BH)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S51">?PR?CANTX?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S51">?PR?CANTX?USER</a></li>
</ul>

<h2><a name="S93"></a>?L?COM0029 (C:002A23H - C:002A2EH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=6 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>) Top=8 Bytes (<a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S94"></a>?L?COM002B (C:002E39H - C:002E49H)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S95"></a>?L?COM002C (C:002E1FH - C:002E2CH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=6 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>) Top=6 Bytes (<a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
<li><a href="#S33">?PR?INITSW?SWDRIVER</a></li>
</ul>

<h2><a name="S96"></a>?L?COM002E (C:003354H - C:00335BH)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S54">?PR?SWSAMPLE?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S97"></a>?L?COM002F (C:003A98H - C:003A9FH)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
</ul>

<h2><a name="S98"></a>?L?COM0030 (C:002DFAH - C:002E02H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
</ul>

<h2><a name="S99"></a>?L?COM0031 (C:00358BH - C:003599H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S51">?PR?CANTX?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S51">?PR?CANTX?USER</a></li>
</ul>

<h2><a name="S100"></a>?L?COM0032 (C:003A91H - C:003A97H)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
</ul>

<h2><a name="S101"></a>?L?COM0033 (C:002E2DH - C:002E38H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=6 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>) Top=8 Bytes (<a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S102"></a>?L?COM0034 (C:002A3DH - C:002A45H)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S103"></a>?L?COM0036 (C:00355CH - C:003565H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S54">?PR?SWSAMPLE?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S104"></a>?L?COM0037 (C:0036F1H - C:003708H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S71">?C?LIB_CODE</a>) Top=14 Bytes (<a href="#S22">?PR?_CAN_VLOADDATA?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S22">?PR?_CAN_VLOADDATA?CAN</a></li>
</ul>

<h2><a name="S105"></a>?L?COM0038 (C:003580H - C:00358AH)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S51">?PR?CANTX?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S51">?PR?CANTX?USER</a></li>
</ul>

<h2><a name="S106"></a>?L?COM003A (C:0026D3H - C:0026DBH)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S13">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S13">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S107"></a>?L?COM003B (C:002E51H - C:002E59H)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
</ul>

<h2><a name="S108"></a>?L?COM003C (C:003070H - C:003081H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S71">?C?LIB_CODE</a>) Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
</ul>

<h2><a name="S109"></a>?L?COM003D (C:003D15H - C:003D1EH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S71">?C?LIB_CODE</a>) Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
</ul>

<h2><a name="S110"></a>?L?COM003F (C:003636H - C:003646H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S51">?PR?CANTX?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S51">?PR?CANTX?USER</a></li>
</ul>

<h2><a name="S111"></a>?L?COM0040 (C:00305AH - C:003062H)</h2>
<p><br><b>Maximum Stack:</b> Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
</ul>

<h2><a name="S112"></a>?L?COM0041 (C:003AB6H - C:003AC3H)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
</ul>

<h2><a name="S113"></a>?L?COM0042 (C:003579H - C:00357FH)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S51">?PR?CANTX?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S51">?PR?CANTX?USER</a></li>
</ul>

<h2><a name="S114"></a>?L?COM0043 (C:0026FAH - C:002706H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S58">?PR?CAN_SETWDTRESET?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S58">?PR?CAN_SETWDTRESET?BOOT</a></li>
</ul>

<h2><a name="S115"></a>?L?COM0044 (C:002710H - C:00271CH)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S25">?PR?WDT_VINIT?WDT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S58">?PR?CAN_SETWDTRESET?BOOT</a></li>
<li><a href="#S25">?PR?WDT_VINIT?WDT</a></li>
</ul>

<h2><a name="S116"></a>?L?COM0045 (C:003063H - C:00306FH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S71">?C?LIB_CODE</a>) Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
</ul>

<h2><a name="S117"></a>?L?COM0046 (C:003D1FH - C:003D2BH)</h2>
<p><br><b>Maximum Stack:</b> Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
</ul>

<h2><a name="S118"></a>?L?COM0047 (C:003AC4H - C:003ACBH)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S119"></a>?L?COM0048 (C:003ACCH - C:003AD3H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S120"></a>?L?COM0049 (C:00362AH - C:003635H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S54">?PR?SWSAMPLE?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S121"></a>?L?COM004A (C:003D0DH - C:003D14H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S50">?PR?LEDDR?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S50">?PR?LEDDR?USER</a></li>
</ul>

<h2><a name="S122"></a>?L?COM004B (C:0026DCH - C:0026E2H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=6 Bytes (<a href="#S16">?PR?_CAN_VSETLISTCOMMAND?CAN</a>) Top=12 Bytes (<a href="#S13">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_VSETLISTCOMMAND?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S13">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S123"></a>?L?COM004C (C:003D2CH - C:003D35H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S71">?C?LIB_CODE</a>) Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
</ul>

<h2><a name="S124"></a>?L?COM004D (C:003D36H - C:003D40H)</h2>
<p><br><b>Maximum Stack:</b> Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
</ul>

<h2><a name="S125"></a>?L?COM004E (C:003D41H - C:003D49H)</h2>
<p><br><b>Maximum Stack:</b> Top=14 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S71">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
</ul>

<h2><a name="S126"></a>?L?COM004F (C:00359AH - C:0035A0H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S51">?PR?CANTX?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S51">?PR?CANTX?USER</a></li>
</ul>

<h2><a name="S127"></a>?L?COM0050 (C:003AACH - C:003AB5H)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S35">?PR?_MC33993CMDSENDONEBIT?SWDRIVER</a></li>
</ul>

<h2><a name="S128"></a>?L?COM0051 (C:002E6AH - C:002E72H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
</ul>

<h2><a name="S129"></a>?L?COM0052 (C:0030EFH - C:0030F7H)</h2>
<p><br><b>Maximum Stack:</b> Top=14 Bytes (<a href="#S19">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S19">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a></li>
<li><a href="#S23">?PR?_CAN_VRELEASEOBJ?CAN</a></li>
</ul>

<h2><a name="S130"></a>?L?COM0053 (C:0026E3H - C:0026EBH)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S13">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S13">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S131"></a>?L?COM0054 (C:0026ECH - C:0026F4H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S13">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S13">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S132"></a>?L?COM0055 (C:002E5AH - C:002E61H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
</ul>

<h2><a name="S133"></a>?L?COM0056 (C:002E62H - C:002E69H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S29">?PR?_LEDLIGHT?SWDRIVER</a></li>
</ul>

<h2><a name="S134"></a>?L?COM0057 (C:003D4AH - C:003D51H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S135"></a>?L?COM0058 (C:003D52H - C:003D59H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S136"></a>?L?COM0059 (C:003D5AH - C:003D61H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S137"></a>?L?COM005A (C:003D62H - C:003D69H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S138"></a>?L?COM005B (C:003D6AH - C:003D71H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S139"></a>?L?COM005C (C:003D72H - C:003D79H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S140"></a>?L?COM005D (C:003D7AH - C:003D81H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S141"></a>?L?COM005E (C:003D82H - C:003D89H)</h2>
<p><br><b>Maximum Stack:</b> Top=12 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
<li><a href="#S31">?PR?INITMC33993SEC?SWDRIVER</a></li>
</ul>

<h2><a name="S142"></a>?L?COM005F (C:0037AEH - C:0037B5H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=12 Bytes (<a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a>) Top=4 Bytes (<a href="#S52">?PR?CANRXTX?USER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S17">?PR?_CAN_VGETMSGOBJ?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S64">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S52">?PR?CANRXTX?USER</a></li>
</ul>

<h2><a name="S143"></a>?L?COM0060 (C:00361AH - C:003621H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S54">?PR?SWSAMPLE?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S54">?PR?SWSAMPLE?USER</a></li>
</ul>

<h2><a name="S144"></a>?L?COM0061 (C:003622H - C:003629H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S48">?PR?PASSWORD?USER</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S48">?PR?PASSWORD?USER</a></li>
</ul>

<h2><a name="S145"></a>?L?COM0062 (C:002E4AH - C:002E50H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=6 Bytes (<a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a>) Top=6 Bytes (<a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S41">?PR?_MC33993CMDSEND?SWDRIVER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S27">?PR?_SWSTASAMPLE?SWDRIVER</a></li>
</ul>

</body>
</html>

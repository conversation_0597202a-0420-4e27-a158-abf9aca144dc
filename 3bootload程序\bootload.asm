;****************************************************************************
; Filename      bootload.asm
; Project       bootload.dav
;----------------------------------------------------------------------------
; Description   This file contains the assembler formatted information
;                about the actual project values. It will be used by your
;                programming environment.
;
;                PLEASE DO NOT MODIFY THIS FILE !
;
;----------------------------------------------------------------------------
; Date          2013-7-7 10:40:52
;
;****************************************************************************


; RMAP=x -CPU Accumulator Register
ACC_ACC0	SET	0
ACC_ACC1	SET	0
ACC_ACC2	SET	0
ACC_ACC3	SET	0
ACC_ACC4	SET	0
ACC_ACC5	SET	0
ACC_ACC6	SET	0
ACC_ACC7	SET	0

; RMAP=0 PAGE=1-ADC Channel 0 Control Register
ADC_CHCTR0_LCC	SET	0
ADC_CHCTR0_RESRSEL	SET	0

; RMAP=0 PAGE=1-ADC Channel 1 Control Register
ADC_CHCTR1_LCC	SET	0
ADC_CHCTR1_RESRSEL	SET	0

; RMAP=0 PAGE=1-ADC Channel 2 Control Register
ADC_CHCTR2_LCC	SET	0
ADC_CHCTR2_RESRSEL	SET	0

; RMAP=0 PAGE=1-ADC Channel 3 Control Register
ADC_CHCTR3_LCC	SET	0
ADC_CHCTR3_RESRSEL	SET	0

; RMAP=0 PAGE=1-ADC Channel 4 Control Register
ADC_CHCTR4_LCC	SET	0
ADC_CHCTR4_RESRSEL	SET	0

; RMAP=0 PAGE=1-ADC Channel 5 Control Register
ADC_CHCTR5_LCC	SET	0
ADC_CHCTR5_RESRSEL	SET	0

; RMAP=0 PAGE=1-ADC Channel 6 Control Register
ADC_CHCTR6_LCC	SET	0
ADC_CHCTR6_RESRSEL	SET	0

; RMAP=0 PAGE=1-ADC Channel 7 Control Register
ADC_CHCTR7_LCC	SET	0
ADC_CHCTR7_RESRSEL	SET	0

; RMAP=0 PAGE=5-ADC Channel Interrupt Clear Register
ADC_CHINCR_CHINC0	SET	0
ADC_CHINCR_CHINC1	SET	0
ADC_CHINCR_CHINC2	SET	0
ADC_CHINCR_CHINC3	SET	0
ADC_CHINCR_CHINC4	SET	0
ADC_CHINCR_CHINC5	SET	0
ADC_CHINCR_CHINC6	SET	0
ADC_CHINCR_CHINC7	SET	0

; RMAP=0 PAGE=5-ADC Channel Interrupt Flag Register
ADC_CHINFR_CHINF0	SET	0
ADC_CHINFR_CHINF1	SET	0
ADC_CHINFR_CHINF2	SET	0
ADC_CHINFR_CHINF3	SET	0
ADC_CHINFR_CHINF4	SET	0
ADC_CHINFR_CHINF5	SET	0
ADC_CHINFR_CHINF6	SET	0
ADC_CHINFR_CHINF7	SET	0

; RMAP=0 PAGE=5-ADC Channel Interrupt Node Pointer Register
ADC_CHINPR_CHINP0	SET	0
ADC_CHINPR_CHINP1	SET	0
ADC_CHINPR_CHINP2	SET	0
ADC_CHINPR_CHINP3	SET	0
ADC_CHINPR_CHINP4	SET	0
ADC_CHINPR_CHINP5	SET	0
ADC_CHINPR_CHINP6	SET	0
ADC_CHINPR_CHINP7	SET	0

; RMAP=0 PAGE=5-ADC Channel Interrupt Set Register
ADC_CHINSR_CHINS0	SET	0
ADC_CHINSR_CHINS1	SET	0
ADC_CHINSR_CHINS2	SET	0
ADC_CHINSR_CHINS3	SET	0
ADC_CHINSR_CHINS4	SET	0
ADC_CHINSR_CHINS5	SET	0
ADC_CHINSR_CHINS6	SET	0
ADC_CHINSR_CHINS7	SET	0

; RMAP=0 PAGE=6-ADC Source 1 Conversion Request Control Register
ADC_CRCR1_CH4	SET	0
ADC_CRCR1_CH5	SET	0
ADC_CRCR1_CH6	SET	0
ADC_CRCR1_CH7	SET	0

; RMAP=0 PAGE=6-ADC Source 1 Conversion Request Mode Register
ADC_CRMR1_0	SET	0
ADC_CRMR1_CLRPND	SET	0
ADC_CRMR1_ENGT	SET	0
ADC_CRMR1_ENSI	SET	0
ADC_CRMR1_ENTR	SET	0
ADC_CRMR1_LDEV	SET	0
ADC_CRMR1_RSV	SET	0
ADC_CRMR1_SCAN	SET	0

; RMAP=0 PAGE=6-ADC Source 1 Conversion Request Pending Register
ADC_CRPR1_CHP4	SET	0
ADC_CRPR1_CHP5	SET	0
ADC_CRPR1_CHP6	SET	0
ADC_CRPR1_CHP7	SET	0

; RMAP=0 PAGE=0-ADC External Trigger Control Register
ADC_ETRCR_ETRSEL0	SET	0
ADC_ETRCR_ETRSEL1	SET	0
ADC_ETRCR_SYNEN0	SET	0
ADC_ETRCR_SYNEN1	SET	0

; RMAP=0 PAGE=5-ADC Event Interrupt Clear Register
ADC_EVINCR_EVINC0	SET	0
ADC_EVINCR_EVINC1	SET	0
ADC_EVINCR_EVINC4	SET	0
ADC_EVINCR_EVINC5	SET	0
ADC_EVINCR_EVINC6	SET	0
ADC_EVINCR_EVINC7	SET	0

; RMAP=0 PAGE=5-ADC Event Interrupt Flag Register
ADC_EVINFR_EVINF0	SET	0
ADC_EVINFR_EVINF1	SET	0
ADC_EVINFR_EVINF4	SET	0
ADC_EVINFR_EVINF5	SET	0
ADC_EVINFR_EVINF6	SET	0
ADC_EVINFR_EVINF7	SET	0

; RMAP=0 PAGE=5-ADC Event Interrupt Node Pointer Register
ADC_EVINPR_EVINP0	SET	0
ADC_EVINPR_EVINP1	SET	0
ADC_EVINPR_EVINP4	SET	0
ADC_EVINPR_EVINP5	SET	0
ADC_EVINPR_EVINP6	SET	0
ADC_EVINPR_EVINP7	SET	0

; RMAP=0 PAGE=5-ADC Event Interrupt Set Flag Register
ADC_EVINSR_EVINS0	SET	0
ADC_EVINSR_EVINS1	SET	0
ADC_EVINSR_EVINS4	SET	0
ADC_EVINSR_EVINS5	SET	0
ADC_EVINSR_EVINS6	SET	0
ADC_EVINSR_EVINS7	SET	0

; RMAP=0 PAGE=0-ADC Global Control Register
ADC_GLOBCTR_ANON	SET	0
ADC_GLOBCTR_CTC	SET	3
ADC_GLOBCTR_DW	SET	0

; RMAP=0 PAGE=0-ADC Global Status Register
ADC_GLOBSTR_BUSY	SET	0
ADC_GLOBSTR_CHNR	SET	0
ADC_GLOBSTR_SAMPLE	SET	0

; RMAP=0 PAGE=0-ADC Input Class 0 Register
ADC_INPCR0_STC	SET	0

; RMAP=0 PAGE=0-ADC Limit Check Boundary Register
ADC_LCBR_BOUND0	SET	7
ADC_LCBR_BOUND1	SET	11

; RMAP=0 -ADC Page Register
ADC_PAGE_OP	SET	0
ADC_PAGE_PAGE	SET	0
ADC_PAGE_STNR	SET	0

; RMAP=0 PAGE=0-ADC Priority and Arbitration Register
ADC_PRAR_ARBM	SET	0
ADC_PRAR_ASEN0	SET	0
ADC_PRAR_ASEN1	SET	0
ADC_PRAR_CSM0	SET	0
ADC_PRAR_CSM1	SET	0
ADC_PRAR_PRIO0	SET	0
ADC_PRAR_PRIO1	SET	0

; RMAP=0 PAGE=6-ADC Source 0 Queue 0 Register
ADC_Q0R0_ENSI	SET	0
ADC_Q0R0_EXTR	SET	0
ADC_Q0R0_REQCHNR	SET	0
ADC_Q0R0_RF	SET	0
ADC_Q0R0_V	SET	0

; RMAP=0 PAGE=6-ADC Source 0 Queue Backup Register
ADC_QBUR0_ENSI	SET	0
ADC_QBUR0_EXTR	SET	0
ADC_QBUR0_REQCHNR	SET	0
ADC_QBUR0_RF	SET	0
ADC_QBUR0_V	SET	0

; RMAP=0 PAGE=6-ADC Source 0 Queue Input Register
ADC_QINR0_ENSI	SET	0
ADC_QINR0_EXTR	SET	0
ADC_QINR0_REQCHNR	SET	0
ADC_QINR0_RF	SET	0

; RMAP=0 PAGE=6-ADC Source 0 Queue Mode Register
ADC_QMR0_0	SET	0
ADC_QMR0_CEV	SET	0
ADC_QMR0_CLRV	SET	0
ADC_QMR0_ENGT	SET	0
ADC_QMR0_ENTR	SET	0
ADC_QMR0_FLUSH	SET	0
ADC_QMR0_TREV	SET	0

; RMAP=0 PAGE=6-ADC Source 0 Queue Status Register
ADC_QSR0_EMPTY	SET	0
ADC_QSR0_EV	SET	0
ADC_QSR0_FILL	SET	0
ADC_QSR0_RSV	SET	0

; RMAP=0 PAGE=4-ADC Result 0 Control Register
ADC_RCR0_DRCTR	SET	0
ADC_RCR0_IEN	SET	0
ADC_RCR0_VFCTR	SET	0
ADC_RCR0_WFR	SET	0

; RMAP=0 PAGE=4-ADC Result 1 Control Register
ADC_RCR1_DRCTR	SET	0
ADC_RCR1_IEN	SET	0
ADC_RCR1_VFCTR	SET	0
ADC_RCR1_WFR	SET	0

; RMAP=0 PAGE=4-ADC Result 2 Control Register
ADC_RCR2_DRCTR	SET	0
ADC_RCR2_IEN	SET	0
ADC_RCR2_VFCTR	SET	0
ADC_RCR2_WFR	SET	0

; RMAP=0 PAGE=4-ADC Result 3 Control Register
ADC_RCR3_DRCTR	SET	0
ADC_RCR3_IEN	SET	0
ADC_RCR3_VFCTR	SET	0
ADC_RCR3_WFR	SET	0

; RMAP=0 PAGE=2-ADC Result 0 Register High
ADC_RESR0H_RESULT	SET	0

; RMAP=0 PAGE=2-ADC Result 0 Register Low
ADC_RESR0L_CHNR	SET	0
ADC_RESR0L_DRC	SET	0
ADC_RESR0L_RESULT	SET	0
ADC_RESR0L_VF	SET	0

; RMAP=0 PAGE=2-ADC Result 1 Register High
ADC_RESR1H_RESULT	SET	0

; RMAP=0 PAGE=2-ADC Result 1 Register Low
ADC_RESR1L_CHNR	SET	0
ADC_RESR1L_DRC	SET	0
ADC_RESR1L_RESULT	SET	0
ADC_RESR1L_VF	SET	0

; RMAP=0 PAGE=2-ADC Result 2 Register High
ADC_RESR2H_RESULT	SET	0

; RMAP=0 PAGE=2-ADC Result 2 Register Low
ADC_RESR2L_CHNR	SET	0
ADC_RESR2L_DRC	SET	0
ADC_RESR2L_RESULT	SET	0
ADC_RESR2L_VF	SET	0

; RMAP=0 PAGE=2-ADC Result 3 Register High
ADC_RESR3H_RESULT	SET	0

; RMAP=0 PAGE=2-ADC Result 3 Register Low
ADC_RESR3L_CHNR	SET	0
ADC_RESR3L_DRC	SET	0
ADC_RESR3L_RESULT	SET	0
ADC_RESR3L_VF	SET	0

; RMAP=0 PAGE=3-ADC Result 0 View A Register High
ADC_RESRA0H_RESULT	SET	0

; RMAP=0 PAGE=3-ADC Result 0 View A Register Low
ADC_RESRA0L_CHNR	SET	0
ADC_RESRA0L_DRC	SET	0
ADC_RESRA0L_RESULT	SET	0
ADC_RESRA0L_VF	SET	0

; RMAP=0 PAGE=3-ADC Result 1 View A Register High
ADC_RESRA1H_RESULT	SET	0

; RMAP=0 PAGE=3-ADC Result 1 View A Register Low
ADC_RESRA1L_CHNR	SET	0
ADC_RESRA1L_DRC	SET	0
ADC_RESRA1L_RESULT	SET	0
ADC_RESRA1L_VF	SET	0

; RMAP=0 PAGE=3-ADC Result 2 View A Register High
ADC_RESRA2H_RESULT	SET	0

; RMAP=0 PAGE=3-ADC Result 2 View A Register Low
ADC_RESRA2L_CHNR	SET	0
ADC_RESRA2L_DRC	SET	0
ADC_RESRA2L_RESULT	SET	0
ADC_RESRA2L_VF	SET	0

; RMAP=0 PAGE=3-ADC Result 3 View A Register High
ADC_RESRA3H_RESULT	SET	0

; RMAP=0 PAGE=3-ADC Result 3 View A Register Low
ADC_RESRA3L_CHNR	SET	0
ADC_RESRA3L_DRC	SET	0
ADC_RESRA3L_RESULT	SET	0
ADC_RESRA3L_VF	SET	0

; RMAP=0 PAGE=4-ADC Valid Flag Clear Register
ADC_VFCR_VFC0	SET	0
ADC_VFCR_VFC1	SET	0
ADC_VFCR_VFC2	SET	0
ADC_VFCR_VFC3	SET	0

; RMAP=0 PAGE=0-SCU Baud Rate Control Register
BCON_BGSEL	SET	0
BCON_BRDIS	SET	0
BCON_BRPRE	SET	0
BCON_R	SET	0

; RMAP=0 PAGE=0-SCU Baud Rate Timer/Reload Register
BG_BR_VALUE	SET	255

; RMAP=x -CPU B Register
B_B0	SET	0
B_B1	SET	0
B_B2	SET	0
B_B3	SET	0
B_B4	SET	0
B_B5	SET	0
B_B6	SET	0
B_B7	SET	0

; RMAP=0 -MultiCAN Serial Channel Control Register
CAN_ADCON_AUAD	SET	0
CAN_ADCON_BSY	SET	0
CAN_ADCON_RWEN	SET	0
CAN_ADCON_V0	SET	0
CAN_ADCON_V1	SET	0
CAN_ADCON_V2	SET	0
CAN_ADCON_V3	SET	0

; RMAP=0 -MultiCAN Baud Rate Control Register
CAN_ADH_CA10	SET	0
CAN_ADH_CA11	SET	0
CAN_ADH_CA12	SET	0
CAN_ADH_CA13	SET	0

; RMAP=0 -MultiCAN Serial Data Buffer Register
CAN_ADL_CA2	SET	0
CAN_ADL_CA3	SET	0
CAN_ADL_CA4	SET	0
CAN_ADL_CA5	SET	0
CAN_ADL_CA6	SET	0
CAN_ADL_CA7	SET	0
CAN_ADL_CA8	SET	0
CAN_ADL_CA9	SET	0

; RMAP=0 -MultiCAN CAN Data Register 0
CAN_DATA0_CD[7:0]	SET	0

; RMAP=0 -MultiCAN CAN Data Register 1
CAN_DATA1_CD[15:8]	SET	0

; RMAP=0 -MultiCAN CAN Data Register 2
CAN_DATA2_CD	SET	0

; RMAP=0 -MultiCAN CAN Data Register 3
CAN_DATA3_CD	SET	0

; List Register 0
CAN_LIST0_0	SET	0
CAN_LIST0_BEGIN	SET	0
CAN_LIST0_EMPTY	SET	0
CAN_LIST0_END	SET	0
CAN_LIST0_SIZE	SET	0

; List Register 1
CAN_LIST1_0	SET	0
CAN_LIST1_BEGIN	SET	0
CAN_LIST1_EMPTY	SET	0
CAN_LIST1_END	SET	0
CAN_LIST1_SIZE	SET	0

; List Register 2
CAN_LIST2_0	SET	0
CAN_LIST2_BEGIN	SET	0
CAN_LIST2_EMPTY	SET	0
CAN_LIST2_END	SET	0
CAN_LIST2_SIZE	SET	0

; List Register 3
CAN_LIST3_0	SET	0
CAN_LIST3_BEGIN	SET	0
CAN_LIST3_EMPTY	SET	0
CAN_LIST3_END	SET	0
CAN_LIST3_SIZE	SET	0

; List Register 4
CAN_LIST4_0	SET	0
CAN_LIST4_BEGIN	SET	0
CAN_LIST4_EMPTY	SET	0
CAN_LIST4_END	SET	0
CAN_LIST4_SIZE	SET	0

; List Register 5
CAN_LIST5_0	SET	0
CAN_LIST5_BEGIN	SET	0
CAN_LIST5_EMPTY	SET	0
CAN_LIST5_END	SET	0
CAN_LIST5_SIZE	SET	0

; List Register 6
CAN_LIST6_0	SET	0
CAN_LIST6_BEGIN	SET	0
CAN_LIST6_EMPTY	SET	0
CAN_LIST6_END	SET	0
CAN_LIST6_SIZE	SET	0

; List Register 7
CAN_LIST7_0	SET	0
CAN_LIST7_BEGIN	SET	0
CAN_LIST7_EMPTY	SET	0
CAN_LIST7_END	SET	0
CAN_LIST7_SIZE	SET	0

; MultiCAN Module Control Register
CAN_MCR_0	SET	0
CAN_MCR_0	SET	0
CAN_MCR_MPSEL	SET	0

; Module Interrupt Trigger Register
CAN_MITR_IT[31:8]	SET	0
CAN_MITR_IT[7:0]	SET	0

; CAN Message Object 0 Acceptance Mask Register
CAN_MOAMR0_0	SET	0
CAN_MOAMR0_AM	SET	536870911
CAN_MOAMR0_MIDE	SET	1

; CAN Message Object 10 Acceptance Mask Register
CAN_MOAMR10_0	SET	0
CAN_MOAMR10_AM	SET	536870911
CAN_MOAMR10_MIDE	SET	1

; CAN Message Object 11 Acceptance Mask Register
CAN_MOAMR11_0	SET	0
CAN_MOAMR11_AM	SET	536870911
CAN_MOAMR11_MIDE	SET	1

; CAN Message Object 12 Acceptance Mask Register
CAN_MOAMR12_0	SET	0
CAN_MOAMR12_AM	SET	536870911
CAN_MOAMR12_MIDE	SET	1

; CAN Message Object 13 Acceptance Mask Register
CAN_MOAMR13_0	SET	0
CAN_MOAMR13_AM	SET	536870911
CAN_MOAMR13_MIDE	SET	1

; CAN Message Object 14 Acceptance Mask Register
CAN_MOAMR14_0	SET	0
CAN_MOAMR14_AM	SET	536870911
CAN_MOAMR14_MIDE	SET	1

; CAN Message Object 15 Acceptance Mask Register
CAN_MOAMR15_0	SET	0
CAN_MOAMR15_AM	SET	536870911
CAN_MOAMR15_MIDE	SET	1

; CAN Message Object 16 Acceptance Mask Register
CAN_MOAMR16_0	SET	0
CAN_MOAMR16_AM	SET	536870911
CAN_MOAMR16_MIDE	SET	1

; CAN Message Object 17 Acceptance Mask Register
CAN_MOAMR17_0	SET	0
CAN_MOAMR17_AM	SET	536870911
CAN_MOAMR17_MIDE	SET	1

; CAN Message Object 18 Acceptance Mask Register
CAN_MOAMR18_0	SET	0
CAN_MOAMR18_AM	SET	536870911
CAN_MOAMR18_MIDE	SET	1

; CAN Message Object 19 Acceptance Mask Register
CAN_MOAMR19_0	SET	0
CAN_MOAMR19_AM	SET	536870911
CAN_MOAMR19_MIDE	SET	1

; CAN Message Object 1 Acceptance Mask Register
CAN_MOAMR1_0	SET	0
CAN_MOAMR1_AM	SET	536870911
CAN_MOAMR1_MIDE	SET	1

; CAN Message Object 20 Acceptance Mask Register
CAN_MOAMR20_0	SET	0
CAN_MOAMR20_AM	SET	536870911
CAN_MOAMR20_MIDE	SET	1

; CAN Message Object 21 Acceptance Mask Register
CAN_MOAMR21_0	SET	0
CAN_MOAMR21_AM	SET	536870911
CAN_MOAMR21_MIDE	SET	1

; CAN Message Object 22 Acceptance Mask Register
CAN_MOAMR22_0	SET	0
CAN_MOAMR22_AM	SET	536870911
CAN_MOAMR22_MIDE	SET	1

; CAN Message Object 23 Acceptance Mask Register
CAN_MOAMR23_0	SET	0
CAN_MOAMR23_AM	SET	536870911
CAN_MOAMR23_MIDE	SET	1

; CAN Message Object 24 Acceptance Mask Register
CAN_MOAMR24_0	SET	0
CAN_MOAMR24_AM	SET	536870911
CAN_MOAMR24_MIDE	SET	1

; CAN Message Object 25 Acceptance Mask Register
CAN_MOAMR25_0	SET	0
CAN_MOAMR25_AM	SET	536870911
CAN_MOAMR25_MIDE	SET	1

; CAN Message Object 26 Acceptance Mask Register
CAN_MOAMR26_0	SET	0
CAN_MOAMR26_AM	SET	536870911
CAN_MOAMR26_MIDE	SET	1

; CAN Message Object 27 Acceptance Mask Register
CAN_MOAMR27_0	SET	0
CAN_MOAMR27_AM	SET	536870911
CAN_MOAMR27_MIDE	SET	1

; CAN Message Object 28 Acceptance Mask Register
CAN_MOAMR28_0	SET	0
CAN_MOAMR28_AM	SET	536870911
CAN_MOAMR28_MIDE	SET	1

; CAN Message Object 29 Acceptance Mask Register
CAN_MOAMR29_0	SET	0
CAN_MOAMR29_AM	SET	536870911
CAN_MOAMR29_MIDE	SET	1

; CAN Message Object 2 Acceptance Mask Register
CAN_MOAMR2_0	SET	0
CAN_MOAMR2_AM	SET	536870911
CAN_MOAMR2_MIDE	SET	1

; CAN Message Object 30 Acceptance Mask Register
CAN_MOAMR30_0	SET	0
CAN_MOAMR30_AM	SET	536870911
CAN_MOAMR30_MIDE	SET	1

; CAN Message Object 31 Acceptance Mask Register
CAN_MOAMR31_0	SET	0
CAN_MOAMR31_AM	SET	536870911
CAN_MOAMR31_MIDE	SET	1

; CAN Message Object 3 Acceptance Mask Register
CAN_MOAMR3_0	SET	0
CAN_MOAMR3_AM	SET	536870911
CAN_MOAMR3_MIDE	SET	1

; CAN Message Object 4 Acceptance Mask Register
CAN_MOAMR4_0	SET	0
CAN_MOAMR4_AM	SET	536870911
CAN_MOAMR4_MIDE	SET	1

; CAN Message Object 5 Acceptance Mask Register
CAN_MOAMR5_0	SET	0
CAN_MOAMR5_AM	SET	536870911
CAN_MOAMR5_MIDE	SET	1

; CAN Message Object 6 Acceptance Mask Register
CAN_MOAMR6_0	SET	0
CAN_MOAMR6_AM	SET	536870911
CAN_MOAMR6_MIDE	SET	1

; CAN Message Object 7 Acceptance Mask Register
CAN_MOAMR7_0	SET	0
CAN_MOAMR7_AM	SET	536870911
CAN_MOAMR7_MIDE	SET	1

; CAN Message Object 8 Acceptance Mask Register
CAN_MOAMR8_0	SET	0
CAN_MOAMR8_AM	SET	536870911
CAN_MOAMR8_MIDE	SET	1

; CAN Message Object 9 Acceptance Mask Register
CAN_MOAMR9_0	SET	0
CAN_MOAMR9_AM	SET	536870911
CAN_MOAMR9_MIDE	SET	1

; CAN Message Object 0 Arbitration Register
CAN_MOAR0_ID	SET	3
CAN_MOAR0_IDE	SET	1
CAN_MOAR0_PRI	SET	3

; CAN Message Object 10 Arbitration Register
CAN_MOAR10_ID	SET	0
CAN_MOAR10_IDE	SET	0
CAN_MOAR10_PRI	SET	3

; CAN Message Object 11 Arbitration Register
CAN_MOAR11_ID	SET	0
CAN_MOAR11_IDE	SET	0
CAN_MOAR11_PRI	SET	3

; CAN Message Object 12 Arbitration Register
CAN_MOAR12_ID	SET	0
CAN_MOAR12_IDE	SET	0
CAN_MOAR12_PRI	SET	3

; CAN Message Object 13 Arbitration Register
CAN_MOAR13_ID	SET	0
CAN_MOAR13_IDE	SET	0
CAN_MOAR13_PRI	SET	3

; CAN Message Object 14 Arbitration Register
CAN_MOAR14_ID	SET	0
CAN_MOAR14_IDE	SET	0
CAN_MOAR14_PRI	SET	3

; CAN Message Object 15 Arbitration Register
CAN_MOAR15_ID	SET	0
CAN_MOAR15_IDE	SET	0
CAN_MOAR15_PRI	SET	3

; CAN Message Object 16 Arbitration Register
CAN_MOAR16_ID	SET	0
CAN_MOAR16_IDE	SET	0
CAN_MOAR16_PRI	SET	3

; CAN Message Object 17 Arbitration Register
CAN_MOAR17_ID	SET	0
CAN_MOAR17_IDE	SET	0
CAN_MOAR17_PRI	SET	3

; CAN Message Object 18 Arbitration Register
CAN_MOAR18_ID	SET	0
CAN_MOAR18_IDE	SET	0
CAN_MOAR18_PRI	SET	3

; CAN Message Object 19 Arbitration Register
CAN_MOAR19_ID	SET	0
CAN_MOAR19_IDE	SET	0
CAN_MOAR19_PRI	SET	3

; CAN Message Object 1 Arbitration Register
CAN_MOAR1_ID	SET	3
CAN_MOAR1_IDE	SET	1
CAN_MOAR1_PRI	SET	3

; CAN Message Object 20 Arbitration Register
CAN_MOAR20_ID	SET	0
CAN_MOAR20_IDE	SET	0
CAN_MOAR20_PRI	SET	3

; CAN Message Object 21 Arbitration Register
CAN_MOAR21_ID	SET	0
CAN_MOAR21_IDE	SET	0
CAN_MOAR21_PRI	SET	3

; CAN Message Object 22 Arbitration Register
CAN_MOAR22_ID	SET	0
CAN_MOAR22_IDE	SET	0
CAN_MOAR22_PRI	SET	3

; CAN Message Object 23 Arbitration Register
CAN_MOAR23_ID	SET	0
CAN_MOAR23_IDE	SET	0
CAN_MOAR23_PRI	SET	3

; CAN Message Object 24 Arbitration Register
CAN_MOAR24_ID	SET	0
CAN_MOAR24_IDE	SET	0
CAN_MOAR24_PRI	SET	3

; CAN Message Object 25 Arbitration Register
CAN_MOAR25_ID	SET	0
CAN_MOAR25_IDE	SET	0
CAN_MOAR25_PRI	SET	3

; CAN Message Object 26 Arbitration Register
CAN_MOAR26_ID	SET	0
CAN_MOAR26_IDE	SET	0
CAN_MOAR26_PRI	SET	3

; CAN Message Object 27 Arbitration Register
CAN_MOAR27_ID	SET	0
CAN_MOAR27_IDE	SET	0
CAN_MOAR27_PRI	SET	3

; CAN Message Object 28 Arbitration Register
CAN_MOAR28_ID	SET	0
CAN_MOAR28_IDE	SET	0
CAN_MOAR28_PRI	SET	3

; CAN Message Object 29 Arbitration Register
CAN_MOAR29_ID	SET	0
CAN_MOAR29_IDE	SET	0
CAN_MOAR29_PRI	SET	3

; CAN Message Object 2 Arbitration Register
CAN_MOAR2_ID	SET	3
CAN_MOAR2_IDE	SET	1
CAN_MOAR2_PRI	SET	3

; CAN Message Object 30 Arbitration Register
CAN_MOAR30_ID	SET	0
CAN_MOAR30_IDE	SET	0
CAN_MOAR30_PRI	SET	3

; CAN Message Object 31 Arbitration Register
CAN_MOAR31_ID	SET	0
CAN_MOAR31_IDE	SET	0
CAN_MOAR31_PRI	SET	3

; CAN Message Object 3 Arbitration Register
CAN_MOAR3_ID	SET	536870896
CAN_MOAR3_IDE	SET	1
CAN_MOAR3_PRI	SET	3

; CAN Message Object 4 Arbitration Register
CAN_MOAR4_ID	SET	0
CAN_MOAR4_IDE	SET	0
CAN_MOAR4_PRI	SET	3

; CAN Message Object 5 Arbitration Register
CAN_MOAR5_ID	SET	0
CAN_MOAR5_IDE	SET	0
CAN_MOAR5_PRI	SET	3

; CAN Message Object 6 Arbitration Register
CAN_MOAR6_ID	SET	0
CAN_MOAR6_IDE	SET	0
CAN_MOAR6_PRI	SET	3

; CAN Message Object 7 Arbitration Register
CAN_MOAR7_ID	SET	0
CAN_MOAR7_IDE	SET	0
CAN_MOAR7_PRI	SET	3

; CAN Message Object 8 Arbitration Register
CAN_MOAR8_ID	SET	0
CAN_MOAR8_IDE	SET	0
CAN_MOAR8_PRI	SET	3

; CAN Message Object 9 Arbitration Register
CAN_MOAR9_ID	SET	0
CAN_MOAR9_IDE	SET	0
CAN_MOAR9_PRI	SET	3

; CAN Message Object 0 Control Register
CAN_MOCTR0_CTRL	SET	256
CAN_MOCTR0_DIR	SET	0
CAN_MOCTR0_LIST	SET	1
CAN_MOCTR0_MSGLST	SET	0
CAN_MOCTR0_MSGVAL	SET	1
CAN_MOCTR0_NEWDAT	SET	0
CAN_MOCTR0_RTSEL	SET	0
CAN_MOCTR0_RXEN	SET	0
CAN_MOCTR0_RXPND	SET	0
CAN_MOCTR0_RXUPD	SET	0
CAN_MOCTR0_TXEN0	SET	0
CAN_MOCTR0_TXEN1	SET	0
CAN_MOCTR0_TXPND	SET	0
CAN_MOCTR0_TXRQ	SET	0

; CAN Message Object 10 Control Register
CAN_MOCTR10_CTRL	SET	0
CAN_MOCTR10_DIR	SET	0
CAN_MOCTR10_LIST	SET	0
CAN_MOCTR10_MSGLST	SET	0
CAN_MOCTR10_MSGVAL	SET	0
CAN_MOCTR10_NEWDAT	SET	0
CAN_MOCTR10_RTSEL	SET	0
CAN_MOCTR10_RXEN	SET	0
CAN_MOCTR10_RXPND	SET	0
CAN_MOCTR10_RXUPD	SET	0
CAN_MOCTR10_TXEN0	SET	0
CAN_MOCTR10_TXEN1	SET	0
CAN_MOCTR10_TXPND	SET	0
CAN_MOCTR10_TXRQ	SET	0

; CAN Message Object 11 Control Register
CAN_MOCTR11_CTRL	SET	0
CAN_MOCTR11_DIR	SET	0
CAN_MOCTR11_LIST	SET	0
CAN_MOCTR11_MSGLST	SET	0
CAN_MOCTR11_MSGVAL	SET	0
CAN_MOCTR11_NEWDAT	SET	0
CAN_MOCTR11_RTSEL	SET	0
CAN_MOCTR11_RXEN	SET	0
CAN_MOCTR11_RXPND	SET	0
CAN_MOCTR11_RXUPD	SET	0
CAN_MOCTR11_TXEN0	SET	0
CAN_MOCTR11_TXEN1	SET	0
CAN_MOCTR11_TXPND	SET	0
CAN_MOCTR11_TXRQ	SET	0

; CAN Message Object 12 Control Register
CAN_MOCTR12_CTRL	SET	0
CAN_MOCTR12_DIR	SET	0
CAN_MOCTR12_LIST	SET	0
CAN_MOCTR12_MSGLST	SET	0
CAN_MOCTR12_MSGVAL	SET	0
CAN_MOCTR12_NEWDAT	SET	0
CAN_MOCTR12_RTSEL	SET	0
CAN_MOCTR12_RXEN	SET	0
CAN_MOCTR12_RXPND	SET	0
CAN_MOCTR12_RXUPD	SET	0
CAN_MOCTR12_TXEN0	SET	0
CAN_MOCTR12_TXEN1	SET	0
CAN_MOCTR12_TXPND	SET	0
CAN_MOCTR12_TXRQ	SET	0

; CAN Message Object 13 Control Register
CAN_MOCTR13_CTRL	SET	0
CAN_MOCTR13_DIR	SET	0
CAN_MOCTR13_LIST	SET	0
CAN_MOCTR13_MSGLST	SET	0
CAN_MOCTR13_MSGVAL	SET	0
CAN_MOCTR13_NEWDAT	SET	0
CAN_MOCTR13_RTSEL	SET	0
CAN_MOCTR13_RXEN	SET	0
CAN_MOCTR13_RXPND	SET	0
CAN_MOCTR13_RXUPD	SET	0
CAN_MOCTR13_TXEN0	SET	0
CAN_MOCTR13_TXEN1	SET	0
CAN_MOCTR13_TXPND	SET	0
CAN_MOCTR13_TXRQ	SET	0

; CAN Message Object 14 Control Register
CAN_MOCTR14_CTRL	SET	0
CAN_MOCTR14_DIR	SET	0
CAN_MOCTR14_LIST	SET	0
CAN_MOCTR14_MSGLST	SET	0
CAN_MOCTR14_MSGVAL	SET	0
CAN_MOCTR14_NEWDAT	SET	0
CAN_MOCTR14_RTSEL	SET	0
CAN_MOCTR14_RXEN	SET	0
CAN_MOCTR14_RXPND	SET	0
CAN_MOCTR14_RXUPD	SET	0
CAN_MOCTR14_TXEN0	SET	0
CAN_MOCTR14_TXEN1	SET	0
CAN_MOCTR14_TXPND	SET	0
CAN_MOCTR14_TXRQ	SET	0

; CAN Message Object 15 Control Register
CAN_MOCTR15_CTRL	SET	0
CAN_MOCTR15_DIR	SET	0
CAN_MOCTR15_LIST	SET	0
CAN_MOCTR15_MSGLST	SET	0
CAN_MOCTR15_MSGVAL	SET	0
CAN_MOCTR15_NEWDAT	SET	0
CAN_MOCTR15_RTSEL	SET	0
CAN_MOCTR15_RXEN	SET	0
CAN_MOCTR15_RXPND	SET	0
CAN_MOCTR15_RXUPD	SET	0
CAN_MOCTR15_TXEN0	SET	0
CAN_MOCTR15_TXEN1	SET	0
CAN_MOCTR15_TXPND	SET	0
CAN_MOCTR15_TXRQ	SET	0

; CAN Message Object 16 Control Register
CAN_MOCTR16_CTRL	SET	0
CAN_MOCTR16_DIR	SET	0
CAN_MOCTR16_LIST	SET	0
CAN_MOCTR16_MSGLST	SET	0
CAN_MOCTR16_MSGVAL	SET	0
CAN_MOCTR16_NEWDAT	SET	0
CAN_MOCTR16_RTSEL	SET	0
CAN_MOCTR16_RXEN	SET	0
CAN_MOCTR16_RXPND	SET	0
CAN_MOCTR16_RXUPD	SET	0
CAN_MOCTR16_TXEN0	SET	0
CAN_MOCTR16_TXEN1	SET	0
CAN_MOCTR16_TXPND	SET	0
CAN_MOCTR16_TXRQ	SET	0

; CAN Message Object 17 Control Register
CAN_MOCTR17_CTRL	SET	0
CAN_MOCTR17_DIR	SET	0
CAN_MOCTR17_LIST	SET	0
CAN_MOCTR17_MSGLST	SET	0
CAN_MOCTR17_MSGVAL	SET	0
CAN_MOCTR17_NEWDAT	SET	0
CAN_MOCTR17_RTSEL	SET	0
CAN_MOCTR17_RXEN	SET	0
CAN_MOCTR17_RXPND	SET	0
CAN_MOCTR17_RXUPD	SET	0
CAN_MOCTR17_TXEN0	SET	0
CAN_MOCTR17_TXEN1	SET	0
CAN_MOCTR17_TXPND	SET	0
CAN_MOCTR17_TXRQ	SET	0

; CAN Message Object 18 Control Register
CAN_MOCTR18_CTRL	SET	0
CAN_MOCTR18_DIR	SET	0
CAN_MOCTR18_LIST	SET	0
CAN_MOCTR18_MSGLST	SET	0
CAN_MOCTR18_MSGVAL	SET	0
CAN_MOCTR18_NEWDAT	SET	0
CAN_MOCTR18_RTSEL	SET	0
CAN_MOCTR18_RXEN	SET	0
CAN_MOCTR18_RXPND	SET	0
CAN_MOCTR18_RXUPD	SET	0
CAN_MOCTR18_TXEN0	SET	0
CAN_MOCTR18_TXEN1	SET	0
CAN_MOCTR18_TXPND	SET	0
CAN_MOCTR18_TXRQ	SET	0

; CAN Message Object 19 Control Register
CAN_MOCTR19_CTRL	SET	0
CAN_MOCTR19_DIR	SET	0
CAN_MOCTR19_LIST	SET	0
CAN_MOCTR19_MSGLST	SET	0
CAN_MOCTR19_MSGVAL	SET	0
CAN_MOCTR19_NEWDAT	SET	0
CAN_MOCTR19_RTSEL	SET	0
CAN_MOCTR19_RXEN	SET	0
CAN_MOCTR19_RXPND	SET	0
CAN_MOCTR19_RXUPD	SET	0
CAN_MOCTR19_TXEN0	SET	0
CAN_MOCTR19_TXEN1	SET	0
CAN_MOCTR19_TXPND	SET	0
CAN_MOCTR19_TXRQ	SET	0

; CAN Message Object 1 Control Register
CAN_MOCTR1_CTRL	SET	512
CAN_MOCTR1_DIR	SET	0
CAN_MOCTR1_LIST	SET	1
CAN_MOCTR1_MSGLST	SET	0
CAN_MOCTR1_MSGVAL	SET	1
CAN_MOCTR1_NEWDAT	SET	0
CAN_MOCTR1_RTSEL	SET	0
CAN_MOCTR1_RXEN	SET	0
CAN_MOCTR1_RXPND	SET	0
CAN_MOCTR1_RXUPD	SET	0
CAN_MOCTR1_TXEN0	SET	0
CAN_MOCTR1_TXEN1	SET	0
CAN_MOCTR1_TXPND	SET	0
CAN_MOCTR1_TXRQ	SET	0

; CAN Message Object 20 Control Register
CAN_MOCTR20_CTRL	SET	0
CAN_MOCTR20_DIR	SET	0
CAN_MOCTR20_LIST	SET	0
CAN_MOCTR20_MSGLST	SET	0
CAN_MOCTR20_MSGVAL	SET	0
CAN_MOCTR20_NEWDAT	SET	0
CAN_MOCTR20_RTSEL	SET	0
CAN_MOCTR20_RXEN	SET	0
CAN_MOCTR20_RXPND	SET	0
CAN_MOCTR20_RXUPD	SET	0
CAN_MOCTR20_TXEN0	SET	0
CAN_MOCTR20_TXEN1	SET	0
CAN_MOCTR20_TXPND	SET	0
CAN_MOCTR20_TXRQ	SET	0

; CAN Message Object 21 Control Register
CAN_MOCTR21_CTRL	SET	0
CAN_MOCTR21_DIR	SET	0
CAN_MOCTR21_LIST	SET	0
CAN_MOCTR21_MSGLST	SET	0
CAN_MOCTR21_MSGVAL	SET	0
CAN_MOCTR21_NEWDAT	SET	0
CAN_MOCTR21_RTSEL	SET	0
CAN_MOCTR21_RXEN	SET	0
CAN_MOCTR21_RXPND	SET	0
CAN_MOCTR21_RXUPD	SET	0
CAN_MOCTR21_TXEN0	SET	0
CAN_MOCTR21_TXEN1	SET	0
CAN_MOCTR21_TXPND	SET	0
CAN_MOCTR21_TXRQ	SET	0

; CAN Message Object 22 Control Register
CAN_MOCTR22_CTRL	SET	0
CAN_MOCTR22_DIR	SET	0
CAN_MOCTR22_LIST	SET	0
CAN_MOCTR22_MSGLST	SET	0
CAN_MOCTR22_MSGVAL	SET	0
CAN_MOCTR22_NEWDAT	SET	0
CAN_MOCTR22_RTSEL	SET	0
CAN_MOCTR22_RXEN	SET	0
CAN_MOCTR22_RXPND	SET	0
CAN_MOCTR22_RXUPD	SET	0
CAN_MOCTR22_TXEN0	SET	0
CAN_MOCTR22_TXEN1	SET	0
CAN_MOCTR22_TXPND	SET	0
CAN_MOCTR22_TXRQ	SET	0

; CAN Message Object 23 Control Register
CAN_MOCTR23_CTRL	SET	0
CAN_MOCTR23_DIR	SET	0
CAN_MOCTR23_LIST	SET	0
CAN_MOCTR23_MSGLST	SET	0
CAN_MOCTR23_MSGVAL	SET	0
CAN_MOCTR23_NEWDAT	SET	0
CAN_MOCTR23_RTSEL	SET	0
CAN_MOCTR23_RXEN	SET	0
CAN_MOCTR23_RXPND	SET	0
CAN_MOCTR23_RXUPD	SET	0
CAN_MOCTR23_TXEN0	SET	0
CAN_MOCTR23_TXEN1	SET	0
CAN_MOCTR23_TXPND	SET	0
CAN_MOCTR23_TXRQ	SET	0

; CAN Message Object 24 Control Register
CAN_MOCTR24_CTRL	SET	0
CAN_MOCTR24_DIR	SET	0
CAN_MOCTR24_LIST	SET	0
CAN_MOCTR24_MSGLST	SET	0
CAN_MOCTR24_MSGVAL	SET	0
CAN_MOCTR24_NEWDAT	SET	0
CAN_MOCTR24_RTSEL	SET	0
CAN_MOCTR24_RXEN	SET	0
CAN_MOCTR24_RXPND	SET	0
CAN_MOCTR24_RXUPD	SET	0
CAN_MOCTR24_TXEN0	SET	0
CAN_MOCTR24_TXEN1	SET	0
CAN_MOCTR24_TXPND	SET	0
CAN_MOCTR24_TXRQ	SET	0

; CAN Message Object 25 Control Register
CAN_MOCTR25_CTRL	SET	0
CAN_MOCTR25_DIR	SET	0
CAN_MOCTR25_LIST	SET	0
CAN_MOCTR25_MSGLST	SET	0
CAN_MOCTR25_MSGVAL	SET	0
CAN_MOCTR25_NEWDAT	SET	0
CAN_MOCTR25_RTSEL	SET	0
CAN_MOCTR25_RXEN	SET	0
CAN_MOCTR25_RXPND	SET	0
CAN_MOCTR25_RXUPD	SET	0
CAN_MOCTR25_TXEN0	SET	0
CAN_MOCTR25_TXEN1	SET	0
CAN_MOCTR25_TXPND	SET	0
CAN_MOCTR25_TXRQ	SET	0

; CAN Message Object 26 Control Register
CAN_MOCTR26_CTRL	SET	0
CAN_MOCTR26_DIR	SET	0
CAN_MOCTR26_LIST	SET	0
CAN_MOCTR26_MSGLST	SET	0
CAN_MOCTR26_MSGVAL	SET	0
CAN_MOCTR26_NEWDAT	SET	0
CAN_MOCTR26_RTSEL	SET	0
CAN_MOCTR26_RXEN	SET	0
CAN_MOCTR26_RXPND	SET	0
CAN_MOCTR26_RXUPD	SET	0
CAN_MOCTR26_TXEN0	SET	0
CAN_MOCTR26_TXEN1	SET	0
CAN_MOCTR26_TXPND	SET	0
CAN_MOCTR26_TXRQ	SET	0

; CAN Message Object 27 Control Register
CAN_MOCTR27_CTRL	SET	0
CAN_MOCTR27_DIR	SET	0
CAN_MOCTR27_LIST	SET	0
CAN_MOCTR27_MSGLST	SET	0
CAN_MOCTR27_MSGVAL	SET	0
CAN_MOCTR27_NEWDAT	SET	0
CAN_MOCTR27_RTSEL	SET	0
CAN_MOCTR27_RXEN	SET	0
CAN_MOCTR27_RXPND	SET	0
CAN_MOCTR27_RXUPD	SET	0
CAN_MOCTR27_TXEN0	SET	0
CAN_MOCTR27_TXEN1	SET	0
CAN_MOCTR27_TXPND	SET	0
CAN_MOCTR27_TXRQ	SET	0

; CAN Message Object 28 Control Register
CAN_MOCTR28_CTRL	SET	0
CAN_MOCTR28_DIR	SET	0
CAN_MOCTR28_LIST	SET	0
CAN_MOCTR28_MSGLST	SET	0
CAN_MOCTR28_MSGVAL	SET	0
CAN_MOCTR28_NEWDAT	SET	0
CAN_MOCTR28_RTSEL	SET	0
CAN_MOCTR28_RXEN	SET	0
CAN_MOCTR28_RXPND	SET	0
CAN_MOCTR28_RXUPD	SET	0
CAN_MOCTR28_TXEN0	SET	0
CAN_MOCTR28_TXEN1	SET	0
CAN_MOCTR28_TXPND	SET	0
CAN_MOCTR28_TXRQ	SET	0

; CAN Message Object 29 Control Register
CAN_MOCTR29_CTRL	SET	0
CAN_MOCTR29_DIR	SET	0
CAN_MOCTR29_LIST	SET	0
CAN_MOCTR29_MSGLST	SET	0
CAN_MOCTR29_MSGVAL	SET	0
CAN_MOCTR29_NEWDAT	SET	0
CAN_MOCTR29_RTSEL	SET	0
CAN_MOCTR29_RXEN	SET	0
CAN_MOCTR29_RXPND	SET	0
CAN_MOCTR29_RXUPD	SET	0
CAN_MOCTR29_TXEN0	SET	0
CAN_MOCTR29_TXEN1	SET	0
CAN_MOCTR29_TXPND	SET	0
CAN_MOCTR29_TXRQ	SET	0

; CAN Message Object 2 Control Register
CAN_MOCTR2_CTRL	SET	769
CAN_MOCTR2_DIR	SET	0
CAN_MOCTR2_LIST	SET	1
CAN_MOCTR2_MSGLST	SET	0
CAN_MOCTR2_MSGVAL	SET	1
CAN_MOCTR2_NEWDAT	SET	0
CAN_MOCTR2_RTSEL	SET	0
CAN_MOCTR2_RXEN	SET	0
CAN_MOCTR2_RXPND	SET	0
CAN_MOCTR2_RXUPD	SET	0
CAN_MOCTR2_TXEN0	SET	0
CAN_MOCTR2_TXEN1	SET	0
CAN_MOCTR2_TXPND	SET	0
CAN_MOCTR2_TXRQ	SET	0

; CAN Message Object 30 Control Register
CAN_MOCTR30_CTRL	SET	0
CAN_MOCTR30_DIR	SET	0
CAN_MOCTR30_LIST	SET	0
CAN_MOCTR30_MSGLST	SET	0
CAN_MOCTR30_MSGVAL	SET	0
CAN_MOCTR30_NEWDAT	SET	0
CAN_MOCTR30_RTSEL	SET	0
CAN_MOCTR30_RXEN	SET	0
CAN_MOCTR30_RXPND	SET	0
CAN_MOCTR30_RXUPD	SET	0
CAN_MOCTR30_TXEN0	SET	0
CAN_MOCTR30_TXEN1	SET	0
CAN_MOCTR30_TXPND	SET	0
CAN_MOCTR30_TXRQ	SET	0

; CAN Message Object 31 Control Register
CAN_MOCTR31_CTRL	SET	0
CAN_MOCTR31_DIR	SET	0
CAN_MOCTR31_LIST	SET	0
CAN_MOCTR31_MSGLST	SET	0
CAN_MOCTR31_MSGVAL	SET	0
CAN_MOCTR31_NEWDAT	SET	0
CAN_MOCTR31_RTSEL	SET	0
CAN_MOCTR31_RXEN	SET	0
CAN_MOCTR31_RXPND	SET	0
CAN_MOCTR31_RXUPD	SET	0
CAN_MOCTR31_TXEN0	SET	0
CAN_MOCTR31_TXEN1	SET	0
CAN_MOCTR31_TXPND	SET	0
CAN_MOCTR31_TXRQ	SET	0

; CAN Message Object 3 Control Register
CAN_MOCTR3_CTRL	SET	770
CAN_MOCTR3_DIR	SET	1
CAN_MOCTR3_LIST	SET	1
CAN_MOCTR3_MSGLST	SET	0
CAN_MOCTR3_MSGVAL	SET	1
CAN_MOCTR3_NEWDAT	SET	0
CAN_MOCTR3_RTSEL	SET	0
CAN_MOCTR3_RXEN	SET	0
CAN_MOCTR3_RXPND	SET	0
CAN_MOCTR3_RXUPD	SET	0
CAN_MOCTR3_TXEN0	SET	0
CAN_MOCTR3_TXEN1	SET	0
CAN_MOCTR3_TXPND	SET	0
CAN_MOCTR3_TXRQ	SET	0

; CAN Message Object 4 Control Register
CAN_MOCTR4_CTRL	SET	0
CAN_MOCTR4_DIR	SET	0
CAN_MOCTR4_LIST	SET	0
CAN_MOCTR4_MSGLST	SET	0
CAN_MOCTR4_MSGVAL	SET	0
CAN_MOCTR4_NEWDAT	SET	0
CAN_MOCTR4_RTSEL	SET	0
CAN_MOCTR4_RXEN	SET	0
CAN_MOCTR4_RXPND	SET	0
CAN_MOCTR4_RXUPD	SET	0
CAN_MOCTR4_TXEN0	SET	0
CAN_MOCTR4_TXEN1	SET	0
CAN_MOCTR4_TXPND	SET	0
CAN_MOCTR4_TXRQ	SET	0

; CAN Message Object 5 Control Register
CAN_MOCTR5_CTRL	SET	0
CAN_MOCTR5_DIR	SET	0
CAN_MOCTR5_LIST	SET	0
CAN_MOCTR5_MSGLST	SET	0
CAN_MOCTR5_MSGVAL	SET	0
CAN_MOCTR5_NEWDAT	SET	0
CAN_MOCTR5_RTSEL	SET	0
CAN_MOCTR5_RXEN	SET	0
CAN_MOCTR5_RXPND	SET	0
CAN_MOCTR5_RXUPD	SET	0
CAN_MOCTR5_TXEN0	SET	0
CAN_MOCTR5_TXEN1	SET	0
CAN_MOCTR5_TXPND	SET	0
CAN_MOCTR5_TXRQ	SET	0

; CAN Message Object 6 Control Register
CAN_MOCTR6_CTRL	SET	0
CAN_MOCTR6_DIR	SET	0
CAN_MOCTR6_LIST	SET	0
CAN_MOCTR6_MSGLST	SET	0
CAN_MOCTR6_MSGVAL	SET	0
CAN_MOCTR6_NEWDAT	SET	0
CAN_MOCTR6_RTSEL	SET	0
CAN_MOCTR6_RXEN	SET	0
CAN_MOCTR6_RXPND	SET	0
CAN_MOCTR6_RXUPD	SET	0
CAN_MOCTR6_TXEN0	SET	0
CAN_MOCTR6_TXEN1	SET	0
CAN_MOCTR6_TXPND	SET	0
CAN_MOCTR6_TXRQ	SET	0

; CAN Message Object 7 Control Register
CAN_MOCTR7_CTRL	SET	0
CAN_MOCTR7_DIR	SET	0
CAN_MOCTR7_LIST	SET	0
CAN_MOCTR7_MSGLST	SET	0
CAN_MOCTR7_MSGVAL	SET	0
CAN_MOCTR7_NEWDAT	SET	0
CAN_MOCTR7_RTSEL	SET	0
CAN_MOCTR7_RXEN	SET	0
CAN_MOCTR7_RXPND	SET	0
CAN_MOCTR7_RXUPD	SET	0
CAN_MOCTR7_TXEN0	SET	0
CAN_MOCTR7_TXEN1	SET	0
CAN_MOCTR7_TXPND	SET	0
CAN_MOCTR7_TXRQ	SET	0

; CAN Message Object 8 Control Register
CAN_MOCTR8_CTRL	SET	0
CAN_MOCTR8_DIR	SET	0
CAN_MOCTR8_LIST	SET	0
CAN_MOCTR8_MSGLST	SET	0
CAN_MOCTR8_MSGVAL	SET	0
CAN_MOCTR8_NEWDAT	SET	0
CAN_MOCTR8_RTSEL	SET	0
CAN_MOCTR8_RXEN	SET	0
CAN_MOCTR8_RXPND	SET	0
CAN_MOCTR8_RXUPD	SET	0
CAN_MOCTR8_TXEN0	SET	0
CAN_MOCTR8_TXEN1	SET	0
CAN_MOCTR8_TXPND	SET	0
CAN_MOCTR8_TXRQ	SET	0

; CAN Message Object 9 Control Register
CAN_MOCTR9_CTRL	SET	0
CAN_MOCTR9_DIR	SET	0
CAN_MOCTR9_LIST	SET	0
CAN_MOCTR9_MSGLST	SET	0
CAN_MOCTR9_MSGVAL	SET	0
CAN_MOCTR9_NEWDAT	SET	0
CAN_MOCTR9_RTSEL	SET	0
CAN_MOCTR9_RXEN	SET	0
CAN_MOCTR9_RXPND	SET	0
CAN_MOCTR9_RXUPD	SET	0
CAN_MOCTR9_TXEN0	SET	0
CAN_MOCTR9_TXEN1	SET	0
CAN_MOCTR9_TXPND	SET	0
CAN_MOCTR9_TXRQ	SET	0

; CAN Message Object 0 Data Register High
CAN_MODATAH0_DB4	SET	0
CAN_MODATAH0_DB5	SET	0
CAN_MODATAH0_DB6	SET	0
CAN_MODATAH0_DB7	SET	0

; CAN Message Object 10 Data Register High
CAN_MODATAH10_DB4	SET	0
CAN_MODATAH10_DB5	SET	0
CAN_MODATAH10_DB6	SET	0
CAN_MODATAH10_DB7	SET	0

; CAN Message Object 11 Data Register High
CAN_MODATAH11_DB4	SET	0
CAN_MODATAH11_DB5	SET	0
CAN_MODATAH11_DB6	SET	0
CAN_MODATAH11_DB7	SET	0

; CAN Message Object 12 Data Register High
CAN_MODATAH12_DB4	SET	0
CAN_MODATAH12_DB5	SET	0
CAN_MODATAH12_DB6	SET	0
CAN_MODATAH12_DB7	SET	0

; CAN Message Object 13 Data Register High
CAN_MODATAH13_DB4	SET	0
CAN_MODATAH13_DB5	SET	0
CAN_MODATAH13_DB6	SET	0
CAN_MODATAH13_DB7	SET	0

; CAN Message Object 14 Data Register High
CAN_MODATAH14_DB4	SET	0
CAN_MODATAH14_DB5	SET	0
CAN_MODATAH14_DB6	SET	0
CAN_MODATAH14_DB7	SET	0

; CAN Message Object 15 Data Register High
CAN_MODATAH15_DB4	SET	0
CAN_MODATAH15_DB5	SET	0
CAN_MODATAH15_DB6	SET	0
CAN_MODATAH15_DB7	SET	0

; CAN Message Object 16 Data Register High
CAN_MODATAH16_DB4	SET	0
CAN_MODATAH16_DB5	SET	0
CAN_MODATAH16_DB6	SET	0
CAN_MODATAH16_DB7	SET	0

; CAN Message Object 17 Data Register High
CAN_MODATAH17_DB4	SET	0
CAN_MODATAH17_DB5	SET	0
CAN_MODATAH17_DB6	SET	0
CAN_MODATAH17_DB7	SET	0

; CAN Message Object 18 Data Register High
CAN_MODATAH18_DB4	SET	0
CAN_MODATAH18_DB5	SET	0
CAN_MODATAH18_DB6	SET	0
CAN_MODATAH18_DB7	SET	0

; CAN Message Object 19 Data Register High
CAN_MODATAH19_DB4	SET	0
CAN_MODATAH19_DB5	SET	0
CAN_MODATAH19_DB6	SET	0
CAN_MODATAH19_DB7	SET	0

; CAN Message Object 1 Data Register High
CAN_MODATAH1_DB4	SET	0
CAN_MODATAH1_DB5	SET	0
CAN_MODATAH1_DB6	SET	0
CAN_MODATAH1_DB7	SET	0

; CAN Message Object 20 Data Register High
CAN_MODATAH20_DB4	SET	0
CAN_MODATAH20_DB5	SET	0
CAN_MODATAH20_DB6	SET	0
CAN_MODATAH20_DB7	SET	0

; CAN Message Object 21 Data Register High
CAN_MODATAH21_DB4	SET	0
CAN_MODATAH21_DB5	SET	0
CAN_MODATAH21_DB6	SET	0
CAN_MODATAH21_DB7	SET	0

; CAN Message Object 22 Data Register High
CAN_MODATAH22_DB4	SET	0
CAN_MODATAH22_DB5	SET	0
CAN_MODATAH22_DB6	SET	0
CAN_MODATAH22_DB7	SET	0

; CAN Message Object 23 Data Register High
CAN_MODATAH23_DB4	SET	0
CAN_MODATAH23_DB5	SET	0
CAN_MODATAH23_DB6	SET	0
CAN_MODATAH23_DB7	SET	0

; CAN Message Object 24 Data Register High
CAN_MODATAH24_DB4	SET	0
CAN_MODATAH24_DB5	SET	0
CAN_MODATAH24_DB6	SET	0
CAN_MODATAH24_DB7	SET	0

; CAN Message Object 25 Data Register High
CAN_MODATAH25_DB4	SET	0
CAN_MODATAH25_DB5	SET	0
CAN_MODATAH25_DB6	SET	0
CAN_MODATAH25_DB7	SET	0

; CAN Message Object 26 Data Register High
CAN_MODATAH26_DB4	SET	0
CAN_MODATAH26_DB5	SET	0
CAN_MODATAH26_DB6	SET	0
CAN_MODATAH26_DB7	SET	0

; CAN Message Object 27 Data Register High
CAN_MODATAH27_DB4	SET	0
CAN_MODATAH27_DB5	SET	0
CAN_MODATAH27_DB6	SET	0
CAN_MODATAH27_DB7	SET	0

; CAN Message Object 28 Data Register High
CAN_MODATAH28_DB4	SET	0
CAN_MODATAH28_DB5	SET	0
CAN_MODATAH28_DB6	SET	0
CAN_MODATAH28_DB7	SET	0

; CAN Message Object 29 Data Register High
CAN_MODATAH29_DB4	SET	0
CAN_MODATAH29_DB5	SET	0
CAN_MODATAH29_DB6	SET	0
CAN_MODATAH29_DB7	SET	0

; CAN Message Object 2 Data Register High
CAN_MODATAH2_DB4	SET	0
CAN_MODATAH2_DB5	SET	0
CAN_MODATAH2_DB6	SET	0
CAN_MODATAH2_DB7	SET	0

; CAN Message Object 30 Data Register High
CAN_MODATAH30_DB4	SET	0
CAN_MODATAH30_DB5	SET	0
CAN_MODATAH30_DB6	SET	0
CAN_MODATAH30_DB7	SET	0

; CAN Message Object 31 Data Register High
CAN_MODATAH31_DB4	SET	0
CAN_MODATAH31_DB5	SET	0
CAN_MODATAH31_DB6	SET	0
CAN_MODATAH31_DB7	SET	0

; CAN Message Object 3 Data Register High
CAN_MODATAH3_DB4	SET	0
CAN_MODATAH3_DB5	SET	0
CAN_MODATAH3_DB6	SET	0
CAN_MODATAH3_DB7	SET	0

; CAN Message Object 4 Data Register High
CAN_MODATAH4_DB4	SET	0
CAN_MODATAH4_DB5	SET	0
CAN_MODATAH4_DB6	SET	0
CAN_MODATAH4_DB7	SET	0

; CAN Message Object 5 Data Register High
CAN_MODATAH5_DB4	SET	0
CAN_MODATAH5_DB5	SET	0
CAN_MODATAH5_DB6	SET	0
CAN_MODATAH5_DB7	SET	0

; CAN Message Object 6 Data Register High
CAN_MODATAH6_DB4	SET	0
CAN_MODATAH6_DB5	SET	0
CAN_MODATAH6_DB6	SET	0
CAN_MODATAH6_DB7	SET	0

; CAN Message Object 7 Data Register High
CAN_MODATAH7_DB4	SET	0
CAN_MODATAH7_DB5	SET	0
CAN_MODATAH7_DB6	SET	0
CAN_MODATAH7_DB7	SET	0

; CAN Message Object 8 Data Register High
CAN_MODATAH8_DB4	SET	0
CAN_MODATAH8_DB5	SET	0
CAN_MODATAH8_DB6	SET	0
CAN_MODATAH8_DB7	SET	0

; CAN Message Object 9 Data Register High
CAN_MODATAH9_DB4	SET	0
CAN_MODATAH9_DB5	SET	0
CAN_MODATAH9_DB6	SET	0
CAN_MODATAH9_DB7	SET	0

; CAN Message Object 0 Data Register Low
CAN_MODATAL0_DB0	SET	0
CAN_MODATAL0_DB1	SET	0
CAN_MODATAL0_DB2	SET	0
CAN_MODATAL0_DB3	SET	0

; CAN Message Object 10 Data Register Low
CAN_MODATAL10_DB0	SET	0
CAN_MODATAL10_DB1	SET	0
CAN_MODATAL10_DB2	SET	0
CAN_MODATAL10_DB3	SET	0

; CAN Message Object 11 Data Register Low
CAN_MODATAL11_DB0	SET	0
CAN_MODATAL11_DB1	SET	0
CAN_MODATAL11_DB2	SET	0
CAN_MODATAL11_DB3	SET	0

; CAN Message Object 12 Data Register Low
CAN_MODATAL12_DB0	SET	0
CAN_MODATAL12_DB1	SET	0
CAN_MODATAL12_DB2	SET	0
CAN_MODATAL12_DB3	SET	0

; CAN Message Object 13 Data Register Low
CAN_MODATAL13_DB0	SET	0
CAN_MODATAL13_DB1	SET	0
CAN_MODATAL13_DB2	SET	0
CAN_MODATAL13_DB3	SET	0

; CAN Message Object 14 Data Register Low
CAN_MODATAL14_DB0	SET	0
CAN_MODATAL14_DB1	SET	0
CAN_MODATAL14_DB2	SET	0
CAN_MODATAL14_DB3	SET	0

; CAN Message Object 15 Data Register Low
CAN_MODATAL15_DB0	SET	0
CAN_MODATAL15_DB1	SET	0
CAN_MODATAL15_DB2	SET	0
CAN_MODATAL15_DB3	SET	0

; CAN Message Object 16 Data Register Low
CAN_MODATAL16_DB0	SET	0
CAN_MODATAL16_DB1	SET	0
CAN_MODATAL16_DB2	SET	0
CAN_MODATAL16_DB3	SET	0

; CAN Message Object 17 Data Register Low
CAN_MODATAL17_DB0	SET	0
CAN_MODATAL17_DB1	SET	0
CAN_MODATAL17_DB2	SET	0
CAN_MODATAL17_DB3	SET	0

; CAN Message Object 18 Data Register Low
CAN_MODATAL18_DB0	SET	0
CAN_MODATAL18_DB1	SET	0
CAN_MODATAL18_DB2	SET	0
CAN_MODATAL18_DB3	SET	0

; CAN Message Object 19 Data Register Low
CAN_MODATAL19_DB0	SET	0
CAN_MODATAL19_DB1	SET	0
CAN_MODATAL19_DB2	SET	0
CAN_MODATAL19_DB3	SET	0

; CAN Message Object 1 Data Register Low
CAN_MODATAL1_DB0	SET	0
CAN_MODATAL1_DB1	SET	0
CAN_MODATAL1_DB2	SET	0
CAN_MODATAL1_DB3	SET	0

; CAN Message Object 20 Data Register Low
CAN_MODATAL20_DB0	SET	0
CAN_MODATAL20_DB1	SET	0
CAN_MODATAL20_DB2	SET	0
CAN_MODATAL20_DB3	SET	0

; CAN Message Object 21 Data Register Low
CAN_MODATAL21_DB0	SET	0
CAN_MODATAL21_DB1	SET	0
CAN_MODATAL21_DB2	SET	0
CAN_MODATAL21_DB3	SET	0

; CAN Message Object 22 Data Register Low
CAN_MODATAL22_DB0	SET	0
CAN_MODATAL22_DB1	SET	0
CAN_MODATAL22_DB2	SET	0
CAN_MODATAL22_DB3	SET	0

; CAN Message Object 23 Data Register Low
CAN_MODATAL23_DB0	SET	0
CAN_MODATAL23_DB1	SET	0
CAN_MODATAL23_DB2	SET	0
CAN_MODATAL23_DB3	SET	0

; CAN Message Object 24 Data Register Low
CAN_MODATAL24_DB0	SET	0
CAN_MODATAL24_DB1	SET	0
CAN_MODATAL24_DB2	SET	0
CAN_MODATAL24_DB3	SET	0

; CAN Message Object 25 Data Register Low
CAN_MODATAL25_DB0	SET	0
CAN_MODATAL25_DB1	SET	0
CAN_MODATAL25_DB2	SET	0
CAN_MODATAL25_DB3	SET	0

; CAN Message Object 26 Data Register Low
CAN_MODATAL26_DB0	SET	0
CAN_MODATAL26_DB1	SET	0
CAN_MODATAL26_DB2	SET	0
CAN_MODATAL26_DB3	SET	0

; CAN Message Object 27 Data Register Low
CAN_MODATAL27_DB0	SET	0
CAN_MODATAL27_DB1	SET	0
CAN_MODATAL27_DB2	SET	0
CAN_MODATAL27_DB3	SET	0

; CAN Message Object 28 Data Register Low
CAN_MODATAL28_DB0	SET	0
CAN_MODATAL28_DB1	SET	0
CAN_MODATAL28_DB2	SET	0
CAN_MODATAL28_DB3	SET	0

; CAN Message Object 29 Data Register Low
CAN_MODATAL29_DB0	SET	0
CAN_MODATAL29_DB1	SET	0
CAN_MODATAL29_DB2	SET	0
CAN_MODATAL29_DB3	SET	0

; CAN Message Object 2 Data Register Low
CAN_MODATAL2_DB0	SET	0
CAN_MODATAL2_DB1	SET	0
CAN_MODATAL2_DB2	SET	0
CAN_MODATAL2_DB3	SET	0

; CAN Message Object 30 Data Register Low
CAN_MODATAL30_DB0	SET	0
CAN_MODATAL30_DB1	SET	0
CAN_MODATAL30_DB2	SET	0
CAN_MODATAL30_DB3	SET	0

; CAN Message Object 31 Data Register Low
CAN_MODATAL31_DB0	SET	0
CAN_MODATAL31_DB1	SET	0
CAN_MODATAL31_DB2	SET	0
CAN_MODATAL31_DB3	SET	0

; CAN Message Object 3 Data Register Low
CAN_MODATAL3_DB0	SET	0
CAN_MODATAL3_DB1	SET	0
CAN_MODATAL3_DB2	SET	0
CAN_MODATAL3_DB3	SET	0

; CAN Message Object 4 Data Register Low
CAN_MODATAL4_DB0	SET	0
CAN_MODATAL4_DB1	SET	0
CAN_MODATAL4_DB2	SET	0
CAN_MODATAL4_DB3	SET	0

; CAN Message Object 5 Data Register Low
CAN_MODATAL5_DB0	SET	0
CAN_MODATAL5_DB1	SET	0
CAN_MODATAL5_DB2	SET	0
CAN_MODATAL5_DB3	SET	0

; CAN Message Object 6 Data Register Low
CAN_MODATAL6_DB0	SET	0
CAN_MODATAL6_DB1	SET	0
CAN_MODATAL6_DB2	SET	0
CAN_MODATAL6_DB3	SET	0

; CAN Message Object 7 Data Register Low
CAN_MODATAL7_DB0	SET	0
CAN_MODATAL7_DB1	SET	0
CAN_MODATAL7_DB2	SET	0
CAN_MODATAL7_DB3	SET	0

; CAN Message Object 8 Data Register Low
CAN_MODATAL8_DB0	SET	0
CAN_MODATAL8_DB1	SET	0
CAN_MODATAL8_DB2	SET	0
CAN_MODATAL8_DB3	SET	0

; CAN Message Object 9 Data Register Low
CAN_MODATAL9_DB0	SET	0
CAN_MODATAL9_DB1	SET	0
CAN_MODATAL9_DB2	SET	0
CAN_MODATAL9_DB3	SET	0

; CAN Message Object 0 Function Control Register
CAN_MOFCR0_0	SET	0
CAN_MOFCR0_0	SET	0
CAN_MOFCR0_0	SET	0
CAN_MOFCR0_0	SET	0
CAN_MOFCR0_DATC	SET	0
CAN_MOFCR0_DLC	SET	8
CAN_MOFCR0_DLCC	SET	0
CAN_MOFCR0_FRREN	SET	0
CAN_MOFCR0_GDFS	SET	0
CAN_MOFCR0_IDC	SET	0
CAN_MOFCR0_MMC	SET	1
CAN_MOFCR0_OVIE	SET	0
CAN_MOFCR0_RMM	SET	0
CAN_MOFCR0_RXIE	SET	0
CAN_MOFCR0_SDT	SET	0
CAN_MOFCR0_STT	SET	0
CAN_MOFCR0_TXIE	SET	0

; CAN Message Object 10 Function Control Register
CAN_MOFCR10_0	SET	0
CAN_MOFCR10_0	SET	0
CAN_MOFCR10_0	SET	0
CAN_MOFCR10_0	SET	0
CAN_MOFCR10_DATC	SET	0
CAN_MOFCR10_DLC	SET	0
CAN_MOFCR10_DLCC	SET	0
CAN_MOFCR10_FRREN	SET	0
CAN_MOFCR10_GDFS	SET	0
CAN_MOFCR10_IDC	SET	0
CAN_MOFCR10_MMC	SET	0
CAN_MOFCR10_OVIE	SET	0
CAN_MOFCR10_RMM	SET	0
CAN_MOFCR10_RXIE	SET	0
CAN_MOFCR10_SDT	SET	0
CAN_MOFCR10_STT	SET	0
CAN_MOFCR10_TXIE	SET	0

; CAN Message Object 11 Function Control Register
CAN_MOFCR11_0	SET	0
CAN_MOFCR11_0	SET	0
CAN_MOFCR11_0	SET	0
CAN_MOFCR11_0	SET	0
CAN_MOFCR11_DATC	SET	0
CAN_MOFCR11_DLC	SET	0
CAN_MOFCR11_DLCC	SET	0
CAN_MOFCR11_FRREN	SET	0
CAN_MOFCR11_GDFS	SET	0
CAN_MOFCR11_IDC	SET	0
CAN_MOFCR11_MMC	SET	0
CAN_MOFCR11_OVIE	SET	0
CAN_MOFCR11_RMM	SET	0
CAN_MOFCR11_RXIE	SET	0
CAN_MOFCR11_SDT	SET	0
CAN_MOFCR11_STT	SET	0
CAN_MOFCR11_TXIE	SET	0

; CAN Message Object 12 Function Control Register
CAN_MOFCR12_0	SET	0
CAN_MOFCR12_0	SET	0
CAN_MOFCR12_0	SET	0
CAN_MOFCR12_0	SET	0
CAN_MOFCR12_DATC	SET	0
CAN_MOFCR12_DLC	SET	0
CAN_MOFCR12_DLCC	SET	0
CAN_MOFCR12_FRREN	SET	0
CAN_MOFCR12_GDFS	SET	0
CAN_MOFCR12_IDC	SET	0
CAN_MOFCR12_MMC	SET	0
CAN_MOFCR12_OVIE	SET	0
CAN_MOFCR12_RMM	SET	0
CAN_MOFCR12_RXIE	SET	0
CAN_MOFCR12_SDT	SET	0
CAN_MOFCR12_STT	SET	0
CAN_MOFCR12_TXIE	SET	0

; CAN Message Object 13 Function Control Register
CAN_MOFCR13_0	SET	0
CAN_MOFCR13_0	SET	0
CAN_MOFCR13_0	SET	0
CAN_MOFCR13_0	SET	0
CAN_MOFCR13_DATC	SET	0
CAN_MOFCR13_DLC	SET	0
CAN_MOFCR13_DLCC	SET	0
CAN_MOFCR13_FRREN	SET	0
CAN_MOFCR13_GDFS	SET	0
CAN_MOFCR13_IDC	SET	0
CAN_MOFCR13_MMC	SET	0
CAN_MOFCR13_OVIE	SET	0
CAN_MOFCR13_RMM	SET	0
CAN_MOFCR13_RXIE	SET	0
CAN_MOFCR13_SDT	SET	0
CAN_MOFCR13_STT	SET	0
CAN_MOFCR13_TXIE	SET	0

; CAN Message Object 14 Function Control Register
CAN_MOFCR14_0	SET	0
CAN_MOFCR14_0	SET	0
CAN_MOFCR14_0	SET	0
CAN_MOFCR14_0	SET	0
CAN_MOFCR14_DATC	SET	0
CAN_MOFCR14_DLC	SET	0
CAN_MOFCR14_DLCC	SET	0
CAN_MOFCR14_FRREN	SET	0
CAN_MOFCR14_GDFS	SET	0
CAN_MOFCR14_IDC	SET	0
CAN_MOFCR14_MMC	SET	0
CAN_MOFCR14_OVIE	SET	0
CAN_MOFCR14_RMM	SET	0
CAN_MOFCR14_RXIE	SET	0
CAN_MOFCR14_SDT	SET	0
CAN_MOFCR14_STT	SET	0
CAN_MOFCR14_TXIE	SET	0

; CAN Message Object 15 Function Control Register
CAN_MOFCR15_0	SET	0
CAN_MOFCR15_0	SET	0
CAN_MOFCR15_0	SET	0
CAN_MOFCR15_0	SET	0
CAN_MOFCR15_DATC	SET	0
CAN_MOFCR15_DLC	SET	0
CAN_MOFCR15_DLCC	SET	0
CAN_MOFCR15_FRREN	SET	0
CAN_MOFCR15_GDFS	SET	0
CAN_MOFCR15_IDC	SET	0
CAN_MOFCR15_MMC	SET	0
CAN_MOFCR15_OVIE	SET	0
CAN_MOFCR15_RMM	SET	0
CAN_MOFCR15_RXIE	SET	0
CAN_MOFCR15_SDT	SET	0
CAN_MOFCR15_STT	SET	0
CAN_MOFCR15_TXIE	SET	0

; CAN Message Object 16 Function Control Register
CAN_MOFCR16_0	SET	0
CAN_MOFCR16_0	SET	0
CAN_MOFCR16_0	SET	0
CAN_MOFCR16_0	SET	0
CAN_MOFCR16_DATC	SET	0
CAN_MOFCR16_DLC	SET	0
CAN_MOFCR16_DLCC	SET	0
CAN_MOFCR16_FRREN	SET	0
CAN_MOFCR16_GDFS	SET	0
CAN_MOFCR16_IDC	SET	0
CAN_MOFCR16_MMC	SET	0
CAN_MOFCR16_OVIE	SET	0
CAN_MOFCR16_RMM	SET	0
CAN_MOFCR16_RXIE	SET	0
CAN_MOFCR16_SDT	SET	0
CAN_MOFCR16_STT	SET	0
CAN_MOFCR16_TXIE	SET	0

; CAN Message Object 17 Function Control Register
CAN_MOFCR17_0	SET	0
CAN_MOFCR17_0	SET	0
CAN_MOFCR17_0	SET	0
CAN_MOFCR17_0	SET	0
CAN_MOFCR17_DATC	SET	0
CAN_MOFCR17_DLC	SET	0
CAN_MOFCR17_DLCC	SET	0
CAN_MOFCR17_FRREN	SET	0
CAN_MOFCR17_GDFS	SET	0
CAN_MOFCR17_IDC	SET	0
CAN_MOFCR17_MMC	SET	0
CAN_MOFCR17_OVIE	SET	0
CAN_MOFCR17_RMM	SET	0
CAN_MOFCR17_RXIE	SET	0
CAN_MOFCR17_SDT	SET	0
CAN_MOFCR17_STT	SET	0
CAN_MOFCR17_TXIE	SET	0

; CAN Message Object 18 Function Control Register
CAN_MOFCR18_0	SET	0
CAN_MOFCR18_0	SET	0
CAN_MOFCR18_0	SET	0
CAN_MOFCR18_0	SET	0
CAN_MOFCR18_DATC	SET	0
CAN_MOFCR18_DLC	SET	0
CAN_MOFCR18_DLCC	SET	0
CAN_MOFCR18_FRREN	SET	0
CAN_MOFCR18_GDFS	SET	0
CAN_MOFCR18_IDC	SET	0
CAN_MOFCR18_MMC	SET	0
CAN_MOFCR18_OVIE	SET	0
CAN_MOFCR18_RMM	SET	0
CAN_MOFCR18_RXIE	SET	0
CAN_MOFCR18_SDT	SET	0
CAN_MOFCR18_STT	SET	0
CAN_MOFCR18_TXIE	SET	0

; CAN Message Object 19 Function Control Register
CAN_MOFCR19_0	SET	0
CAN_MOFCR19_0	SET	0
CAN_MOFCR19_0	SET	0
CAN_MOFCR19_0	SET	0
CAN_MOFCR19_DATC	SET	0
CAN_MOFCR19_DLC	SET	0
CAN_MOFCR19_DLCC	SET	0
CAN_MOFCR19_FRREN	SET	0
CAN_MOFCR19_GDFS	SET	0
CAN_MOFCR19_IDC	SET	0
CAN_MOFCR19_MMC	SET	0
CAN_MOFCR19_OVIE	SET	0
CAN_MOFCR19_RMM	SET	0
CAN_MOFCR19_RXIE	SET	0
CAN_MOFCR19_SDT	SET	0
CAN_MOFCR19_STT	SET	0
CAN_MOFCR19_TXIE	SET	0

; CAN Message Object 1 Function Control Register
CAN_MOFCR1_0	SET	0
CAN_MOFCR1_0	SET	0
CAN_MOFCR1_0	SET	0
CAN_MOFCR1_0	SET	0
CAN_MOFCR1_DATC	SET	0
CAN_MOFCR1_DLC	SET	0
CAN_MOFCR1_DLCC	SET	0
CAN_MOFCR1_FRREN	SET	0
CAN_MOFCR1_GDFS	SET	0
CAN_MOFCR1_IDC	SET	0
CAN_MOFCR1_MMC	SET	0
CAN_MOFCR1_OVIE	SET	0
CAN_MOFCR1_RMM	SET	0
CAN_MOFCR1_RXIE	SET	0
CAN_MOFCR1_SDT	SET	0
CAN_MOFCR1_STT	SET	0
CAN_MOFCR1_TXIE	SET	0

; CAN Message Object 20 Function Control Register
CAN_MOFCR20_0	SET	0
CAN_MOFCR20_0	SET	0
CAN_MOFCR20_0	SET	0
CAN_MOFCR20_0	SET	0
CAN_MOFCR20_DATC	SET	0
CAN_MOFCR20_DLC	SET	0
CAN_MOFCR20_DLCC	SET	0
CAN_MOFCR20_FRREN	SET	0
CAN_MOFCR20_GDFS	SET	0
CAN_MOFCR20_IDC	SET	0
CAN_MOFCR20_MMC	SET	0
CAN_MOFCR20_OVIE	SET	0
CAN_MOFCR20_RMM	SET	0
CAN_MOFCR20_RXIE	SET	0
CAN_MOFCR20_SDT	SET	0
CAN_MOFCR20_STT	SET	0
CAN_MOFCR20_TXIE	SET	0

; CAN Message Object 21 Function Control Register
CAN_MOFCR21_0	SET	0
CAN_MOFCR21_0	SET	0
CAN_MOFCR21_0	SET	0
CAN_MOFCR21_0	SET	0
CAN_MOFCR21_DATC	SET	0
CAN_MOFCR21_DLC	SET	0
CAN_MOFCR21_DLCC	SET	0
CAN_MOFCR21_FRREN	SET	0
CAN_MOFCR21_GDFS	SET	0
CAN_MOFCR21_IDC	SET	0
CAN_MOFCR21_MMC	SET	0
CAN_MOFCR21_OVIE	SET	0
CAN_MOFCR21_RMM	SET	0
CAN_MOFCR21_RXIE	SET	0
CAN_MOFCR21_SDT	SET	0
CAN_MOFCR21_STT	SET	0
CAN_MOFCR21_TXIE	SET	0

; CAN Message Object 22 Function Control Register
CAN_MOFCR22_0	SET	0
CAN_MOFCR22_0	SET	0
CAN_MOFCR22_0	SET	0
CAN_MOFCR22_0	SET	0
CAN_MOFCR22_DATC	SET	0
CAN_MOFCR22_DLC	SET	0
CAN_MOFCR22_DLCC	SET	0
CAN_MOFCR22_FRREN	SET	0
CAN_MOFCR22_GDFS	SET	0
CAN_MOFCR22_IDC	SET	0
CAN_MOFCR22_MMC	SET	0
CAN_MOFCR22_OVIE	SET	0
CAN_MOFCR22_RMM	SET	0
CAN_MOFCR22_RXIE	SET	0
CAN_MOFCR22_SDT	SET	0
CAN_MOFCR22_STT	SET	0
CAN_MOFCR22_TXIE	SET	0

; CAN Message Object 23 Function Control Register
CAN_MOFCR23_0	SET	0
CAN_MOFCR23_0	SET	0
CAN_MOFCR23_0	SET	0
CAN_MOFCR23_0	SET	0
CAN_MOFCR23_DATC	SET	0
CAN_MOFCR23_DLC	SET	0
CAN_MOFCR23_DLCC	SET	0
CAN_MOFCR23_FRREN	SET	0
CAN_MOFCR23_GDFS	SET	0
CAN_MOFCR23_IDC	SET	0
CAN_MOFCR23_MMC	SET	0
CAN_MOFCR23_OVIE	SET	0
CAN_MOFCR23_RMM	SET	0
CAN_MOFCR23_RXIE	SET	0
CAN_MOFCR23_SDT	SET	0
CAN_MOFCR23_STT	SET	0
CAN_MOFCR23_TXIE	SET	0

; CAN Message Object 24 Function Control Register
CAN_MOFCR24_0	SET	0
CAN_MOFCR24_0	SET	0
CAN_MOFCR24_0	SET	0
CAN_MOFCR24_0	SET	0
CAN_MOFCR24_DATC	SET	0
CAN_MOFCR24_DLC	SET	0
CAN_MOFCR24_DLCC	SET	0
CAN_MOFCR24_FRREN	SET	0
CAN_MOFCR24_GDFS	SET	0
CAN_MOFCR24_IDC	SET	0
CAN_MOFCR24_MMC	SET	0
CAN_MOFCR24_OVIE	SET	0
CAN_MOFCR24_RMM	SET	0
CAN_MOFCR24_RXIE	SET	0
CAN_MOFCR24_SDT	SET	0
CAN_MOFCR24_STT	SET	0
CAN_MOFCR24_TXIE	SET	0

; CAN Message Object 25 Function Control Register
CAN_MOFCR25_0	SET	0
CAN_MOFCR25_0	SET	0
CAN_MOFCR25_0	SET	0
CAN_MOFCR25_0	SET	0
CAN_MOFCR25_DATC	SET	0
CAN_MOFCR25_DLC	SET	0
CAN_MOFCR25_DLCC	SET	0
CAN_MOFCR25_FRREN	SET	0
CAN_MOFCR25_GDFS	SET	0
CAN_MOFCR25_IDC	SET	0
CAN_MOFCR25_MMC	SET	0
CAN_MOFCR25_OVIE	SET	0
CAN_MOFCR25_RMM	SET	0
CAN_MOFCR25_RXIE	SET	0
CAN_MOFCR25_SDT	SET	0
CAN_MOFCR25_STT	SET	0
CAN_MOFCR25_TXIE	SET	0

; CAN Message Object 26 Function Control Register
CAN_MOFCR26_0	SET	0
CAN_MOFCR26_0	SET	0
CAN_MOFCR26_0	SET	0
CAN_MOFCR26_0	SET	0
CAN_MOFCR26_DATC	SET	0
CAN_MOFCR26_DLC	SET	0
CAN_MOFCR26_DLCC	SET	0
CAN_MOFCR26_FRREN	SET	0
CAN_MOFCR26_GDFS	SET	0
CAN_MOFCR26_IDC	SET	0
CAN_MOFCR26_MMC	SET	0
CAN_MOFCR26_OVIE	SET	0
CAN_MOFCR26_RMM	SET	0
CAN_MOFCR26_RXIE	SET	0
CAN_MOFCR26_SDT	SET	0
CAN_MOFCR26_STT	SET	0
CAN_MOFCR26_TXIE	SET	0

; CAN Message Object 27 Function Control Register
CAN_MOFCR27_0	SET	0
CAN_MOFCR27_0	SET	0
CAN_MOFCR27_0	SET	0
CAN_MOFCR27_0	SET	0
CAN_MOFCR27_DATC	SET	0
CAN_MOFCR27_DLC	SET	0
CAN_MOFCR27_DLCC	SET	0
CAN_MOFCR27_FRREN	SET	0
CAN_MOFCR27_GDFS	SET	0
CAN_MOFCR27_IDC	SET	0
CAN_MOFCR27_MMC	SET	0
CAN_MOFCR27_OVIE	SET	0
CAN_MOFCR27_RMM	SET	0
CAN_MOFCR27_RXIE	SET	0
CAN_MOFCR27_SDT	SET	0
CAN_MOFCR27_STT	SET	0
CAN_MOFCR27_TXIE	SET	0

; CAN Message Object 28 Function Control Register
CAN_MOFCR28_0	SET	0
CAN_MOFCR28_0	SET	0
CAN_MOFCR28_0	SET	0
CAN_MOFCR28_0	SET	0
CAN_MOFCR28_DATC	SET	0
CAN_MOFCR28_DLC	SET	0
CAN_MOFCR28_DLCC	SET	0
CAN_MOFCR28_FRREN	SET	0
CAN_MOFCR28_GDFS	SET	0
CAN_MOFCR28_IDC	SET	0
CAN_MOFCR28_MMC	SET	0
CAN_MOFCR28_OVIE	SET	0
CAN_MOFCR28_RMM	SET	0
CAN_MOFCR28_RXIE	SET	0
CAN_MOFCR28_SDT	SET	0
CAN_MOFCR28_STT	SET	0
CAN_MOFCR28_TXIE	SET	0

; CAN Message Object 29 Function Control Register
CAN_MOFCR29_0	SET	0
CAN_MOFCR29_0	SET	0
CAN_MOFCR29_0	SET	0
CAN_MOFCR29_0	SET	0
CAN_MOFCR29_DATC	SET	0
CAN_MOFCR29_DLC	SET	0
CAN_MOFCR29_DLCC	SET	0
CAN_MOFCR29_FRREN	SET	0
CAN_MOFCR29_GDFS	SET	0
CAN_MOFCR29_IDC	SET	0
CAN_MOFCR29_MMC	SET	0
CAN_MOFCR29_OVIE	SET	0
CAN_MOFCR29_RMM	SET	0
CAN_MOFCR29_RXIE	SET	0
CAN_MOFCR29_SDT	SET	0
CAN_MOFCR29_STT	SET	0
CAN_MOFCR29_TXIE	SET	0

; CAN Message Object 2 Function Control Register
CAN_MOFCR2_0	SET	0
CAN_MOFCR2_0	SET	0
CAN_MOFCR2_0	SET	0
CAN_MOFCR2_0	SET	0
CAN_MOFCR2_DATC	SET	0
CAN_MOFCR2_DLC	SET	0
CAN_MOFCR2_DLCC	SET	0
CAN_MOFCR2_FRREN	SET	0
CAN_MOFCR2_GDFS	SET	0
CAN_MOFCR2_IDC	SET	0
CAN_MOFCR2_MMC	SET	0
CAN_MOFCR2_OVIE	SET	0
CAN_MOFCR2_RMM	SET	0
CAN_MOFCR2_RXIE	SET	0
CAN_MOFCR2_SDT	SET	0
CAN_MOFCR2_STT	SET	0
CAN_MOFCR2_TXIE	SET	0

; CAN Message Object 30 Function Control Register
CAN_MOFCR30_0	SET	0
CAN_MOFCR30_0	SET	0
CAN_MOFCR30_0	SET	0
CAN_MOFCR30_0	SET	0
CAN_MOFCR30_DATC	SET	0
CAN_MOFCR30_DLC	SET	0
CAN_MOFCR30_DLCC	SET	0
CAN_MOFCR30_FRREN	SET	0
CAN_MOFCR30_GDFS	SET	0
CAN_MOFCR30_IDC	SET	0
CAN_MOFCR30_MMC	SET	0
CAN_MOFCR30_OVIE	SET	0
CAN_MOFCR30_RMM	SET	0
CAN_MOFCR30_RXIE	SET	0
CAN_MOFCR30_SDT	SET	0
CAN_MOFCR30_STT	SET	0
CAN_MOFCR30_TXIE	SET	0

; CAN Message Object 31 Function Control Register
CAN_MOFCR31_0	SET	0
CAN_MOFCR31_0	SET	0
CAN_MOFCR31_0	SET	0
CAN_MOFCR31_0	SET	0
CAN_MOFCR31_DATC	SET	0
CAN_MOFCR31_DLC	SET	0
CAN_MOFCR31_DLCC	SET	0
CAN_MOFCR31_FRREN	SET	0
CAN_MOFCR31_GDFS	SET	0
CAN_MOFCR31_IDC	SET	0
CAN_MOFCR31_MMC	SET	0
CAN_MOFCR31_OVIE	SET	0
CAN_MOFCR31_RMM	SET	0
CAN_MOFCR31_RXIE	SET	0
CAN_MOFCR31_SDT	SET	0
CAN_MOFCR31_STT	SET	0
CAN_MOFCR31_TXIE	SET	0

; CAN Message Object 3 Function Control Register
CAN_MOFCR3_0	SET	0
CAN_MOFCR3_0	SET	0
CAN_MOFCR3_0	SET	0
CAN_MOFCR3_0	SET	0
CAN_MOFCR3_DATC	SET	0
CAN_MOFCR3_DLC	SET	8
CAN_MOFCR3_DLCC	SET	0
CAN_MOFCR3_FRREN	SET	0
CAN_MOFCR3_GDFS	SET	0
CAN_MOFCR3_IDC	SET	0
CAN_MOFCR3_MMC	SET	0
CAN_MOFCR3_OVIE	SET	0
CAN_MOFCR3_RMM	SET	0
CAN_MOFCR3_RXIE	SET	0
CAN_MOFCR3_SDT	SET	0
CAN_MOFCR3_STT	SET	0
CAN_MOFCR3_TXIE	SET	0

; CAN Message Object 4 Function Control Register
CAN_MOFCR4_0	SET	0
CAN_MOFCR4_0	SET	0
CAN_MOFCR4_0	SET	0
CAN_MOFCR4_0	SET	0
CAN_MOFCR4_DATC	SET	0
CAN_MOFCR4_DLC	SET	0
CAN_MOFCR4_DLCC	SET	0
CAN_MOFCR4_FRREN	SET	0
CAN_MOFCR4_GDFS	SET	0
CAN_MOFCR4_IDC	SET	0
CAN_MOFCR4_MMC	SET	0
CAN_MOFCR4_OVIE	SET	0
CAN_MOFCR4_RMM	SET	0
CAN_MOFCR4_RXIE	SET	0
CAN_MOFCR4_SDT	SET	0
CAN_MOFCR4_STT	SET	0
CAN_MOFCR4_TXIE	SET	0

; CAN Message Object 5 Function Control Register
CAN_MOFCR5_0	SET	0
CAN_MOFCR5_0	SET	0
CAN_MOFCR5_0	SET	0
CAN_MOFCR5_0	SET	0
CAN_MOFCR5_DATC	SET	0
CAN_MOFCR5_DLC	SET	0
CAN_MOFCR5_DLCC	SET	0
CAN_MOFCR5_FRREN	SET	0
CAN_MOFCR5_GDFS	SET	0
CAN_MOFCR5_IDC	SET	0
CAN_MOFCR5_MMC	SET	0
CAN_MOFCR5_OVIE	SET	0
CAN_MOFCR5_RMM	SET	0
CAN_MOFCR5_RXIE	SET	0
CAN_MOFCR5_SDT	SET	0
CAN_MOFCR5_STT	SET	0
CAN_MOFCR5_TXIE	SET	0

; CAN Message Object 6 Function Control Register
CAN_MOFCR6_0	SET	0
CAN_MOFCR6_0	SET	0
CAN_MOFCR6_0	SET	0
CAN_MOFCR6_0	SET	0
CAN_MOFCR6_DATC	SET	0
CAN_MOFCR6_DLC	SET	0
CAN_MOFCR6_DLCC	SET	0
CAN_MOFCR6_FRREN	SET	0
CAN_MOFCR6_GDFS	SET	0
CAN_MOFCR6_IDC	SET	0
CAN_MOFCR6_MMC	SET	0
CAN_MOFCR6_OVIE	SET	0
CAN_MOFCR6_RMM	SET	0
CAN_MOFCR6_RXIE	SET	0
CAN_MOFCR6_SDT	SET	0
CAN_MOFCR6_STT	SET	0
CAN_MOFCR6_TXIE	SET	0

; CAN Message Object 7 Function Control Register
CAN_MOFCR7_0	SET	0
CAN_MOFCR7_0	SET	0
CAN_MOFCR7_0	SET	0
CAN_MOFCR7_0	SET	0
CAN_MOFCR7_DATC	SET	0
CAN_MOFCR7_DLC	SET	0
CAN_MOFCR7_DLCC	SET	0
CAN_MOFCR7_FRREN	SET	0
CAN_MOFCR7_GDFS	SET	0
CAN_MOFCR7_IDC	SET	0
CAN_MOFCR7_MMC	SET	0
CAN_MOFCR7_OVIE	SET	0
CAN_MOFCR7_RMM	SET	0
CAN_MOFCR7_RXIE	SET	0
CAN_MOFCR7_SDT	SET	0
CAN_MOFCR7_STT	SET	0
CAN_MOFCR7_TXIE	SET	0

; CAN Message Object 8 Function Control Register
CAN_MOFCR8_0	SET	0
CAN_MOFCR8_0	SET	0
CAN_MOFCR8_0	SET	0
CAN_MOFCR8_0	SET	0
CAN_MOFCR8_DATC	SET	0
CAN_MOFCR8_DLC	SET	0
CAN_MOFCR8_DLCC	SET	0
CAN_MOFCR8_FRREN	SET	0
CAN_MOFCR8_GDFS	SET	0
CAN_MOFCR8_IDC	SET	0
CAN_MOFCR8_MMC	SET	0
CAN_MOFCR8_OVIE	SET	0
CAN_MOFCR8_RMM	SET	0
CAN_MOFCR8_RXIE	SET	0
CAN_MOFCR8_SDT	SET	0
CAN_MOFCR8_STT	SET	0
CAN_MOFCR8_TXIE	SET	0

; CAN Message Object 9 Function Control Register
CAN_MOFCR9_0	SET	0
CAN_MOFCR9_0	SET	0
CAN_MOFCR9_0	SET	0
CAN_MOFCR9_0	SET	0
CAN_MOFCR9_DATC	SET	0
CAN_MOFCR9_DLC	SET	0
CAN_MOFCR9_DLCC	SET	0
CAN_MOFCR9_FRREN	SET	0
CAN_MOFCR9_GDFS	SET	0
CAN_MOFCR9_IDC	SET	0
CAN_MOFCR9_MMC	SET	0
CAN_MOFCR9_OVIE	SET	0
CAN_MOFCR9_RMM	SET	0
CAN_MOFCR9_RXIE	SET	0
CAN_MOFCR9_SDT	SET	0
CAN_MOFCR9_STT	SET	0
CAN_MOFCR9_TXIE	SET	0

; CAN Message Object 0 FIFO/Gateway Pointer Register
CAN_MOFGPR0_BOT	SET	0
CAN_MOFGPR0_CUR	SET	0
CAN_MOFGPR0_SEL	SET	0
CAN_MOFGPR0_TOP	SET	2

; CAN Message Object 10 FIFO/Gateway Pointer Register
CAN_MOFGPR10_BOT	SET	255
CAN_MOFGPR10_CUR	SET	255
CAN_MOFGPR10_SEL	SET	255
CAN_MOFGPR10_TOP	SET	255

; CAN Message Object 11 FIFO/Gateway Pointer Register
CAN_MOFGPR11_BOT	SET	255
CAN_MOFGPR11_CUR	SET	255
CAN_MOFGPR11_SEL	SET	255
CAN_MOFGPR11_TOP	SET	255

; CAN Message Object 12 FIFO/Gateway Pointer Register
CAN_MOFGPR12_BOT	SET	255
CAN_MOFGPR12_CUR	SET	255
CAN_MOFGPR12_SEL	SET	255
CAN_MOFGPR12_TOP	SET	255

; CAN Message Object 13 FIFO/Gateway Pointer Register
CAN_MOFGPR13_BOT	SET	255
CAN_MOFGPR13_CUR	SET	255
CAN_MOFGPR13_SEL	SET	255
CAN_MOFGPR13_TOP	SET	255

; CAN Message Object 14 FIFO/Gateway Pointer Register
CAN_MOFGPR14_BOT	SET	255
CAN_MOFGPR14_CUR	SET	255
CAN_MOFGPR14_SEL	SET	255
CAN_MOFGPR14_TOP	SET	255

; CAN Message Object 15 FIFO/Gateway Pointer Register
CAN_MOFGPR15_BOT	SET	255
CAN_MOFGPR15_CUR	SET	255
CAN_MOFGPR15_SEL	SET	255
CAN_MOFGPR15_TOP	SET	255

; CAN Message Object 16 FIFO/Gateway Pointer Register
CAN_MOFGPR16_BOT	SET	255
CAN_MOFGPR16_CUR	SET	255
CAN_MOFGPR16_SEL	SET	255
CAN_MOFGPR16_TOP	SET	255

; CAN Message Object 17 FIFO/Gateway Pointer Register
CAN_MOFGPR17_BOT	SET	255
CAN_MOFGPR17_CUR	SET	255
CAN_MOFGPR17_SEL	SET	255
CAN_MOFGPR17_TOP	SET	255

; CAN Message Object 18 FIFO/Gateway Pointer Register
CAN_MOFGPR18_BOT	SET	255
CAN_MOFGPR18_CUR	SET	255
CAN_MOFGPR18_SEL	SET	255
CAN_MOFGPR18_TOP	SET	255

; CAN Message Object 19 FIFO/Gateway Pointer Register
CAN_MOFGPR19_BOT	SET	255
CAN_MOFGPR19_CUR	SET	255
CAN_MOFGPR19_SEL	SET	255
CAN_MOFGPR19_TOP	SET	255

; CAN Message Object 1 FIFO/Gateway Pointer Register
CAN_MOFGPR1_BOT	SET	255
CAN_MOFGPR1_CUR	SET	255
CAN_MOFGPR1_SEL	SET	255
CAN_MOFGPR1_TOP	SET	255

; CAN Message Object 20 FIFO/Gateway Pointer Register
CAN_MOFGPR20_BOT	SET	255
CAN_MOFGPR20_CUR	SET	255
CAN_MOFGPR20_SEL	SET	255
CAN_MOFGPR20_TOP	SET	255

; CAN Message Object 21 FIFO/Gateway Pointer Register
CAN_MOFGPR21_BOT	SET	255
CAN_MOFGPR21_CUR	SET	255
CAN_MOFGPR21_SEL	SET	255
CAN_MOFGPR21_TOP	SET	255

; CAN Message Object 22 FIFO/Gateway Pointer Register
CAN_MOFGPR22_BOT	SET	255
CAN_MOFGPR22_CUR	SET	255
CAN_MOFGPR22_SEL	SET	255
CAN_MOFGPR22_TOP	SET	255

; CAN Message Object 23 FIFO/Gateway Pointer Register
CAN_MOFGPR23_BOT	SET	255
CAN_MOFGPR23_CUR	SET	255
CAN_MOFGPR23_SEL	SET	255
CAN_MOFGPR23_TOP	SET	255

; CAN Message Object 24 FIFO/Gateway Pointer Register
CAN_MOFGPR24_BOT	SET	255
CAN_MOFGPR24_CUR	SET	255
CAN_MOFGPR24_SEL	SET	255
CAN_MOFGPR24_TOP	SET	255

; CAN Message Object 25 FIFO/Gateway Pointer Register
CAN_MOFGPR25_BOT	SET	255
CAN_MOFGPR25_CUR	SET	255
CAN_MOFGPR25_SEL	SET	255
CAN_MOFGPR25_TOP	SET	255

; CAN Message Object 26 FIFO/Gateway Pointer Register
CAN_MOFGPR26_BOT	SET	255
CAN_MOFGPR26_CUR	SET	255
CAN_MOFGPR26_SEL	SET	255
CAN_MOFGPR26_TOP	SET	255

; CAN Message Object 27 FIFO/Gateway Pointer Register
CAN_MOFGPR27_BOT	SET	255
CAN_MOFGPR27_CUR	SET	255
CAN_MOFGPR27_SEL	SET	255
CAN_MOFGPR27_TOP	SET	255

; CAN Message Object 28 FIFO/Gateway Pointer Register
CAN_MOFGPR28_BOT	SET	255
CAN_MOFGPR28_CUR	SET	255
CAN_MOFGPR28_SEL	SET	255
CAN_MOFGPR28_TOP	SET	255

; CAN Message Object 29 FIFO/Gateway Pointer Register
CAN_MOFGPR29_BOT	SET	255
CAN_MOFGPR29_CUR	SET	255
CAN_MOFGPR29_SEL	SET	255
CAN_MOFGPR29_TOP	SET	255

; CAN Message Object 2 FIFO/Gateway Pointer Register
CAN_MOFGPR2_BOT	SET	255
CAN_MOFGPR2_CUR	SET	255
CAN_MOFGPR2_SEL	SET	255
CAN_MOFGPR2_TOP	SET	255

; CAN Message Object 30 FIFO/Gateway Pointer Register
CAN_MOFGPR30_BOT	SET	255
CAN_MOFGPR30_CUR	SET	255
CAN_MOFGPR30_SEL	SET	255
CAN_MOFGPR30_TOP	SET	255

; CAN Message Object 31 FIFO/Gateway Pointer Register
CAN_MOFGPR31_BOT	SET	255
CAN_MOFGPR31_CUR	SET	255
CAN_MOFGPR31_SEL	SET	255
CAN_MOFGPR31_TOP	SET	255

; CAN Message Object 3 FIFO/Gateway Pointer Register
CAN_MOFGPR3_BOT	SET	255
CAN_MOFGPR3_CUR	SET	255
CAN_MOFGPR3_SEL	SET	255
CAN_MOFGPR3_TOP	SET	255

; CAN Message Object 4 FIFO/Gateway Pointer Register
CAN_MOFGPR4_BOT	SET	255
CAN_MOFGPR4_CUR	SET	255
CAN_MOFGPR4_SEL	SET	255
CAN_MOFGPR4_TOP	SET	255

; CAN Message Object 5 FIFO/Gateway Pointer Register
CAN_MOFGPR5_BOT	SET	255
CAN_MOFGPR5_CUR	SET	255
CAN_MOFGPR5_SEL	SET	255
CAN_MOFGPR5_TOP	SET	255

; CAN Message Object 6 FIFO/Gateway Pointer Register
CAN_MOFGPR6_BOT	SET	255
CAN_MOFGPR6_CUR	SET	255
CAN_MOFGPR6_SEL	SET	255
CAN_MOFGPR6_TOP	SET	255

; CAN Message Object 7 FIFO/Gateway Pointer Register
CAN_MOFGPR7_BOT	SET	255
CAN_MOFGPR7_CUR	SET	255
CAN_MOFGPR7_SEL	SET	255
CAN_MOFGPR7_TOP	SET	255

; CAN Message Object 8 FIFO/Gateway Pointer Register
CAN_MOFGPR8_BOT	SET	255
CAN_MOFGPR8_CUR	SET	255
CAN_MOFGPR8_SEL	SET	255
CAN_MOFGPR8_TOP	SET	255

; CAN Message Object 9 FIFO/Gateway Pointer Register
CAN_MOFGPR9_BOT	SET	255
CAN_MOFGPR9_CUR	SET	255
CAN_MOFGPR9_SEL	SET	255
CAN_MOFGPR9_TOP	SET	255

; CAN Message Object 0 Interrupt Pointer Register
CAN_MOIPR0_CFCVAL	SET	0
CAN_MOIPR0_MPN	SET	0
CAN_MOIPR0_RXINP	SET	0
CAN_MOIPR0_TXINP	SET	0

; CAN Message Object 10 Interrupt Pointer Register
CAN_MOIPR10_CFCVAL	SET	0
CAN_MOIPR10_MPN	SET	10
CAN_MOIPR10_RXINP	SET	0
CAN_MOIPR10_TXINP	SET	0

; CAN Message Object 11 Interrupt Pointer Register
CAN_MOIPR11_CFCVAL	SET	0
CAN_MOIPR11_MPN	SET	11
CAN_MOIPR11_RXINP	SET	0
CAN_MOIPR11_TXINP	SET	0

; CAN Message Object 12 Interrupt Pointer Register
CAN_MOIPR12_CFCVAL	SET	0
CAN_MOIPR12_MPN	SET	12
CAN_MOIPR12_RXINP	SET	0
CAN_MOIPR12_TXINP	SET	0

; CAN Message Object 13 Interrupt Pointer Register
CAN_MOIPR13_CFCVAL	SET	0
CAN_MOIPR13_MPN	SET	13
CAN_MOIPR13_RXINP	SET	0
CAN_MOIPR13_TXINP	SET	0

; CAN Message Object 14 Interrupt Pointer Register
CAN_MOIPR14_CFCVAL	SET	0
CAN_MOIPR14_MPN	SET	14
CAN_MOIPR14_RXINP	SET	0
CAN_MOIPR14_TXINP	SET	0

; CAN Message Object 15 Interrupt Pointer Register
CAN_MOIPR15_CFCVAL	SET	0
CAN_MOIPR15_MPN	SET	15
CAN_MOIPR15_RXINP	SET	0
CAN_MOIPR15_TXINP	SET	0

; CAN Message Object 16 Interrupt Pointer Register
CAN_MOIPR16_CFCVAL	SET	0
CAN_MOIPR16_MPN	SET	16
CAN_MOIPR16_RXINP	SET	0
CAN_MOIPR16_TXINP	SET	0

; CAN Message Object 17 Interrupt Pointer Register
CAN_MOIPR17_CFCVAL	SET	0
CAN_MOIPR17_MPN	SET	17
CAN_MOIPR17_RXINP	SET	0
CAN_MOIPR17_TXINP	SET	0

; CAN Message Object 18 Interrupt Pointer Register
CAN_MOIPR18_CFCVAL	SET	0
CAN_MOIPR18_MPN	SET	18
CAN_MOIPR18_RXINP	SET	0
CAN_MOIPR18_TXINP	SET	0

; CAN Message Object 19 Interrupt Pointer Register
CAN_MOIPR19_CFCVAL	SET	0
CAN_MOIPR19_MPN	SET	19
CAN_MOIPR19_RXINP	SET	0
CAN_MOIPR19_TXINP	SET	0

; CAN Message Object 1 Interrupt Pointer Register
CAN_MOIPR1_CFCVAL	SET	0
CAN_MOIPR1_MPN	SET	1
CAN_MOIPR1_RXINP	SET	0
CAN_MOIPR1_TXINP	SET	0

; CAN Message Object 20 Interrupt Pointer Register
CAN_MOIPR20_CFCVAL	SET	0
CAN_MOIPR20_MPN	SET	20
CAN_MOIPR20_RXINP	SET	0
CAN_MOIPR20_TXINP	SET	0

; CAN Message Object 21 Interrupt Pointer Register
CAN_MOIPR21_CFCVAL	SET	0
CAN_MOIPR21_MPN	SET	21
CAN_MOIPR21_RXINP	SET	0
CAN_MOIPR21_TXINP	SET	0

; CAN Message Object 22 Interrupt Pointer Register
CAN_MOIPR22_CFCVAL	SET	0
CAN_MOIPR22_MPN	SET	22
CAN_MOIPR22_RXINP	SET	0
CAN_MOIPR22_TXINP	SET	0

; CAN Message Object 23 Interrupt Pointer Register
CAN_MOIPR23_CFCVAL	SET	0
CAN_MOIPR23_MPN	SET	23
CAN_MOIPR23_RXINP	SET	0
CAN_MOIPR23_TXINP	SET	0

; CAN Message Object 24 Interrupt Pointer Register
CAN_MOIPR24_CFCVAL	SET	0
CAN_MOIPR24_MPN	SET	24
CAN_MOIPR24_RXINP	SET	0
CAN_MOIPR24_TXINP	SET	0

; CAN Message Object 25 Interrupt Pointer Register
CAN_MOIPR25_CFCVAL	SET	0
CAN_MOIPR25_MPN	SET	25
CAN_MOIPR25_RXINP	SET	0
CAN_MOIPR25_TXINP	SET	0

; CAN Message Object 26 Interrupt Pointer Register
CAN_MOIPR26_CFCVAL	SET	0
CAN_MOIPR26_MPN	SET	26
CAN_MOIPR26_RXINP	SET	0
CAN_MOIPR26_TXINP	SET	0

; CAN Message Object 27 Interrupt Pointer Register
CAN_MOIPR27_CFCVAL	SET	0
CAN_MOIPR27_MPN	SET	27
CAN_MOIPR27_RXINP	SET	0
CAN_MOIPR27_TXINP	SET	0

; CAN Message Object 28 Interrupt Pointer Register
CAN_MOIPR28_CFCVAL	SET	0
CAN_MOIPR28_MPN	SET	28
CAN_MOIPR28_RXINP	SET	0
CAN_MOIPR28_TXINP	SET	0

; CAN Message Object 29 Interrupt Pointer Register
CAN_MOIPR29_CFCVAL	SET	0
CAN_MOIPR29_MPN	SET	29
CAN_MOIPR29_RXINP	SET	0
CAN_MOIPR29_TXINP	SET	0

; CAN Message Object 2 Interrupt Pointer Register
CAN_MOIPR2_CFCVAL	SET	0
CAN_MOIPR2_MPN	SET	2
CAN_MOIPR2_RXINP	SET	0
CAN_MOIPR2_TXINP	SET	0

; CAN Message Object 30 Interrupt Pointer Register
CAN_MOIPR30_CFCVAL	SET	0
CAN_MOIPR30_MPN	SET	30
CAN_MOIPR30_RXINP	SET	0
CAN_MOIPR30_TXINP	SET	0

; CAN Message Object 31 Interrupt Pointer Register
CAN_MOIPR31_CFCVAL	SET	0
CAN_MOIPR31_MPN	SET	31
CAN_MOIPR31_RXINP	SET	0
CAN_MOIPR31_TXINP	SET	0

; CAN Message Object 3 Interrupt Pointer Register
CAN_MOIPR3_CFCVAL	SET	0
CAN_MOIPR3_MPN	SET	3
CAN_MOIPR3_RXINP	SET	0
CAN_MOIPR3_TXINP	SET	0

; CAN Message Object 4 Interrupt Pointer Register
CAN_MOIPR4_CFCVAL	SET	0
CAN_MOIPR4_MPN	SET	4
CAN_MOIPR4_RXINP	SET	0
CAN_MOIPR4_TXINP	SET	0

; CAN Message Object 5 Interrupt Pointer Register
CAN_MOIPR5_CFCVAL	SET	0
CAN_MOIPR5_MPN	SET	5
CAN_MOIPR5_RXINP	SET	0
CAN_MOIPR5_TXINP	SET	0

; CAN Message Object 6 Interrupt Pointer Register
CAN_MOIPR6_CFCVAL	SET	0
CAN_MOIPR6_MPN	SET	6
CAN_MOIPR6_RXINP	SET	0
CAN_MOIPR6_TXINP	SET	0

; CAN Message Object 7 Interrupt Pointer Register
CAN_MOIPR7_CFCVAL	SET	0
CAN_MOIPR7_MPN	SET	7
CAN_MOIPR7_RXINP	SET	0
CAN_MOIPR7_TXINP	SET	0

; CAN Message Object 8 Interrupt Pointer Register
CAN_MOIPR8_CFCVAL	SET	0
CAN_MOIPR8_MPN	SET	8
CAN_MOIPR8_RXINP	SET	0
CAN_MOIPR8_TXINP	SET	0

; CAN Message Object 9 Interrupt Pointer Register
CAN_MOIPR9_CFCVAL	SET	0
CAN_MOIPR9_MPN	SET	9
CAN_MOIPR9_RXINP	SET	0
CAN_MOIPR9_TXINP	SET	0

; Message Index Register 0
CAN_MSID0_INDEX	SET	0

; Message Index Register 1
CAN_MSID1_INDEX	SET	0

; Message Index Mask Register
CAN_MSIMASK_IM	SET	0

; Message Pending Register 0
CAN_MSPND0_PND	SET	0

; Message Pending Register 1
CAN_MSPND1_PND	SET	0

; Node 0 Bit Timing Register
CAN_NBTR0_0	SET	0
CAN_NBTR0_BRP	SET	47
CAN_NBTR0_DIV8	SET	0
CAN_NBTR0_SJW	SET	1
CAN_NBTR0_TSEG1	SET	4
CAN_NBTR0_TSEG2	SET	3

; Node 1 Bit Timing Register
CAN_NBTR1_0	SET	0
CAN_NBTR1_BRP	SET	63
CAN_NBTR1_DIV8	SET	0
CAN_NBTR1_SJW	SET	1
CAN_NBTR1_TSEG1	SET	8
CAN_NBTR1_TSEG2	SET	1

; Node 0 Control Register
CAN_NCR0_0	SET	0
CAN_NCR0_ALIE	SET	0
CAN_NCR0_CALM	SET	0
CAN_NCR0_CANDIS	SET	0
CAN_NCR0_CCE	SET	0
CAN_NCR0_INIT	SET	0
CAN_NCR0_LECIE	SET	0
CAN_NCR0_TRIE	SET	0

; Node 1 Control Register
CAN_NCR1_0	SET	0
CAN_NCR1_0	SET	0
CAN_NCR1_ALIE	SET	0
CAN_NCR1_CALM	SET	0
CAN_NCR1_CANDIS	SET	0
CAN_NCR1_CCE	SET	0
CAN_NCR1_INIT	SET	1
CAN_NCR1_LECIE	SET	0
CAN_NCR1_TRIE	SET	0

; Node 0 Error Counter Register
CAN_NECNT0_0	SET	0
CAN_NECNT0_EWRNLVL	SET	96
CAN_NECNT0_LEINC	SET	0
CAN_NECNT0_LETD	SET	0
CAN_NECNT0_REC	SET	0
CAN_NECNT0_TEC	SET	0

; Node 1 Error Counter Register
CAN_NECNT1_0	SET	0
CAN_NECNT1_EWRNLVL	SET	96
CAN_NECNT1_LEINC	SET	0
CAN_NECNT1_LETD	SET	0
CAN_NECNT1_REC	SET	0
CAN_NECNT1_TEC	SET	0

; Node 0 Frame Counter Register
CAN_NFCR0_0	SET	0
CAN_NFCR0_0	SET	0
CAN_NFCR0_CFC	SET	0
CAN_NFCR0_CFCIE	SET	0
CAN_NFCR0_CFCOV	SET	0
CAN_NFCR0_CFMOD	SET	0
CAN_NFCR0_CFSEL	SET	0

; Node 1 Frame Counter Register
CAN_NFCR1_0	SET	0
CAN_NFCR1_0	SET	0
CAN_NFCR1_CFC	SET	0
CAN_NFCR1_CFCIE	SET	0
CAN_NFCR1_CFCOV	SET	0
CAN_NFCR1_CFMOD	SET	0
CAN_NFCR1_CFSEL	SET	0

; Node 0 Interrupt Pointer Register
CAN_NIPR0_0	SET	0
CAN_NIPR0_ALINP	SET	0
CAN_NIPR0_CFCINP	SET	0
CAN_NIPR0_LECINP	SET	0
CAN_NIPR0_TRINP	SET	0

; Node 1 Interrupt Pointer Register
CAN_NIPR1_0	SET	0
CAN_NIPR1_ALINP	SET	0
CAN_NIPR1_CFCINP	SET	0
CAN_NIPR1_LECINP	SET	0
CAN_NIPR1_TRINP	SET	0

; Node 0 Port Control Register
CAN_NPCR0_0	SET	0
CAN_NPCR0_0	SET	0
CAN_NPCR0_LBM	SET	0
CAN_NPCR0_RXSEL	SET	0

; Node 1 Port Control Register
CAN_NPCR1_0	SET	0
CAN_NPCR1_0	SET	0
CAN_NPCR1_LBM	SET	0
CAN_NPCR1_RXSEL	SET	0

; Node 0 Status Register
CAN_NSR0_0	SET	0
CAN_NSR0_ALERT	SET	0
CAN_NSR0_BOFF	SET	0
CAN_NSR0_EWRN	SET	0
CAN_NSR0_LEC	SET	0
CAN_NSR0_LLE	SET	0
CAN_NSR0_LOE	SET	0
CAN_NSR0_RXOK	SET	0
CAN_NSR0_TXOK	SET	0

; Node 1 Status Register
CAN_NSR1_0	SET	0
CAN_NSR1_ALERT	SET	0
CAN_NSR1_BOFF	SET	0
CAN_NSR1_EWRN	SET	0
CAN_NSR1_LEC	SET	0
CAN_NSR1_LLE	SET	0
CAN_NSR1_LOE	SET	0
CAN_NSR1_RXOK	SET	0
CAN_NSR1_TXOK	SET	0

; Panel Control Register
CAN_PANCTR_0	SET	0
CAN_PANCTR_BUSY	SET	0
CAN_PANCTR_PANAR1	SET	0
CAN_PANCTR_PANAR2	SET	0
CAN_PANCTR_PANCMD	SET	0
CAN_PANCTR_RBUSY	SET	0

; RMAP=0 PAGE=1-CCU6 Capture/Compare Register for Channel CC60 High
CCU6_CC60RH_CC60VH	SET	0

; RMAP=0 PAGE=1-CCU6 Capture/Compare Register for Channel CC60 Low
CCU6_CC60RL_CC60VL	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Shadow Register for Channel CC60 High
CCU6_CC60SRH_CC60SH	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Shadow Register for Channel CC60 Low
CCU6_CC60SRL_CC60SL	SET	0

; RMAP=0 PAGE=1-CCU6 Capture/Compare Register for Channel CC61 High
CCU6_CC61RH_CC61VH	SET	0

; RMAP=0 PAGE=1-CCU6 Capture/Compare Register for Channel CC61 Low
CCU6_CC61RL_CC61VL	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Shadow Register for Channel CC61 High
CCU6_CC61SRH_CC61SH	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Shadow Register for Channel CC61 Low
CCU6_CC61SRL_CC61SL	SET	0

; RMAP=0 PAGE=1-CCU6 Capture/Compare Register for Channel CC62 High
CCU6_CC62RH_CC62VH	SET	0

; RMAP=0 PAGE=1-CCU6 Capture/Compare Register for Channel CC62 Low
CCU6_CC62RL_CC62VL	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Shadow Register for Channel CC62 High
CCU6_CC62SRH_CC62SH	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Shadow Register for Channel CC62 Low
CCU6_CC62SRL_CC62SL	SET	0

; RMAP=0 PAGE=1-CCU6 Capture/Compare Register for Channel CC63 High
CCU6_CC63RH_CC63VH	SET	0

; RMAP=0 PAGE=1-CCU6 Capture/Compare Register for Channel CC63 Low
CCU6_CC63RL_CC63VL	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Shadow Register for Channel CC63 High
CCU6_CC63SRH_CC63SH	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Shadow Register for Channel CC63 Low
CCU6_CC63SRL_CC63SL	SET	0

; RMAP=0 PAGE=0-CCU6 Compare State Modification Register High
CCU6_CMPMODIFH_MCC60R	SET	0
CCU6_CMPMODIFH_MCC61R	SET	0
CCU6_CMPMODIFH_MCC62R	SET	0
CCU6_CMPMODIFH_MCC63R	SET	0

; RMAP=0 PAGE=0-CCU6 Compare State Modification Register Low
CCU6_CMPMODIFL_MCC60S	SET	0
CCU6_CMPMODIFL_MCC61S	SET	0
CCU6_CMPMODIFL_MCC62S	SET	0
CCU6_CMPMODIFL_MCC63S	SET	0

; RMAP=0 PAGE=3-CCU6 Compare State Register High
CCU6_CMPSTATH_CC60PS	SET	0
CCU6_CMPSTATH_CC61PS	SET	0
CCU6_CMPSTATH_CC62PS	SET	0
CCU6_CMPSTATH_COUT60PS	SET	0
CCU6_CMPSTATH_COUT61PS	SET	0
CCU6_CMPSTATH_COUT62PS	SET	0
CCU6_CMPSTATH_COUT63PS	SET	0
CCU6_CMPSTATH_T13IM	SET	0

; RMAP=0 PAGE=3-CCU6 Compare State Register Low
CCU6_CMPSTATL_CC60ST	SET	0
CCU6_CMPSTATL_CC61ST	SET	0
CCU6_CMPSTATL_CC62ST	SET	0
CCU6_CMPSTATL_CC63ST	SET	0
CCU6_CMPSTATL_CCPOS0	SET	0
CCU6_CMPSTATL_CCPOS1	SET	0
CCU6_CMPSTATL_CCPOS2	SET	0

; RMAP=0 PAGE=2-CCU6 Capture/Compare Interrupt Enable Register High
CCU6_IENH_ENCHE	SET	0
CCU6_IENH_ENIDLE	SET	0
CCU6_IENH_ENSTR	SET	0
CCU6_IENH_ENT13CM	SET	0
CCU6_IENH_ENT13PM	SET	0
CCU6_IENH_ENTRPF	SET	0
CCU6_IENH_ENWHE	SET	0

; RMAP=0 PAGE=2-CCU6 Capture/Compare Interrupt Enable Register Low
CCU6_IENL_ENCC60F	SET	0
CCU6_IENL_ENCC60R	SET	0
CCU6_IENL_ENCC61F	SET	0
CCU6_IENL_ENCC61R	SET	0
CCU6_IENL_ENCC62F	SET	0
CCU6_IENL_ENCC62R	SET	0
CCU6_IENL_ENT12OM	SET	0
CCU6_IENL_ENT12PM	SET	0

; RMAP=0 PAGE=2-CCU6 Capture/Compare Interrupt Node Pointer Register High
CCU6_INPH_INPERR	SET	0
CCU6_INPH_INPT12	SET	0
CCU6_INPH_INPT13	SET	0

; RMAP=0 PAGE=2-CCU6 Capture/Compare Interrupt Node Pointer Register Low
CCU6_INPL_INPCC60	SET	0
CCU6_INPL_INPCC61	SET	0
CCU6_INPL_INPCC62	SET	0
CCU6_INPL_INPCHE	SET	0

; RMAP=0 PAGE=3-CCU6 Capture/Compare Interrupt Status Register High
CCU6_ISH_CHE	SET	0
CCU6_ISH_IDLE	SET	0
CCU6_ISH_STR	SET	0
CCU6_ISH_T13CM	SET	0
CCU6_ISH_T13PM	SET	0
CCU6_ISH_TRPF	SET	0
CCU6_ISH_TRPS	SET	0
CCU6_ISH_WHE	SET	0

; RMAP=0 PAGE=3-CCU6 Capture/Compare Interrupt Status Register Low
CCU6_ISL_ICC60F	SET	0
CCU6_ISL_ICC60R	SET	0
CCU6_ISL_ICC61F	SET	0
CCU6_ISL_ICC61R	SET	0
CCU6_ISL_ICC62F	SET	0
CCU6_ISL_ICC62R	SET	0
CCU6_ISL_T12OM	SET	0
CCU6_ISL_T12PM	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Interrupt Status Reset Register High
CCU6_ISRH_RCHE	SET	0
CCU6_ISRH_RIDLE	SET	0
CCU6_ISRH_RSTR	SET	0
CCU6_ISRH_RT13CM	SET	0
CCU6_ISRH_RT13PM	SET	0
CCU6_ISRH_RTRPF	SET	0
CCU6_ISRH_RWHE	SET	0

; RMAP=0 PAGE=0-CCU6 Capture/Compare Interrupt Status Reset Register Low
CCU6_ISRL_RCC60F	SET	0
CCU6_ISRL_RCC60R	SET	0
CCU6_ISRL_RCC61F	SET	0
CCU6_ISRL_RCC61R	SET	0
CCU6_ISRL_RCC62F	SET	0
CCU6_ISRL_RCC62R	SET	0
CCU6_ISRL_RT12OM	SET	0
CCU6_ISRL_RT12PM	SET	0

; RMAP=0 PAGE=2-CCU6 Capture/Compare Interrupt Status Set Register High
CCU6_ISSH_SCHE	SET	0
CCU6_ISSH_SIDLE	SET	0
CCU6_ISSH_SSTR	SET	0
CCU6_ISSH_ST13CM	SET	0
CCU6_ISSH_ST13PM	SET	0
CCU6_ISSH_STRPF	SET	0
CCU6_ISSH_SWHC	SET	0
CCU6_ISSH_SWHE	SET	0

; RMAP=0 PAGE=2-CCU6 Capture/Compare Interrupt Status Set Register Low
CCU6_ISSL_SCC60F	SET	0
CCU6_ISSL_SCC60R	SET	0
CCU6_ISSL_SCC61F	SET	0
CCU6_ISSL_SCC61R	SET	0
CCU6_ISSL_SCC62F	SET	0
CCU6_ISSL_SCC62R	SET	0
CCU6_ISSL_ST12OM	SET	0
CCU6_ISSL_ST12PM	SET	0

; RMAP=0 PAGE=2-CCU6 Multi-Channel Mode Control Register
CCU6_MCMCTR_SWSEL	SET	0
CCU6_MCMCTR_SWSYN	SET	0

; RMAP=0 PAGE=3-CCU6 Multi-Channel Mode Output Register High
CCU6_MCMOUTH_CURH	SET	0
CCU6_MCMOUTH_EXPH	SET	0

; RMAP=0 PAGE=3-CCU6 Multi-Channel Mode Output Register Low
CCU6_MCMOUTL_MCMP	SET	0
CCU6_MCMOUTL_R	SET	0

; RMAP=0 PAGE=0-CCU6 Multi-Channel Mode Output Shadow Register High
CCU6_MCMOUTSH_CURHS	SET	0
CCU6_MCMOUTSH_EXPHS	SET	0
CCU6_MCMOUTSH_STRHP	SET	0

; RMAP=0 PAGE=0-CCU6 Multi-Channel Mode Output Shadow Register Low
CCU6_MCMOUTSL_MCMPS	SET	0
CCU6_MCMOUTSL_STRMCM	SET	0

; RMAP=0 PAGE=2-CCU6 Modulation Control Register High
CCU6_MODCTRH_ECT13O	SET	0
CCU6_MODCTRH_T13MODEN	SET	0

; RMAP=0 PAGE=2-CCU6 Modulation Control Register Low
CCU6_MODCTRL_MCMEN	SET	0
CCU6_MODCTRL_T12MODEN	SET	0

; RMAP=0 -CCU6 Page Register
CCU6_PAGE_OP	SET	0
CCU6_PAGE_PAGE	SET	0
CCU6_PAGE_STNR	SET	0

; RMAP=0 PAGE=3-CCU6 Port Input Select 0 Register High
CCU6_PISEL0H_ISPOS0	SET	0
CCU6_PISEL0H_ISPOS1	SET	0
CCU6_PISEL0H_ISPOS2	SET	0
CCU6_PISEL0H_IST12HR	SET	0

; RMAP=0 PAGE=3-CCU6 Port Input Select 0 Register Low
CCU6_PISEL0L_ISCC60	SET	0
CCU6_PISEL0L_ISCC61	SET	0
CCU6_PISEL0L_ISCC62	SET	0
CCU6_PISEL0L_ISTRP	SET	0

; RMAP=0 PAGE=3-CCU6 Port Input Select 2 Register
CCU6_PISEL2_IST13HR	SET	0

; RMAP=0 PAGE=2-CCU6 Passive State Level Register
CCU6_PSLR_PSL	SET	0
CCU6_PSLR_PSL63	SET	0

; RMAP=0 PAGE=1-CCU6 Dead-Time Control Register for Timer T12 High
CCU6_T12DTCH_DTE0	SET	0
CCU6_T12DTCH_DTE1	SET	0
CCU6_T12DTCH_DTE2	SET	0
CCU6_T12DTCH_DTR0	SET	0
CCU6_T12DTCH_DTR1	SET	0
CCU6_T12DTCH_DTR2	SET	0

; RMAP=0 PAGE=1-CCU6 Dead-Time Control Register for Timer T12 Low
CCU6_T12DTCL_DTM	SET	1

; RMAP=0 PAGE=3-CCU6 Timer T12 Counter Register High
CCU6_T12H_T12CVH	SET	0

; RMAP=0 PAGE=3-CCU6 Timer T12 Counter Register Low
CCU6_T12L_T12CVL	SET	0

; RMAP=0 PAGE=2-CCU6 Capture/Compare Mode Select Register High
CCU6_T12MSELH_DBYP	SET	0
CCU6_T12MSELH_HSYNC	SET	0
CCU6_T12MSELH_MSEL62	SET	0

; RMAP=0 PAGE=2-CCU6 Capture/Compare Mode Select Register Low
CCU6_T12MSELL_MSEL60	SET	0
CCU6_T12MSELL_MSEL61	SET	0

; RMAP=0 PAGE=1-CCU6 Timer T12 Period Register High
CCU6_T12PRH_T12PVH	SET	0

; RMAP=0 PAGE=1-CCU6 Timer T12 Period Register Low
CCU6_T12PRL_T12PVL	SET	1

; RMAP=0 PAGE=3-CCU6 Timer T13 Counter Register High
CCU6_T13H_T13CVH	SET	0

; RMAP=0 PAGE=3-CCU6 Timer T13 Counter Register Low
CCU6_T13L_T13CVL	SET	0

; RMAP=0 PAGE=1-CCU6 Timer T13 Period Register High
CCU6_T13PRH_T13PVH	SET	0

; RMAP=0 PAGE=1-CCU6 Timer T13 Period Register Low
CCU6_T13PRL_T13PVL	SET	1

; RMAP=0 PAGE=1-CCU6 Timer Control 0 Register High
CCU6_TCTR0H_STE13	SET	0
CCU6_TCTR0H_T13CLK	SET	0
CCU6_TCTR0H_T13PRE	SET	0
CCU6_TCTR0H_T13R	SET	0

; RMAP=0 PAGE=1-CCU6 Timer Control 0 Register Low
CCU6_TCTR0L_CDIR	SET	0
CCU6_TCTR0L_CTM	SET	0
CCU6_TCTR0L_STE12	SET	0
CCU6_TCTR0L_T12CLK	SET	0
CCU6_TCTR0L_T12PRE	SET	0
CCU6_TCTR0L_T12R	SET	0

; RMAP=0 PAGE=2-CCU6 Timer Control 2 Register High
CCU6_TCTR2H_T12RSEL	SET	0
CCU6_TCTR2H_T13RSEL	SET	0

; RMAP=0 PAGE=2-CCU6 Timer Control 2 Register Low
CCU6_TCTR2L_T12SSC	SET	0
CCU6_TCTR2L_T13SSC	SET	0
CCU6_TCTR2L_T13TEC	SET	0
CCU6_TCTR2L_T13TED	SET	0

; RMAP=0 PAGE=0-CCU6 Timer Control 4 Register High
CCU6_TCTR4H_T13RES	SET	0
CCU6_TCTR4H_T13RR	SET	0
CCU6_TCTR4H_T13RS	SET	0
CCU6_TCTR4H_T13STD	SET	0
CCU6_TCTR4H_T13STR	SET	0

; RMAP=0 PAGE=0-CCU6 Timer Control 4 Register Low
CCU6_TCTR4L_DTRES	SET	0
CCU6_TCTR4L_T12RES	SET	0
CCU6_TCTR4L_T12RR	SET	0
CCU6_TCTR4L_T12RS	SET	0
CCU6_TCTR4L_T12STD	SET	0
CCU6_TCTR4L_T12STR	SET	0

; RMAP=0 PAGE=2-CCU6 Trap Control Register High
CCU6_TRPCTRH_TRPEN	SET	0
CCU6_TRPCTRH_TRPEN13	SET	0
CCU6_TRPCTRH_TRPPEN	SET	0

; RMAP=0 PAGE=2-CCU6 Trap Control Register Low
CCU6_TRPCTRL_TRPM0	SET	0
CCU6_TRPCTRL_TRPM1	SET	0
CCU6_TRPCTRL_TRPM2	SET	0

; RMAP=1 -CORDIC CORDIC Control Register
CD_CON_MODE	SET	0
CD_CON_MPS	SET	0
CD_CON_ROTVEC	SET	0
CD_CON_ST	SET	0
CD_CON_ST_MODE	SET	0
CD_CON_X_USIGN	SET	0

; RMAP=1 -CORDIC CORDIC X Data High Byte
CD_CORDXH_DATAH	SET	0

; RMAP=1 -CORDIC CORDIC X Data Low Byte
CD_CORDXL_DATAL	SET	0

; RMAP=1 -CORDIC CORDIC Y Data High Byte
CD_CORDYH_DATAH	SET	0

; RMAP=1 -CORDIC CORDIC Y Data Low Byte
CD_CORDYL_DATAL	SET	0

; RMAP=1 -CORDIC CORDIC Z Data High Byte
CD_CORDZH_DATAH	SET	0

; RMAP=1 -CORDIC CORDIC Z Data Low Byte
CD_CORDZL_DATAL	SET	0

; RMAP=1 -CORDIC CORDIC Status and Data Control Register
CD_STATC_CD_BSY	SET	0
CD_STATC_DMAP	SET	0
CD_STATC_EOC	SET	0
CD_STATC_ERROR	SET	0
CD_STATC_INT_EN	SET	0
CD_STATC_KEEPX	SET	0
CD_STATC_KEEPY	SET	0
CD_STATC_KEEPZ	SET	0

; RMAP=0 PAGE=1-SCU Clock Control Register
CMCON_CLKREL	SET	0
CMCON_FCCFG	SET	1
CMCON_KDIV	SET	0
CMCON_VCOSEL	SET	0

; RMAP=0 PAGE=1-SCU Clock Output Control Register
COCON_COREL	SET	0
COCON_COUTS	SET	0
COCON_TLEN	SET	0

; RMAP=x -CPU Data Pointer Register High
DPH_DPH0	SET	0
DPH_DPH1	SET	0
DPH_DPH2	SET	0
DPH_DPH3	SET	0
DPH_DPH4	SET	0
DPH_DPH5	SET	0
DPH_DPH6	SET	0
DPH_DPH7	SET	0

; RMAP=x -CPU Data Pointer Register Low
DPL_DPL0	SET	0
DPL_DPL1	SET	0
DPL_DPL2	SET	0
DPL_DPL3	SET	0
DPL_DPL4	SET	0
DPL_DPL5	SET	0
DPL_DPL6	SET	0
DPL_DPL7	SET	0

; RMAP=x -CPU Extended Operation Register
EO_DPSEL0	SET	0
EO_TRAP_EN	SET	0

; RMAP=0 PAGE=0-SCU External Interrupt Control Register 0
EXICON0_EXINT0	SET	0
EXICON0_EXINT1	SET	0
EXICON0_EXINT2	SET	0
EXICON0_EXINT3	SET	0

; RMAP=0 PAGE=0-SCU External Interrupt Control Register 1
EXICON1_EXINT4	SET	0
EXICON1_EXINT5	SET	0
EXICON1_EXINT6	SET	0

; RMAP=0 PAGE=0-SCU Fractional Divider Control Register
FDCON_BGS	SET	0
FDCON_BRK	SET	0
FDCON_EOFSYN	SET	0
FDCON_ERRSYN	SET	0
FDCON_FDEN	SET	0
FDCON_FDM	SET	0
FDCON_NDOV	SET	0
FDCON_SYNEN	SET	0

; RMAP=0 PAGE=0-SCU Fractional Divider Result Register
FDRES_RESULT	SET	0

; RMAP=0 PAGE=0-SCU Fractional Divider Reload Register
FDSTEP_STEP	SET	0

; RMAP=0 PAGE=1-SCU Flash Error Address Register High
FEAH_ECCERRADDR	SET	0

; RMAP=0 PAGE=1-SCU Flash Error Address Register Low
FEAL_ECCERRADDR	SET	0

; RMAP=1 -OCDS Hardware Breakpoints Data Register
HWBPDR_HWBPXX	SET	0

; RMAP=1 -OCDS Hardware Breakpoints Select Register
HWBPSR_BPSEL	SET	0
HWBPSR_BPSEL_P	SET	0

; RMAP=0 PAGE=1-SCU Identity Register
ID_PRODID	SET	0
ID_VERID	SET	0

; RMAP=x -CPU Interrupt Enable Register 0
IEN0_EA	SET	0
IEN0_ES	SET	0
IEN0_ET0	SET	0
IEN0_ET1	SET	0
IEN0_ET2	SET	0
IEN0_EX0	SET	0
IEN0_EX1	SET	0

; RMAP=x -CPU Module Interrupt Enable 1 Register
IEN1_EADC	SET	0
IEN1_ECCIP0	SET	0
IEN1_ECCIP1	SET	0
IEN1_ECCIP2	SET	0
IEN1_ECCIP3	SET	0
IEN1_ESSC	SET	0
IEN1_EX2	SET	0
IEN1_EXM	SET	0

; RMAP=x -CPU Interrupt Priority 1 Register
IP1_PADC	SET	0
IP1_PCCIP0	SET	0
IP1_PCCIP1	SET	0
IP1_PCCIP2	SET	0
IP1_PCCIP3	SET	0
IP1_PSSC	SET	0
IP1_PX2	SET	0
IP1_PXM	SET	0

; RMAP=x -CPU Interrupt Priority 1 High Register
IPH1_PADCH	SET	0
IPH1_PCCIP0H	SET	0
IPH1_PCCIP1H	SET	0
IPH1_PCCIP2H	SET	0
IPH1_PCCIP3H	SET	0
IPH1_PSSCH	SET	0
IPH1_PX2H	SET	0
IPH1_PXMH	SET	0

; RMAP=x -CPU Interrupt Priority High Register
IPH_PSH	SET	0
IPH_PT0H	SET	0
IPH_PT1H	SET	0
IPH_PT2H	SET	0
IPH_PX0H	SET	0
IPH_PX1H	SET	0

; RMAP=x -CPU Interrupt Priority Register
IP_PS	SET	0
IP_PT0	SET	0
IP_PT1	SET	0
IP_PT2	SET	0
IP_PX0	SET	0
IP_PX1	SET	0

; RMAP=0 PAGE=0-SCU Interrupt Request Register 0
IRCON0_EXINT0	SET	0
IRCON0_EXINT1	SET	0
IRCON0_EXINT2	SET	0
IRCON0_EXINT3	SET	0
IRCON0_EXINT4	SET	0
IRCON0_EXINT5	SET	0
IRCON0_EXINT6	SET	0

; RMAP=0 PAGE=0-SCU Interrupt Request Register 1
IRCON1_ADCSR0	SET	0
IRCON1_ADCSR1	SET	0
IRCON1_CANSRC1	SET	0
IRCON1_CANSRC2	SET	0
IRCON1_EIR	SET	0
IRCON1_RIR	SET	0
IRCON1_TIR	SET	0

; RMAP=0 PAGE=0-SCU Interrupt Request Register 2
IRCON2_CANSRC0	SET	0
IRCON2_CANSRC3	SET	0

; RMAP=0 PAGE=3-SCU Interrupt Request Register 3
IRCON3_CANSRC4	SET	0
IRCON3_CANSRC5	SET	0
IRCON3_CCU6SR0	SET	0
IRCON3_CCU6SR1	SET	0

; RMAP=0 PAGE=3-SCU Interrupt Request Register 4
IRCON4_CANSRC6	SET	0
IRCON4_CANSRC7	SET	0
IRCON4_CCU6SR2	SET	0
IRCON4_CCU6SR3	SET	0

; RMAP=1 -MDU MDU Operand Register 0
MDU_MD0_DATA	SET	0

; RMAP=1 -MDU MDU Operand Register 1
MDU_MD1_DATA	SET	0

; RMAP=1 -MDU MDU Operand Register 2
MDU_MD2_DATA	SET	0

; RMAP=1 -MDU MDU Operand Register 3
MDU_MD3_DATA	SET	0

; RMAP=1 -MDU MDU Operand Register 4
MDU_MD4_DATA	SET	0

; RMAP=1 -MDU MDU Operand Register 5
MDU_MD5_DATA	SET	0

; RMAP=1 -MDU MDU Control Register
MDU_MDUCON_IE	SET	0
MDU_MDUCON_IR	SET	0
MDU_MDUCON_OPCODE	SET	0
MDU_MDUCON_RSEL	SET	0
MDU_MDUCON_START	SET	0

; RMAP=1 -MDU MDU Status Register
MDU_MDUSTAT_IERR	SET	0
MDU_MDUSTAT_IRDY	SET	0
MDU_MDUSTAT_MDU_BSY	SET	0

; RMAP=1 -MDU MDU Result Register 0
MDU_MR0_DATA	SET	0

; RMAP=1 -MDU MDU Result Register 1
MDU_MR1_DATA	SET	0

; RMAP=1 -MDU MDU Result Register 2
MDU_MR2_DATA	SET	0

; RMAP=1 -MDU MDU Result Register 3
MDU_MR3_DATA	SET	0

; RMAP=1 -MDU MDU Result Register 4
MDU_MR4_DATA	SET	0

; RMAP=1 -MDU MDU Result Register 5
MDU_MR5_DATA	SET	0

; RMAP=0 PAGE=1-SCU Miscellaneous Control Register
MISC_CON_DFLASHEN	SET	0

; RMAP=1 -OCDS BreakPoints Control Register
MMBPCR_HWB0C	SET	0
MMBPCR_HWB1C	SET	0
MMBPCR_HWB2C	SET	0
MMBPCR_HWB3C	SET	0
MMBPCR_SWBC	SET	0

; RMAP=1 -OCDS Monitor Mode Control 2 Register
MMCR2_ALTDI	SET	0
MMCR2_DSUSP	SET	0
MMCR2_EXBC	SET	0
MMCR2_JENA	SET	0
MMCR2_MBCON	SET	0
MMCR2_MMEP	SET	0
MMCR2_MMODE	SET	0
MMCR2_STMODE	SET	0

; RMAP=1 -OCDS Monitor Mode Control Register
MMCR_MEXIT	SET	0
MMCR_MEXIT_P	SET	0
MMCR_MRAMS	SET	0
MMCR_MRAMS_P	SET	0
MMCR_MSTEP	SET	0
MMCR_RRF	SET	0
MMCR_TRF	SET	0

; RMAP=1 -OCDS Monitor Mode Data Transfer Register Receive
MMDR_MMRR	SET	0

; RMAP=1 -OCDS Monitor Mode Interrupt Control Register
MMICR_COMRST	SET	0
MMICR_DRETR	SET	0
MMICR_DVECT	SET	0
MMICR_MMUIE	SET	0
MMICR_MMUIE_P	SET	0
MMICR_MSTSEL	SET	0
MMICR_RRIE	SET	0
MMICR_RRIE_P	SET	0

; RMAP=1 -OCDS Monitor Mode Status Register
MMSR_EXBF	SET	0
MMSR_HWB0F	SET	0
MMSR_HWB1F	SET	0
MMSR_HWB2F	SET	0
MMSR_HWB3F	SET	0
MMSR_MBCAM	SET	0
MMSR_MBCIN	SET	0
MMSR_SWBF	SET	0

; RMAP=1 -OCDS Monitor Work Register 1
MMWR1_MMWR1	SET	0

; RMAP=1 -OCDS Monitor Work Register 2
MMWR2_MMWR2	SET	0

; RMAP=0 PAGE=3-SCU Peripheral Input Select Register 1
MODPISEL1_EXINT6IS	SET	0
MODPISEL1_JTAGTCKS1	SET	0
MODPISEL1_JTAGTDIS1	SET	0
MODPISEL1_T21EXIS	SET	0
MODPISEL1_UR1RIS	SET	0

; RMAP=0 PAGE=3-SCU Peripheral Input Select Register 2
MODPISEL2_T0IS	SET	0
MODPISEL2_T1IS	SET	0
MODPISEL2_T21IS	SET	0
MODPISEL2_T2IS	SET	0

; RMAP=0 PAGE=0-SCU Peripheral Input Select Register
MODPISEL_EXINT0IS	SET	0
MODPISEL_EXINT1IS	SET	0
MODPISEL_EXINT2IS	SET	0
MODPISEL_JTAGTCKS	SET	0
MODPISEL_JTAGTDIS	SET	0
MODPISEL_URRIS	SET	0
MODPISEL_URRISH	SET	0

; RMAP=0 PAGE=3-SCU Module Suspend Control Register
MODSUSP_T12SUSP	SET	0
MODSUSP_T13SUSP	SET	0
MODSUSP_T21SUSP	SET	0
MODSUSP_T2SUSP	SET	0
MODSUSP_WDTSUSP	SET	0

; RMAP=0 PAGE=0-SCU NMI Control Register
NMICON_NMIECC	SET	0
NMICON_NMIFLASH	SET	0
NMICON_NMIOCDS	SET	0
NMICON_NMIPLL	SET	0
NMICON_NMIVDD	SET	0
NMICON_NMIVDDP	SET	0
NMICON_NMIWDT	SET	0

; RMAP=0 PAGE=0-SCU NMI Status Register
NMISR_FNMIECC	SET	0
NMISR_FNMIFLASH	SET	0
NMISR_FNMIOCDS	SET	0
NMISR_FNMIPLL	SET	0
NMISR_FNMIVDD	SET	0
NMISR_FNMIVDDP	SET	0
NMISR_FNMIWDT	SET	0

; RMAP=0 PAGE=1-SCU OSC Control Register
OSC_CON_ORDRES	SET	0
OSC_CON_OSCPD	SET	0
OSC_CON_OSCR	SET	0
OSC_CON_OSCSS	SET	1
OSC_CON_XPD	SET	1

; RMAP=0 PAGE=2-P0 P0 Alternate Select 0 Register
P0_ALTSEL0_P0	SET	0
P0_ALTSEL0_P1	SET	0
P0_ALTSEL0_P2	SET	0
P0_ALTSEL0_P3	SET	0
P0_ALTSEL0_P4	SET	0
P0_ALTSEL0_P5	SET	0
P0_ALTSEL0_P6	SET	0
P0_ALTSEL0_P7	SET	0

; RMAP=0 PAGE=2-P0 P0 Alternate Select 1 Register
P0_ALTSEL1_P0	SET	0
P0_ALTSEL1_P1	SET	0
P0_ALTSEL1_P2	SET	0
P0_ALTSEL1_P3	SET	0
P0_ALTSEL1_P4	SET	0
P0_ALTSEL1_P5	SET	0
P0_ALTSEL1_P6	SET	0
P0_ALTSEL1_P7	SET	0

; RMAP=0 PAGE=0-P0 P0 Data Register
P0_DATA_P0	SET	0
P0_DATA_P1	SET	0
P0_DATA_P2	SET	0
P0_DATA_P3	SET	0
P0_DATA_P4	SET	0
P0_DATA_P5	SET	0
P0_DATA_P6	SET	0
P0_DATA_P7	SET	0

; RMAP=0 PAGE=0-P0 P0 Direction Register
P0_DIR_P0	SET	0
P0_DIR_P1	SET	0
P0_DIR_P2	SET	0
P0_DIR_P3	SET	0
P0_DIR_P4	SET	0
P0_DIR_P5	SET	0
P0_DIR_P6	SET	0
P0_DIR_P7	SET	0

; RMAP=0 PAGE=3-P0 P0 Open Drain Control Register
P0_OD_P0	SET	0
P0_OD_P1	SET	0
P0_OD_P2	SET	0
P0_OD_P3	SET	0
P0_OD_P4	SET	0
P0_OD_P5	SET	0
P0_OD_P6	SET	0
P0_OD_P7	SET	0

; RMAP=0 PAGE=1-P0 P0 Pull-Up/Pull-Down Enable Register
P0_PUDEN_P0	SET	0
P0_PUDEN_P1	SET	0
P0_PUDEN_P2	SET	1
P0_PUDEN_P3	SET	0
P0_PUDEN_P4	SET	0
P0_PUDEN_P5	SET	0
P0_PUDEN_P6	SET	1
P0_PUDEN_P7	SET	1

; RMAP=0 PAGE=1-P0 P0 Pull-Up/Pull-Down Select Register
P0_PUDSEL_P0	SET	1
P0_PUDSEL_P1	SET	1
P0_PUDSEL_P2	SET	1
P0_PUDSEL_P3	SET	1
P0_PUDSEL_P4	SET	1
P0_PUDSEL_P5	SET	1
P0_PUDSEL_P6	SET	1
P0_PUDSEL_P7	SET	1

; RMAP=0 PAGE=2-P1 P1 Alternate Select 0 Register
P1_ALTSEL0_P0	SET	0
P1_ALTSEL0_P1	SET	1
P1_ALTSEL0_P2	SET	0
P1_ALTSEL0_P3	SET	0
P1_ALTSEL0_P4	SET	0
P1_ALTSEL0_P5	SET	0
P1_ALTSEL0_P6	SET	0
P1_ALTSEL0_P7	SET	0

; RMAP=0 PAGE=2-P1 P1 Alternate Select 1 Register
P1_ALTSEL1_P0	SET	0
P1_ALTSEL1_P1	SET	1
P1_ALTSEL1_P2	SET	0
P1_ALTSEL1_P3	SET	0
P1_ALTSEL1_P4	SET	0
P1_ALTSEL1_P5	SET	0
P1_ALTSEL1_P6	SET	0
P1_ALTSEL1_P7	SET	0

; RMAP=0 PAGE=0-P1 P1 Data Register
P1_DATA_P0	SET	0
P1_DATA_P1	SET	0
P1_DATA_P2	SET	0
P1_DATA_P3	SET	0
P1_DATA_P4	SET	0
P1_DATA_P5	SET	0
P1_DATA_P6	SET	0
P1_DATA_P7	SET	0

; RMAP=0 PAGE=0-P1 P1 Direction Register
P1_DIR_P0	SET	0
P1_DIR_P1	SET	1
P1_DIR_P2	SET	0
P1_DIR_P3	SET	0
P1_DIR_P4	SET	0
P1_DIR_P5	SET	0
P1_DIR_P6	SET	0
P1_DIR_P7	SET	0

; RMAP=0 PAGE=3-P1 P1 Open Drain Control Register
P1_OD_P0	SET	0
P1_OD_P1	SET	0
P1_OD_P2	SET	0
P1_OD_P3	SET	0
P1_OD_P4	SET	0
P1_OD_P5	SET	0
P1_OD_P6	SET	0
P1_OD_P7	SET	0

; RMAP=0 PAGE=1-P1 P1 Pull-Up/Pull-Down Enable Register
P1_PUDEN_P0	SET	1
P1_PUDEN_P1	SET	1
P1_PUDEN_P2	SET	1
P1_PUDEN_P3	SET	1
P1_PUDEN_P4	SET	1
P1_PUDEN_P5	SET	1
P1_PUDEN_P6	SET	1
P1_PUDEN_P7	SET	1

; RMAP=0 PAGE=1-P1 P1 Pull-Up/Pull-Down Select Register
P1_PUDSEL_P0	SET	1
P1_PUDSEL_P1	SET	1
P1_PUDSEL_P2	SET	1
P1_PUDSEL_P3	SET	1
P1_PUDSEL_P4	SET	1
P1_PUDSEL_P5	SET	1
P1_PUDSEL_P6	SET	1
P1_PUDSEL_P7	SET	1

; RMAP=0 PAGE=0-P2 P2 Data Register
P2_DATA_P0	SET	0
P2_DATA_P1	SET	0
P2_DATA_P2	SET	0
P2_DATA_P3	SET	0
P2_DATA_P4	SET	0
P2_DATA_P5	SET	0
P2_DATA_P6	SET	0
P2_DATA_P7	SET	0

; RMAP=0 PAGE=0-P2 P2 Direction Register
P2_DIR_P0	SET	0
P2_DIR_P1	SET	0
P2_DIR_P2	SET	0
P2_DIR_P3	SET	0
P2_DIR_P4	SET	0
P2_DIR_P5	SET	0
P2_DIR_P6	SET	0
P2_DIR_P7	SET	0

; RMAP=0 PAGE=1-P2 P2 Pull-Up/Pull-Down Enable Register
P2_PUDEN_P0	SET	0
P2_PUDEN_P1	SET	0
P2_PUDEN_P2	SET	0
P2_PUDEN_P3	SET	0
P2_PUDEN_P4	SET	0
P2_PUDEN_P5	SET	0
P2_PUDEN_P6	SET	0
P2_PUDEN_P7	SET	0

; RMAP=0 PAGE=1-P2 P2 Pull-Up/Pull-Down Select Register
P2_PUDSEL_P0	SET	1
P2_PUDSEL_P1	SET	1
P2_PUDSEL_P2	SET	1
P2_PUDSEL_P3	SET	1
P2_PUDSEL_P4	SET	1
P2_PUDSEL_P5	SET	1
P2_PUDSEL_P6	SET	1
P2_PUDSEL_P7	SET	1

; RMAP=0 PAGE=2-P3 P3 Alternate Select 0 Register
P3_ALTSEL0_P0	SET	0
P3_ALTSEL0_P1	SET	0
P3_ALTSEL0_P2	SET	0
P3_ALTSEL0_P3	SET	0
P3_ALTSEL0_P4	SET	0
P3_ALTSEL0_P5	SET	0
P3_ALTSEL0_P6	SET	0
P3_ALTSEL0_P7	SET	0

; RMAP=0 PAGE=2-P3 P3 Alternate Select 1 Register
P3_ALTSEL1_P0	SET	0
P3_ALTSEL1_P1	SET	0
P3_ALTSEL1_P2	SET	0
P3_ALTSEL1_P3	SET	0
P3_ALTSEL1_P4	SET	0
P3_ALTSEL1_P5	SET	0
P3_ALTSEL1_P6	SET	0
P3_ALTSEL1_P7	SET	0

; RMAP=0 PAGE=0-P3 P3 Data Register
P3_DATA_P0	SET	0
P3_DATA_P1	SET	0
P3_DATA_P2	SET	0
P3_DATA_P3	SET	0
P3_DATA_P4	SET	0
P3_DATA_P5	SET	0
P3_DATA_P6	SET	0
P3_DATA_P7	SET	0

; RMAP=0 PAGE=0-P3 P3 Direction Register
P3_DIR_P0	SET	0
P3_DIR_P1	SET	0
P3_DIR_P2	SET	0
P3_DIR_P3	SET	0
P3_DIR_P4	SET	0
P3_DIR_P5	SET	0
P3_DIR_P6	SET	0
P3_DIR_P7	SET	0

; RMAP=0 PAGE=3-P3 P3 Open Drain Control Register
P3_OD_P0	SET	0
P3_OD_P1	SET	0
P3_OD_P2	SET	0
P3_OD_P3	SET	0
P3_OD_P4	SET	0
P3_OD_P5	SET	0
P3_OD_P6	SET	0
P3_OD_P7	SET	0

; RMAP=0 PAGE=1-P3 P3 Pull-Up/Pull-Down Enable Register
P3_PUDEN_P0	SET	0
P3_PUDEN_P1	SET	0
P3_PUDEN_P2	SET	0
P3_PUDEN_P3	SET	0
P3_PUDEN_P4	SET	0
P3_PUDEN_P5	SET	0
P3_PUDEN_P6	SET	0
P3_PUDEN_P7	SET	0

; RMAP=0 PAGE=1-P3 P3 Pull-Up/Pull-Down Select Register
P3_PUDSEL_P0	SET	1
P3_PUDSEL_P1	SET	1
P3_PUDSEL_P2	SET	1
P3_PUDSEL_P3	SET	1
P3_PUDSEL_P4	SET	1
P3_PUDSEL_P5	SET	1
P3_PUDSEL_P6	SET	0
P3_PUDSEL_P7	SET	1

; RMAP=0 PAGE=2-P4 P4 Alternate Select 0 Register
P4_ALTSEL0_P0	SET	0
P4_ALTSEL0_P1	SET	0
P4_ALTSEL0_P2	SET	0
P4_ALTSEL0_P3	SET	0
P4_ALTSEL0_P4	SET	0
P4_ALTSEL0_P5	SET	0
P4_ALTSEL0_P6	SET	0
P4_ALTSEL0_P7	SET	0

; RMAP=0 PAGE=2-P4 P4 Alternate Select 1 Register
P4_ALTSEL1_P0	SET	0
P4_ALTSEL1_P1	SET	0
P4_ALTSEL1_P2	SET	0
P4_ALTSEL1_P3	SET	0
P4_ALTSEL1_P4	SET	0
P4_ALTSEL1_P5	SET	0
P4_ALTSEL1_P6	SET	0
P4_ALTSEL1_P7	SET	0

; RMAP=0 PAGE=0-P4 P4 Data Register
P4_DATA_P0	SET	0
P4_DATA_P1	SET	0
P4_DATA_P2	SET	0
P4_DATA_P3	SET	0
P4_DATA_P4	SET	0
P4_DATA_P5	SET	0
P4_DATA_P6	SET	0
P4_DATA_P7	SET	0

; RMAP=0 PAGE=0-P4 P4 Direction Register
P4_DIR_P0	SET	0
P4_DIR_P1	SET	0
P4_DIR_P2	SET	0
P4_DIR_P3	SET	0
P4_DIR_P4	SET	0
P4_DIR_P5	SET	0
P4_DIR_P6	SET	0
P4_DIR_P7	SET	0

; RMAP=0 PAGE=3-P4 P4 Open Drain Control Register
P4_OD_P0	SET	0
P4_OD_P1	SET	0
P4_OD_P2	SET	0
P4_OD_P3	SET	0
P4_OD_P4	SET	0
P4_OD_P5	SET	0
P4_OD_P6	SET	0
P4_OD_P7	SET	0

; RMAP=0 PAGE=1-P4 P4 Pull-Up/Pull-Down Enable Register
P4_PUDEN_P0	SET	0
P4_PUDEN_P1	SET	0
P4_PUDEN_P2	SET	1
P4_PUDEN_P3	SET	0
P4_PUDEN_P4	SET	0
P4_PUDEN_P5	SET	0
P4_PUDEN_P6	SET	0
P4_PUDEN_P7	SET	0

; RMAP=0 PAGE=1-P4 P4 Pull-Up/Pull-Down Select Register
P4_PUDSEL_P0	SET	1
P4_PUDSEL_P1	SET	1
P4_PUDSEL_P2	SET	1
P4_PUDSEL_P3	SET	1
P4_PUDSEL_P4	SET	1
P4_PUDSEL_P5	SET	1
P4_PUDSEL_P6	SET	1
P4_PUDSEL_P7	SET	1

; RMAP=0 PAGE=2-P5 P5 Alternate Select 0 Register
P5_ALTSEL0_P0	SET	0
P5_ALTSEL0_P1	SET	0
P5_ALTSEL0_P2	SET	0
P5_ALTSEL0_P3	SET	0
P5_ALTSEL0_P4	SET	0
P5_ALTSEL0_P5	SET	0
P5_ALTSEL0_P6	SET	0
P5_ALTSEL0_P7	SET	0

; RMAP=0 PAGE=2-P5 P5 Alternate Select 1 Register
P5_ALTSEL1_P0	SET	0
P5_ALTSEL1_P1	SET	0
P5_ALTSEL1_P2	SET	0
P5_ALTSEL1_P3	SET	0
P5_ALTSEL1_P4	SET	0
P5_ALTSEL1_P5	SET	0
P5_ALTSEL1_P6	SET	0
P5_ALTSEL1_P7	SET	0

; RMAP=0 PAGE=0-P5 P5 Data Register
P5_DATA_P0	SET	0
P5_DATA_P1	SET	0
P5_DATA_P2	SET	0
P5_DATA_P3	SET	0
P5_DATA_P4	SET	0
P5_DATA_P5	SET	0
P5_DATA_P6	SET	0
P5_DATA_P7	SET	0

; RMAP=0 PAGE=0-P5 P5 Direction Register
P5_DIR_P0	SET	0
P5_DIR_P1	SET	0
P5_DIR_P2	SET	0
P5_DIR_P3	SET	0
P5_DIR_P4	SET	0
P5_DIR_P5	SET	0
P5_DIR_P6	SET	0
P5_DIR_P7	SET	0

; RMAP=0 PAGE=3-P5 P5 Open Drain Control Register
P5_OD_P0	SET	0
P5_OD_P1	SET	0
P5_OD_P2	SET	0
P5_OD_P3	SET	0
P5_OD_P4	SET	0
P5_OD_P5	SET	0
P5_OD_P6	SET	0
P5_OD_P7	SET	0

; RMAP=0 PAGE=1-P5 P5 Pull-Up/Pull-Down Enable Register
P5_PUDEN_P0	SET	0
P5_PUDEN_P1	SET	0
P5_PUDEN_P2	SET	0
P5_PUDEN_P3	SET	0
P5_PUDEN_P4	SET	0
P5_PUDEN_P5	SET	0
P5_PUDEN_P6	SET	0
P5_PUDEN_P7	SET	0

; RMAP=0 PAGE=1-P5 P5 Pull-Up/Pull-Down Select Register
P5_PUDSEL_P0	SET	0
P5_PUDSEL_P1	SET	0
P5_PUDSEL_P2	SET	0
P5_PUDSEL_P3	SET	0
P5_PUDSEL_P4	SET	0
P5_PUDSEL_P5	SET	0
P5_PUDSEL_P6	SET	0
P5_PUDSEL_P7	SET	0

; RMAP=0 PAGE=1-SCU Password Register
PASSWD_MODE	SET	0
PASSWD_PASS	SET	0
PASSWD_PROTECT_S	SET	0

; RMAP=x -CPU Power Control Register
PCON_GF0	SET	0
PCON_GF1	SET	0
PCON_IDLE	SET	0
PCON_SMOD	SET	0

; RMAP=0 PAGE=1-SCU PLL Control Register
PLL_CON_LOCK	SET	0
PLL_CON_NDIV	SET	10
PLL_CON_OSCDISC	SET	0
PLL_CON_RESLD	SET	0
PLL_CON_VCOBYP	SET	0

; RMAP=0 PAGE=1-SCU Power Mode Control Register 0
PMCON0_PD	SET	0
PMCON0_SD	SET	0
PMCON0_WDTRST	SET	0
PMCON0_WKRS	SET	0
PMCON0_WKSEL	SET	0
PMCON0_WS	SET	0

; RMAP=0 PAGE=1-SCU Power Mode Control Register 1
PMCON1_ADC_DIS	SET	0
PMCON1_CAN_DIS	SET	0
PMCON1_CCU_DIS	SET	0
PMCON1_CDC_DIS	SET	0
PMCON1_MDU_DIS	SET	0
PMCON1_SSC_DIS	SET	0
PMCON1_T2_DIS	SET	0

; RMAP=0 PAGE=3-SCU Power Mode Control Register 2
PMCON2_T21_DIS	SET	0
PMCON2_UART1_DIS	SET	0

; RMAP=0 -PORT Page Register
PORT_PAGE_OP	SET	0
PORT_PAGE_PAGE	SET	0
PORT_PAGE_STNR	SET	0

; RMAP=x -CPU Program Status Word Register
PSW_AC	SET	0
PSW_CY	SET	0
PSW_F0	SET	0
PSW_F1	SET	0
PSW_OV	SET	0
PSW_P	SET	0
PSW_RS0	SET	0
PSW_RS1	SET	0

; RMAP=x -CPU Serial Data Buffer Register
SBUF_VAL	SET	0

; RMAP=x -CPU Serial Channel Control Register
SCON_RB8	SET	0
SCON_REN	SET	0
SCON_RI	SET	0
SCON_SM0	SET	0
SCON_SM1	SET	0
SCON_SM2	SET	0
SCON_TB8	SET	0
SCON_TI	SET	0

; RMAP=0 -SCU Page Register
SCU_PAGE_OP	SET	0
SCU_PAGE_PAGE	SET	0
SCU_PAGE_STNR	SET	0

; RMAP=x -CPU Stack Pointer Register
SP_SP	SET	0

; RMAP=0 -SSC Baudrate Timer Reload Register High
SSC_BRH_BR_VALUE	SET	0

; RMAP=0 -SSC Baudrate Timer Reload Register Low
SSC_BRL_BR_VALUE	SET	0

; RMAP=0 -SSC Control Register High Operating Mode
SSC_CONH_O_BE	SET	0
SSC_CONH_O_BSY	SET	0
SSC_CONH_O_EN	SET	0
SSC_CONH_O_MS	SET	0
SSC_CONH_O_PE	SET	0
SSC_CONH_O_RE	SET	0
SSC_CONH_O_TE	SET	0

; RMAP=0 -SSC Control Register High Programming Mode
SSC_CONH_P_AREN	SET	0
SSC_CONH_P_BEN	SET	0
SSC_CONH_P_EN	SET	0
SSC_CONH_P_MS	SET	0
SSC_CONH_P_PEN	SET	0
SSC_CONH_P_REN	SET	0
SSC_CONH_P_TEN	SET	0

; RMAP=0 -SSC Control Register Low Operating Mode
SSC_CONL_O_BC	SET	0

; RMAP=0 -SSC Control Register Low Programming Mode
SSC_CONL_P_BM	SET	1
SSC_CONL_P_HB	SET	0
SSC_CONL_P_LB	SET	0
SSC_CONL_P_PH	SET	0
SSC_CONL_P_PO	SET	0

; RMAP=0 -SSC Port Input Select Register
SSC_PISEL_CIS	SET	0
SSC_PISEL_MIS	SET	0
SSC_PISEL_SIS	SET	0

; RMAP=0 -SSC Receiver Buffer Register Low
SSC_RBL_RB_VALUE	SET	0

; RMAP=0 -SSC Transmitter Buffer Register Low
SSC_TBL_TB_VALUE	SET	0

; RMAP=x -SCU System Control Register 0
SYSCON0_1	SET	0
SYSCON0_IMODE	SET	0
SYSCON0_RMAP	SET	0

; RMAP=1 -T21 Timer 2 Reload/Capture Register High
T21_RC2H_RC2	SET	0

; RMAP=1 -T21 Timer 2 Reload/Capture Register Low
T21_RC2L_RC2	SET	0

; RMAP=1 -T21 Timer 2 Control Register
T21_T2CON_C_T2	SET	0
T21_T2CON_CP_RL2	SET	0
T21_T2CON_EXEN2	SET	0
T21_T2CON_EXF2	SET	0
T21_T2CON_TF2	SET	0
T21_T2CON_TR2	SET	0

; RMAP=1 -T21 Timer 2 Register High
T21_T2H_THL2	SET	0

; RMAP=1 -T21 Timer 2 Register Low
T21_T2L_THL2	SET	0

; RMAP=1 -T21 Timer 2 Mode Register
T21_T2MOD_DCEN	SET	0
T21_T2MOD_EDGESEL	SET	0
T21_T2MOD_PREN	SET	0
T21_T2MOD_T2PRE	SET	0
T21_T2MOD_T2REGS	SET	0
T21_T2MOD_T2RHEN	SET	0

; RMAP=0 -T2 Timer 2 Reload/Capture Register High
T2_RC2H_RC2	SET	0

; RMAP=0 -T2 Timer 2 Reload/Capture Register Low
T2_RC2L_RC2	SET	0

; RMAP=0 -T2 Timer 2 Control Register
T2_T2CON_C_T2	SET	0
T2_T2CON_CP_RL2	SET	0
T2_T2CON_EXEN2	SET	0
T2_T2CON_EXF2	SET	0
T2_T2CON_TF2	SET	0
T2_T2CON_TR2	SET	0

; RMAP=0 -T2 Timer 2 Register High
T2_T2H_THL2	SET	0

; RMAP=0 -T2 Timer 2 Register Low
T2_T2L_THL2	SET	0

; RMAP=0 -T2 Timer 2 Mode Register
T2_T2MOD_DCEN	SET	0
T2_T2MOD_EDGESEL	SET	0
T2_T2MOD_PREN	SET	0
T2_T2MOD_T2PRE	SET	0
T2_T2MOD_T2REGS	SET	0
T2_T2MOD_T2RHEN	SET	0

; RMAP=x -CPU Timer Control Register
TCON_IE0	SET	0
TCON_IE1	SET	0
TCON_IT0	SET	0
TCON_IT1	SET	0
TCON_TF0	SET	0
TCON_TF1	SET	0
TCON_TR0	SET	0
TCON_TR1	SET	0

; RMAP=x -CPU Timer 0 Register High
TH0_VAL	SET	0

; RMAP=x -CPU Timer 1 Register High
TH1_VAL	SET	0

; RMAP=x -CPU Timer 0 Register Low
TL0_VAL	SET	0

; RMAP=x -CPU Timer 1 Register Low
TL1_VAL	SET	0

; RMAP=x -CPU Timer Mode Register
TMOD_GATE0	SET	0
TMOD_GATE1	SET	0
TMOD_T0M	SET	0
TMOD_T0S	SET	0
TMOD_T1M	SET	0
TMOD_T1S	SET	0

; RMAP=1 -UART1 Baud Rate Control Register
UART1_BCON_BRPRE	SET	0
UART1_BCON_R	SET	0

; RMAP=1 -UART1 Baud Rate Timer/Reload Register
UART1_BG_BR_VALUE	SET	255

; RMAP=1 -UART1 Fractional Divider Control Register
UART1_FDCON_FDEN	SET	0
UART1_FDCON_FDM	SET	0
UART1_FDCON_NDOV	SET	0

; RMAP=1 -UART1 Fractional Divider Result Register
UART1_FDRES_RESULT	SET	0

; RMAP=1 -UART1 Fractional Divider Reload Register
UART1_FDSTEP_STEP	SET	0

; RMAP=1 -UART1 Serial Data Buffer Register
UART1_SBUF_VAL	SET	0

; RMAP=1 -UART1 Serial Channel Control Register
UART1_SCON_RB8_1	SET	0
UART1_SCON_REN_1	SET	0
UART1_SCON_RI_1	SET	0
UART1_SCON_SM0_1	SET	0
UART1_SCON_SM1_1	SET	0
UART1_SCON_SM2_1	SET	0
UART1_SCON_TB8_1	SET	0
UART1_SCON_TI_1	SET	0

; RMAP=1 -WDT Watchdog Timer Control Register
WDTCON_WDTEN	SET	0
WDTCON_WDTIN	SET	1
WDTCON_WDTPR	SET	0
WDTCON_WDTRS	SET	0
WDTCON_WINBEN	SET	0

; RMAP=1 -WDT Watchdog Timer Register High
WDTH_WDT	SET	0

; RMAP=1 -WDT Watchdog Timer Register Low
WDTL_WDT	SET	0

; RMAP=1 -WDT Watchdog Timer Reload Register
WDTREL_WDTREL	SET	0

; RMAP=1 -WDT Watchdog Window-Boundary Count Register
WDTWINB_WDTWINB	SET	0

; RMAP=0 PAGE=3-SCU On-chip XRAM Address Higher Order
XADDRH_ADDRH	SET	0


{\rtf1\ansi\deff0\deftab720{\fonttbl{\f0\fnil MS Sans Serif;}{\f1\fnil\fcharset2 Symbol;}{\f2\fswiss\fprq2 System;}{\f3\fnil Times New Roman;}{\f4\fswiss\fprq2 Arial;}}
{\colortbl\red0\green0\blue0;\red0\green0\blue128;\red255\green0\blue0;}
\deflang1031\pard\plain\f4\fs28\cf0 DAvE's Project Documentation
\par \plain\f4\fs22\cf0
\par \plain\f4\fs22\cf0 Project: \tab\tab\b JumpPro.dav
\par 
\par \plain\f4\fs22\cf0 Controller: \tab\tab\b XC886CLM-8FF
\par \plain\f4\fs22\cf0 Compiler: \tab\tab\b Keil
\par \plain\f4\fs22\cf0 Memory Model: \tab\b SMALL
\par 
\par \plain\f4\fs22\cf0 Date: \tab\tab\tab\b 2013-7-6 21:43:28
\par 
\par 
\par \plain\f4\fs22\cf2\b Please read this document carefully and note
\par \plain\f4\fs22\cf2\b the red-colored hints.
\par 
\par \plain\f4\fs22\cf2\b If you miss a file in the generated files list
\par \plain\f4\fs22\cf2\b maybe you have forgotten to select the
\par \plain\f4\fs22\cf2\b initialisation function of the related module.
\par 
\par \plain\f4\fs22\cf0 Generated Files:
\plain\f4\fs20\cf0\b
\par \tab\tab\tab MAIN.H
\par \tab\tab\tab MAIN.C
\par \tab\tab\tab START_XC.A51
\par \tab\tab\tab JUMPPRO.ASM
\par 
\par 
\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul Project Settings
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void MAIN_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function initializes the microcontroller. It is

\par \tab \tab assumed that the SFRs are in their reset state.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void main(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the main function.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab Configuration of the System Clock:\par 
\tab - External Osc is selected (configuration is done in the startup file\par 
\tab 'START_XC.A51')\par 
\tab - PLL Mode, NDIV =  24\par 
\tab - input frequency is 8 MHz\par 
\par 
\tab *********************************************************************************\par 
\tab Note : All peripheral related IO configurations are done in the\par 
\tab respective peripheral modules (alternate functions selection)\par 
\tab *********************************************************************************\par 
\par 
\tab \cf2Interrupt structure 2 mode 0 is selected.\cf0\par 
\par 
\tab \cf2Interrupt service routine choice 2 is selected.\cf0\par 
\par 
\tab \cf2globally disable interrupts\cf0\par 
\par 

}

//****************************************************************************
// @Module        Watch Dog Timer
// @Filename      WDT.H
// @Project       CL2.0.dav
//----------------------------------------------------------------------------
// @Controller    Infineon XC886CLM-8FF
//
// @Compiler      Keil
//
// @Codegenerator 1.9
//
// @Description:  This file contains all function prototypes and macros for 
//                the WDT module.
//
//----------------------------------------------------------------------------
// @Date          2013-7-5 07:00:09
//
//****************************************************************************

// USER CODE BEGIN (WDT_Header,1)

// USER CODE END



#ifndef _WDT_H_
#define _WDT_H_

//****************************************************************************
// @Project Includes
//****************************************************************************

// USER CODE BEGIN (WDT_Header,2)

// USER CODE END


//****************************************************************************
// @Macros
//****************************************************************************

// USER CODE BEGIN (WDT_Header,3)

// USER CODE END


//****************************************************************************
// @Defines
//****************************************************************************

// USER CODE BEGIN (WDT_Header,4)

// USER CODE END


//****************************************************************************
// @Typedefs
//****************************************************************************

// USER CODE BEGIN (WDT_Header,5)

// USER CODE END


//****************************************************************************
// @Imported Global Variables
//****************************************************************************

// USER CODE BEGIN (WDT_Header,6)

// USER CODE END


//****************************************************************************
// @Global Variables
//****************************************************************************

// USER CODE BEGIN (WDT_Header,7)

// USER CODE END


//****************************************************************************
// @Prototypes Of Global Functions
//****************************************************************************

void WDT_vInit(void);
void WDT_vDisable(void);

// USER CODE BEGIN (WDT_Header,8)

// USER CODE END


//****************************************************************************
// @Macro         WDT_vRefresh() 
//
//----------------------------------------------------------------------------
// @Description   This macro refreshes the watchdog timer to the reload 
//                value. The interrupts are disabled during execution of the 
//                instructions that set bit WDTRS. 
//                Note: The user has to take care that this macro is called 
//                before expiry of the watchdog timer.
//                pls refer to the Note on WDT_vInit function Description
//
//----------------------------------------------------------------------------
// @Returnvalue   None
//
//----------------------------------------------------------------------------
// @Parameters    None
//
//----------------------------------------------------------------------------
// @Date          2013-7-5
//
//****************************************************************************

#define WDT_vRefresh()  { EA = 0; SET_RMAP(); WDTCON |= 0x02; \
   RESET_RMAP(); EA = 1; }


//****************************************************************************
// @Interrupt Vectors
//****************************************************************************


// USER CODE BEGIN (WDT_Header,9)

// USER CODE END

#endif  // ifndef _WDT_H_

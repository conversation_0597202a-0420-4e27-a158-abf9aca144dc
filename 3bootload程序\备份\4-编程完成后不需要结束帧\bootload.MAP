LX51 LINKER/LOCATER V4.58                                                               07/07/2013  18:32:47  PAGE 1


LX51 LINKER/LOCATER V4.58, INVOKED BY:
C:\KEIL\C51\BIN\LX51.EXE START_XC.obj, MAIN.obj, CAN.obj, boot.obj, XC88x_FLHANDLER.obj TO bootload CODE RESERVE (I:0XC1
>> -I:0XFF) CLASSES (XDATA (X:0XF000-X:0XF5FF), HDATA (X:0XF000-X:0XF5FF), CODE (C:0X7000-C:0X7EFF)) SEGMENTS (?C_C51STA
>> RTUP (C:0X7000), ?PR?MAIN?MAIN)


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE


INPUT MODULES INCLUDED:
  START_XC.obj (?C_STARTUP)
         COMMENT TYPE 0: AX51 V3.07d
  MAIN.obj (MAIN)
         COMMENT TYPE 0: C51 V9.50a
  CAN.obj (CAN)
         COMMENT TYPE 0: C51 V9.50a
  boot.obj (BOOT)
         COMMENT TYPE 0: C51 V9.50a
  XC88x_FLHANDLER.obj (XC88X_FLHANDLER)
         COMMENT TYPE 0: AX51 V3.07d
  C:\KEIL\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?ULCMP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?ULSHR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?COPY517)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDIDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDPDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDCODE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  bootload (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
X:000000H   X:00F000H   X:00F5FFH   000026H   XDATA
X:000000H   X:00F000H   X:00F5FFH             HDATA
C:000000H   C:007000H   C:007EFFH   000C03H   CODE
I:000000H   I:000000H   I:0000FFH   000041H   IDATA
C:000000H   C:000000H   C:00FFFFH   000010H   CONST
I:000000H   I:000000H   I:00007FH   000018H   DATA


MEMORY MAP OF MODULE:  bootload (?C_STARTUP)


LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 2


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   000017H   000010H   ---    ---      **GAP**
000018H   00001FH   000008H   ---    AT..     DATA           "REG BANK 3"
000020H   000035H   000016H   ---    ---      **GAP**
000036H   00003DH   000008H   ---    OFFS..   DATA           ?DT?XC88X_FLHANDLER?1
00003EH   00007FH   000042H   ---    ---      **GAP**
000080H   0000BFH   000040H   BYTE   OFFS..   IDATA          ?ID?BOOT?0
0000C0H   0000C0H   000001H   BYTE   UNIT     IDATA          ?STACK
0000C1H   0000FFH   00003FH   ---    ---      *** RESERVED MEMORY ***

* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO?START_XC?3
000003H   000012H   000010H   BYTE   UNIT     CONST          ?CO?BOOT
000013H   006FFFH   006FEDH   ---    ---      **GAP**
007000H   0070DAH   0000DBH   BYTE   UNIT     CODE           ?C_C51STARTUP
0070DBH   0070ECH   000012H   BYTE   INBLOCK  CODE           ?PR?MAIN?MAIN
0070EDH   0072DDH   0001F1H   BYTE   UNIT     CODE           ?C?LIB_CODE
0072DEH   00730AH   00002DH   BYTE   UNIT     CODE           ?PR?_DFLERASE?XC88X_FLHANDLER
00730BH   007337H   00002DH   BYTE   UNIT     CODE           ?PR?_PFLERASE?XC88X_FLHANDLER
007338H   007357H   000020H   BYTE   UNIT     CODE           ?PR?_FLPROG?XC88X_FLHANDLER
007358H   007373H   00001CH   BYTE   UNIT     CODE           ?C_INITSEG
007374H   007388H   000015H   BYTE   INBLOCK  CODE           ?PR?MAIN_VINIT?MAIN
007389H   0074B9H   000131H   BYTE   INBLOCK  CODE           ?PR?CAN_VINIT?CAN
0074BAH   0074DFH   000026H   BYTE   INBLOCK  CODE           ?PR?_CAN_VWRITEAMDATA?CAN
0074E0H   0074EFH   000010H   BYTE   INBLOCK  CODE           ?PR?_CAN_VSETLISTCOMMAND?CAN
0074F0H   007531H   000042H   BYTE   INBLOCK  CODE           ?PR?_CAN_UBREQUESTMSGOBJ?CAN
007532H   007582H   000051H   BYTE   INBLOCK  CODE           ?PR?_CAN_WAITTRANSMIT?BOOT
007583H   0075B6H   000034H   BYTE   INBLOCK  CODE           ?PR?_CAN_SENDACK?BOOT
0075B7H   007603H   00004DH   BYTE   INBLOCK  CODE           ?PR?DFLASHREAD?BOOT
007604H   007692H   00008FH   BYTE   INBLOCK  CODE           ?PR?_FLASHREAD?BOOT
007693H   0076B9H   000027H   BYTE   INBLOCK  CODE           ?PR?FLASH_WAIT?BOOT
0076BAH   0076D6H   00001DH   BYTE   INBLOCK  CODE           ?PR?CAN_SETWDTRESET?BOOT
0076D7H   00770AH   000034H   BYTE   INBLOCK  CODE           ?PR?_CAN_VTRANSMIT?CAN
00770BH   00777AH   000070H   BYTE   INBLOCK  CODE           ?PR?_CAN_VLOADDATA?CAN
00777BH   007797H   00001DH   BYTE   INBLOCK  CODE           ?PR?CHECKNULL?BOOT
007798H   0077BEH   000027H   BYTE   INBLOCK  CODE           ?PR?_CHECKFLPROG?BOOT
0077BFH   0077C6H   000008H   BYTE   UNIT     CODE           ?L?COM000E
0077C7H   0077D8H   000012H   BYTE   UNIT     CODE           ?L?COM0007
0077D9H   0077E3H   00000BH   BYTE   UNIT     CODE           ?L?COM0012
0077E4H   0077EAH   000007H   BYTE   UNIT     CODE           ?L?COM0018
0077EBH   0077F3H   000009H   BYTE   UNIT     CODE           ?L?COM0013
0077F4H   0077F9H   000006H   BYTE   UNIT     CODE           ?L?COM001E
0077FAH   0077FFH   000006H   ---    ---      **GAP**
007800H   007A47H   000248H   BYTE   INBLOCK  CODE           ?PR?BOOTMAIN?BOOT
007A48H   007B14H   0000CDH   BYTE   INBLOCK  CODE           ?PR?_CAN_READFIFO?BOOT
007B15H   007B4DH   000039H   BYTE   UNIT     CODE           ?L?COM0001
007B4EH   007B62H   000015H   BYTE   UNIT     CODE           ?L?COM000A
007B63H   007B71H   00000FH   BYTE   UNIT     CODE           ?L?COM000F
007B72H   007B7AH   000009H   BYTE   UNIT     CODE           ?L?COM001A
007B7BH   007B83H   000009H   BYTE   UNIT     CODE           ?L?COM001B
007B84H   007B90H   00000DH   BYTE   UNIT     CODE           ?L?COM0017
007B91H   007BA3H   000013H   BYTE   UNIT     CODE           ?L?COM0014
007BA4H   007BD5H   000032H   BYTE   UNIT     CODE           ?L?COM000D
007BD6H   007BEDH   000018H   BYTE   UNIT     CODE           ?L?COM0011
007BEEH   007BFCH   00000FH   BYTE   UNIT     CODE           ?L?COM0015
007BFDH   007C05H   000009H   BYTE   UNIT     CODE           ?L?COM001C

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
00F000H   00F01CH   00001DH   BYTE   UNIT     XDATA          _XDATA_GROUP_
00F01DH   00F025H   000009H   BYTE   UNIT     XDATA          ?XD?BOOT

LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 3




OVERLAY MAP OF MODULE:   bootload (?C_STARTUP)


FUNCTION/MODULE                XDATA_GROUP
--> CALLED FUNCTION/MODULE     START  STOP
==========================================
?C_C51STARTUP                  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                      ----- -----
  +--> MAIN_VINIT/MAIN
  +--> _CAN_SENDACK/BOOT
  +--> BOOTMAIN/BOOT

MAIN_VINIT/MAIN                ----- -----
  +--> CAN_VINIT/CAN

CAN_VINIT/CAN                  ----- -----
  +--> _CAN_VSETLISTCOMMAND/CAN
  +--> _CAN_VWRITEAMDATA/CAN

_CAN_VSETLISTCOMMAND/CAN       ----- -----
  +--> _CAN_VWRITEAMDATA/CAN

_CAN_VWRITEAMDATA/CAN          F000H F003H

_CAN_SENDACK/BOOT              F00AH F013H
  +--> ?CO?BOOT
  +--> _CAN_VLOADDATA/CAN
  +--> _CAN_VTRANSMIT/CAN
  +--> _CAN_WAITTRANSMIT/BOOT

?CO?BOOT                       ----- -----

_CAN_VLOADDATA/CAN             ----- -----

_CAN_VTRANSMIT/CAN             ----- -----

_CAN_WAITTRANSMIT/BOOT         F018H F01CH
  +--> _CAN_UBREQUESTMSGOBJ/CAN

_CAN_UBREQUESTMSGOBJ/CAN       ----- -----

BOOTMAIN/BOOT                  F000H F009H
  +--> ?CO?BOOT
  +--> _CAN_READFIFO/BOOT
  +--> _PFLERASE/XC88X_FLHANDLER
  +--> FLASH_WAIT/BOOT
  +--> _CAN_SENDACK/BOOT
  +--> CHECKNULL/BOOT
  +--> _FLASHREAD/BOOT
  +--> CAN_SETWDTRESET/BOOT
  +--> DFLASHREAD/BOOT
  +--> _DFLERASE/XC88X_FLHANDLER
  +--> _FLPROG/XC88X_FLHANDLER
  +--> _CHECKFLPROG/BOOT

_CAN_READFIFO/BOOT             F00AH F00BH

_PFLERASE/XC88X_FLHANDLER      ----- -----

FLASH_WAIT/BOOT                ----- -----
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 4


  +--> CAN_SETWDTRESET/BOOT

CAN_SETWDTRESET/BOOT           ----- -----

CHECKNULL/BOOT                 ----- -----

_FLASHREAD/BOOT                F00AH F017H
  +--> _CAN_VLOADDATA/CAN
  +--> _CAN_VTRANSMIT/CAN
  +--> _CAN_WAITTRANSMIT/BOOT

DFLASHREAD/BOOT                F00AH F014H
  +--> _CAN_VLOADDATA/CAN
  +--> _CAN_VTRANSMIT/CAN
  +--> _CAN_WAITTRANSMIT/BOOT

_DFLERASE/XC88X_FLHANDLER      ----- -----

_FLPROG/XC88X_FLHANDLER        ----- -----

_CHECKFLPROG/BOOT              ----- -----

?C_INITSEG                     ----- -----



PUBLIC SYMBOLS OF MODULE:  bootload (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      00000000H   NUMBER   ---       ?C?CODESEG
      01007284H   CODE     ---       ?C?COPY517
      010070FFH   CODE     ---       ?C?CSTOPTR
      010070EDH   CODE     ---       ?C?CSTPTR
      000000A2H   DATA     BYTE      ?C?DPSEL
      01007121H   CODE     ---       ?C?ILDIX
      010072CEH   CODE     ---       ?C?LLDCODE
      010072AAH   CODE     ---       ?C?LLDIDATA
      010072C2H   CODE     ---       ?C?LLDPDATA
      0100715BH   CODE     ---       ?C?LLDPTR
      010072B6H   CODE     ---       ?C?LLDXDATA
      01007187H   CODE     ---       ?C?LSTKXDATA
      0100717BH   CODE     ---       ?C?LSTXDATA
      01007137H   CODE     ---       ?C?ULCMP
      01007148H   CODE     ---       ?C?ULSHR
      00000000H   NUMBER   ---       ?C?XDATASEG
      01007096H   CODE     ---       ?C_START
      01000000H   CODE     NEAR LAB  ?C_STARTUP
      01007A48H   CODE     ---       _CAN_ReadFIFO
      01007587H   CODE     ---       _CAN_sendAck
      010074F0H   CODE     ---       _CAN_ubRequestMsgObj
      0100770BH   CODE     ---       _CAN_vLoadData
      010074E0H   CODE     ---       _CAN_vSetListCommand
      010076D7H   CODE     ---       _CAN_vTransmit
      010074BAH   CODE     ---       _CAN_vWriteAMData
      01007532H   CODE     ---       _CAN_waitTransmit
      01007798H   CODE     ---       _CheckFlProg
      010072DEH   CODE     NEAR LAB  _DFlErase
      01007604H   CODE     ---       _FlashRead
      01007338H   CODE     NEAR LAB  _FlProg
      0100730BH   CODE     NEAR LAB  _PFlErase
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
*SFR* 000000CAH   DATA     BYTE      ADC_CHCTR0
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 5


*SFR* 000000CBH   DATA     BYTE      ADC_CHCTR1
*SFR* 000000CCH   DATA     BYTE      ADC_CHCTR2
*SFR* 000000CDH   DATA     BYTE      ADC_CHCTR3
*SFR* 000000CEH   DATA     BYTE      ADC_CHCTR4
*SFR* 000000CFH   DATA     BYTE      ADC_CHCTR5
*SFR* 000000D2H   DATA     BYTE      ADC_CHCTR6
*SFR* 000000D3H   DATA     BYTE      ADC_CHCTR7
*SFR* 000000CBH   DATA     BYTE      ADC_CHINCR
*SFR* 000000CAH   DATA     BYTE      ADC_CHINFR
*SFR* 000000CDH   DATA     BYTE      ADC_CHINPR
*SFR* 000000CCH   DATA     BYTE      ADC_CHINSR
*SFR* 000000CAH   DATA     BYTE      ADC_CRCR1
*SFR* 000000CCH   DATA     BYTE      ADC_CRMR1
*SFR* 000000CBH   DATA     BYTE      ADC_CRPR1
*SFR* 000000CFH   DATA     BYTE      ADC_ETRCR
*SFR* 000000CFH   DATA     BYTE      ADC_EVINCR
*SFR* 000000CEH   DATA     BYTE      ADC_EVINFR
*SFR* 000000D3H   DATA     BYTE      ADC_EVINPR
*SFR* 000000D2H   DATA     BYTE      ADC_EVINSR
*SFR* 000000CAH   DATA     BYTE      ADC_GLOBCTR
*SFR* 000000CBH   DATA     BYTE      ADC_GLOBSTR
*SFR* 000000CEH   DATA     BYTE      ADC_INPCR0
*SFR* 000000CDH   DATA     BYTE      ADC_LCBR
*SFR* 000000D1H   DATA     BYTE      ADC_PAGE
*SFR* 000000CCH   DATA     BYTE      ADC_PRAR
*SFR* 000000CFH   DATA     BYTE      ADC_Q0R0
*SFR* 000000D2H   DATA     BYTE      ADC_QBUR0
*SFR* 000000D2H   DATA     BYTE      ADC_QINR0
*SFR* 000000CDH   DATA     BYTE      ADC_QMR0
*SFR* 000000CEH   DATA     BYTE      ADC_QSR0
*SFR* 000000CAH   DATA     BYTE      ADC_RCR0
*SFR* 000000CBH   DATA     BYTE      ADC_RCR1
*SFR* 000000CCH   DATA     BYTE      ADC_RCR2
*SFR* 000000CDH   DATA     BYTE      ADC_RCR3
*SFR* 000000CBH   DATA     BYTE      ADC_RESR0H
*SFR* 000000CAH   DATA     BYTE      ADC_RESR0L
*SFR* 000000CAH   DATA     WORD      ADC_RESR0LH
*SFR* 000000CDH   DATA     BYTE      ADC_RESR1H
*SFR* 000000CCH   DATA     BYTE      ADC_RESR1L
*SFR* 000000CCH   DATA     WORD      ADC_RESR1LH
*SFR* 000000CFH   DATA     BYTE      ADC_RESR2H
*SFR* 000000CEH   DATA     BYTE      ADC_RESR2L
*SFR* 000000CEH   DATA     WORD      ADC_RESR2LH
*SFR* 000000D3H   DATA     BYTE      ADC_RESR3H
*SFR* 000000D2H   DATA     BYTE      ADC_RESR3L
*SFR* 000000D2H   DATA     WORD      ADC_RESR3LH
*SFR* 000000CBH   DATA     BYTE      ADC_RESRA0H
*SFR* 000000CAH   DATA     BYTE      ADC_RESRA0L
*SFR* 000000CAH   DATA     WORD      ADC_RESRA0LH
*SFR* 000000CDH   DATA     BYTE      ADC_RESRA1H
*SFR* 000000CCH   DATA     BYTE      ADC_RESRA1L
*SFR* 000000CCH   DATA     WORD      ADC_RESRA1LH
*SFR* 000000CFH   DATA     BYTE      ADC_RESRA2H
*SFR* 000000CEH   DATA     BYTE      ADC_RESRA2L
*SFR* 000000CEH   DATA     WORD      ADC_RESRA2LH
*SFR* 000000D3H   DATA     BYTE      ADC_RESRA3H
*SFR* 000000D2H   DATA     BYTE      ADC_RESRA3L
*SFR* 000000D2H   DATA     WORD      ADC_RESRA3LH
*SFR* 000000CEH   DATA     BYTE      ADC_VFCR
*SFR* 000000F0H   DATA     BYTE      B
*SFR* 000000BDH   DATA     BYTE      BCON
*SFR* 000000BEH   DATA     BYTE      BG
      01007800H   CODE     ---       BootMain
*SFR* 000000C0H.1 DATA     BIT       C_T2
*SFR* 000000D8H   DATA     BYTE      CAN_ADCON
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 6


*SFR* 000000DAH   DATA     BYTE      CAN_ADH
*SFR* 000000D9H   DATA     BYTE      CAN_ADL
*SFR* 000000D9H   DATA     WORD      CAN_ADLH
*SFR* 000000DBH   DATA     BYTE      CAN_DATA0
*SFR* 000000DBH   DATA     WORD      CAN_DATA01
*SFR* 000000DCH   DATA     BYTE      CAN_DATA1
*SFR* 000000DDH   DATA     BYTE      CAN_DATA2
*SFR* 000000DDH   DATA     WORD      CAN_DATA23
*SFR* 000000DEH   DATA     BYTE      CAN_DATA3
      010076BAH   CODE     ---       CAN_setWDTReset
      01007389H   CODE     ---       CAN_vInit
*SFR* 000000FBH   DATA     BYTE      CCU6_CC60RH
*SFR* 000000FAH   DATA     BYTE      CCU6_CC60RL
*SFR* 000000FAH   DATA     WORD      CCU6_CC60RLH
*SFR* 000000FBH   DATA     BYTE      CCU6_CC60SRH
*SFR* 000000FAH   DATA     BYTE      CCU6_CC60SRL
*SFR* 000000FAH   DATA     WORD      CCU6_CC60SRLH
*SFR* 000000FDH   DATA     BYTE      CCU6_CC61RH
*SFR* 000000FCH   DATA     BYTE      CCU6_CC61RL
*SFR* 000000FCH   DATA     WORD      CCU6_CC61RLH
*SFR* 000000FDH   DATA     BYTE      CCU6_CC61SRH
*SFR* 000000FCH   DATA     BYTE      CCU6_CC61SRL
*SFR* 000000FCH   DATA     WORD      CCU6_CC61SRLH
*SFR* 000000FFH   DATA     BYTE      CCU6_CC62RH
*SFR* 000000FEH   DATA     BYTE      CCU6_CC62RL
*SFR* 000000FEH   DATA     WORD      CCU6_CC62RLH
*SFR* 000000FFH   DATA     BYTE      CCU6_CC62SRH
*SFR* 000000FEH   DATA     BYTE      CCU6_CC62SRL
*SFR* 000000FEH   DATA     WORD      CCU6_CC62SRLH
*SFR* 0000009BH   DATA     BYTE      CCU6_CC63RH
*SFR* 0000009AH   DATA     BYTE      CCU6_CC63RL
*SFR* 0000009AH   DATA     WORD      CCU6_CC63RLH
*SFR* 0000009BH   DATA     BYTE      CCU6_CC63SRH
*SFR* 0000009AH   DATA     BYTE      CCU6_CC63SRL
*SFR* 0000009AH   DATA     WORD      CCU6_CC63SRLH
*SFR* 000000A7H   DATA     BYTE      CCU6_CMPMODIFH
*SFR* 000000A6H   DATA     BYTE      CCU6_CMPMODIFL
*SFR* 000000FFH   DATA     BYTE      CCU6_CMPSTATH
*SFR* 000000FEH   DATA     BYTE      CCU6_CMPSTATL
*SFR* 0000009DH   DATA     BYTE      CCU6_IENH
*SFR* 0000009CH   DATA     BYTE      CCU6_IENL
*SFR* 0000009FH   DATA     BYTE      CCU6_INPH
*SFR* 0000009EH   DATA     BYTE      CCU6_INPL
*SFR* 0000009DH   DATA     BYTE      CCU6_ISH
*SFR* 0000009CH   DATA     BYTE      CCU6_ISL
*SFR* 000000A5H   DATA     BYTE      CCU6_ISRH
*SFR* 000000A4H   DATA     BYTE      CCU6_ISRL
*SFR* 000000A5H   DATA     BYTE      CCU6_ISSH
*SFR* 000000A4H   DATA     BYTE      CCU6_ISSL
*SFR* 000000A7H   DATA     BYTE      CCU6_MCMCTR
*SFR* 0000009BH   DATA     BYTE      CCU6_MCMOUTH
*SFR* 0000009AH   DATA     BYTE      CCU6_MCMOUTL
*SFR* 0000009FH   DATA     BYTE      CCU6_MCMOUTSH
*SFR* 0000009EH   DATA     BYTE      CCU6_MCMOUTSL
*SFR* 000000FDH   DATA     BYTE      CCU6_MODCTRH
*SFR* 000000FCH   DATA     BYTE      CCU6_MODCTRL
*SFR* 000000A3H   DATA     BYTE      CCU6_PAGE
*SFR* 0000009FH   DATA     BYTE      CCU6_PISEL0H
*SFR* 0000009EH   DATA     BYTE      CCU6_PISEL0L
*SFR* 000000A4H   DATA     BYTE      CCU6_PISEL2
*SFR* 000000A6H   DATA     BYTE      CCU6_PSLR
*SFR* 000000A5H   DATA     BYTE      CCU6_T12DTCH
*SFR* 000000A4H   DATA     BYTE      CCU6_T12DTCL
*SFR* 000000FBH   DATA     BYTE      CCU6_T12H
*SFR* 000000FAH   DATA     BYTE      CCU6_T12L
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 7


*SFR* 000000FAH   DATA     WORD      CCU6_T12LH
*SFR* 0000009BH   DATA     BYTE      CCU6_T12MSELH
*SFR* 0000009AH   DATA     BYTE      CCU6_T12MSELL
*SFR* 0000009DH   DATA     BYTE      CCU6_T12PRH
*SFR* 0000009CH   DATA     BYTE      CCU6_T12PRL
*SFR* 0000009CH   DATA     WORD      CCU6_T12PRLH
*SFR* 000000FDH   DATA     BYTE      CCU6_T13H
*SFR* 000000FCH   DATA     BYTE      CCU6_T13L
*SFR* 000000FCH   DATA     WORD      CCU6_T13LH
*SFR* 0000009FH   DATA     BYTE      CCU6_T13PRH
*SFR* 0000009EH   DATA     BYTE      CCU6_T13PRL
*SFR* 0000009EH   DATA     WORD      CCU6_T13PRLH
*SFR* 000000A7H   DATA     BYTE      CCU6_TCTR0H
*SFR* 000000A6H   DATA     BYTE      CCU6_TCTR0L
*SFR* 000000FBH   DATA     BYTE      CCU6_TCTR2H
*SFR* 000000FAH   DATA     BYTE      CCU6_TCTR2L
*SFR* 0000009DH   DATA     BYTE      CCU6_TCTR4H
*SFR* 0000009CH   DATA     BYTE      CCU6_TCTR4L
*SFR* 000000FFH   DATA     BYTE      CCU6_TRPCTRH
*SFR* 000000FEH   DATA     BYTE      CCU6_TRPCTRL
*SFR* 000000A0H   DATA     BIT       CD_BSY
*SFR* 000000A1H   DATA     BYTE      CD_CON
*SFR* 0000009BH   DATA     BYTE      CD_CORDXH
*SFR* 0000009AH   DATA     BYTE      CD_CORDXL
*SFR* 0000009DH   DATA     BYTE      CD_CORDYH
*SFR* 0000009CH   DATA     BYTE      CD_CORDYL
*SFR* 0000009FH   DATA     BYTE      CD_CORDZH
*SFR* 0000009EH   DATA     BYTE      CD_CORDZL
*SFR* 000000A0H   DATA     BYTE      CD_STATC
      0100777BH   CODE     ---       CheckNull
*SFR* 000000BAH   DATA     BYTE      CMCON
*SFR* 000000BEH   DATA     BYTE      COCON
*SFR* 000000C0H   DATA     BIT       CP_RL2
*SFR* 000000D0H.7 DATA     BIT       CY
      010075B7H   CODE     ---       DFlashRead
*SFR* 000000A0H.4 DATA     BIT       DMAP
*SFR* 00000083H   DATA     BYTE      DPH
*SFR* 00000082H   DATA     BYTE      DPL
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000E8H   DATA     BIT       EADC
*SFR* 000000E8H.4 DATA     BIT       ECCIP0
*SFR* 000000E8H.5 DATA     BIT       ECCIP1
*SFR* 000000E8H.6 DATA     BIT       ECCIP2
*SFR* 000000E8H.7 DATA     BIT       ECCIP3
*SFR* 000000A2H   DATA     BYTE      EO
*SFR* 000000A0H.2 DATA     BIT       EOC
*SFR* 000000A0H.1 DATA     BIT       ERROR
*SFR* 000000A8H.4 DATA     BIT       ES
*SFR* 000000E8H.1 DATA     BIT       ESSC
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H   DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000E8H.2 DATA     BIT       EX2
*SFR* 000000C0H.3 DATA     BIT       EXEN2
*SFR* 000000C0H.6 DATA     BIT       EXF2
*SFR* 000000B7H   DATA     BYTE      EXICON0
*SFR* 000000BAH   DATA     BYTE      EXICON1
*SFR* 000000E8H.3 DATA     BIT       EXM
*SFR* 000000D0H.5 DATA     BIT       F0
*SFR* 000000D0H.1 DATA     BIT       F1
*SFR* 000000E9H   DATA     BYTE      FDCON
*SFR* 000000EBH   DATA     BYTE      FDRES
*SFR* 000000EAH   DATA     BYTE      FDSTEP
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 8


*SFR* 000000BDH   DATA     BYTE      FEAH
*SFR* 000000BCH   DATA     BYTE      FEAL
      01007697H   CODE     ---       Flash_Wait
*SFR* 000000F7H   DATA     BYTE      HWBPDR
*SFR* 000000F6H   DATA     BYTE      HWBPSR
*SFR* 000000B3H   DATA     BYTE      ID
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*SFR* 000000A8H   DATA     BYTE      IEN0
*SFR* 000000E8H   DATA     BYTE      IEN1
*SFR* 000000B0H.1 DATA     BIT       IERR
*SFR* 000000A0H.3 DATA     BIT       INT_EN
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 000000F8H   DATA     BYTE      IP1
*SFR* 000000B9H   DATA     BYTE      IPH
*SFR* 000000F9H   DATA     BYTE      IPH1
*SFR* 000000B4H   DATA     BYTE      IRCON0
*SFR* 000000B5H   DATA     BYTE      IRCON1
*SFR* 000000B6H   DATA     BYTE      IRCON2
*SFR* 000000B4H   DATA     BYTE      IRCON3
*SFR* 000000B5H   DATA     BYTE      IRCON4
*SFR* 000000B0H   DATA     BIT       IRDY
*SFR* 00000088H   DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
*SFR* 000000A0H.5 DATA     BIT       KEEPX
*SFR* 000000A0H.6 DATA     BIT       KEEPY
*SFR* 000000A0H.7 DATA     BIT       KEEPZ
      010070DBH   CODE     ---       main
      01007374H   CODE     ---       MAIN_vInit
*SFR* 000000B0H.2 DATA     BIT       MDU_BSY
*SFR* 000000B2H   DATA     BYTE      MDU_MD0
*SFR* 000000B3H   DATA     BYTE      MDU_MD1
*SFR* 000000B4H   DATA     BYTE      MDU_MD2
*SFR* 000000B5H   DATA     BYTE      MDU_MD3
*SFR* 000000B6H   DATA     BYTE      MDU_MD4
*SFR* 000000B7H   DATA     BYTE      MDU_MD5
*SFR* 000000B1H   DATA     BYTE      MDU_MDUCON
*SFR* 000000B0H   DATA     BYTE      MDU_MDUSTAT
*SFR* 000000B2H   DATA     BYTE      MDU_MR0
*SFR* 000000B3H   DATA     BYTE      MDU_MR1
*SFR* 000000B4H   DATA     BYTE      MDU_MR2
*SFR* 000000B5H   DATA     BYTE      MDU_MR3
*SFR* 000000B6H   DATA     BYTE      MDU_MR4
*SFR* 000000B7H   DATA     BYTE      MDU_MR5
*SFR* 00000083H   DATA     BYTE      MEM_DPH
*SFR* 00000082H   DATA     BYTE      MEM_DPL
*SFR* 000000BBH   DATA     BYTE      MEM_NMICON
*SFR* 000000BCH   DATA     BYTE      MEM_NMISR
*SFR* 00000096H   DATA     BYTE      MEX3
*SFR* 000000E9H   DATA     BYTE      MISC_CON
*SFR* 000000F3H   DATA     BYTE      MMBPCR
*SFR* 000000F1H   DATA     BYTE      MMCR
*SFR* 000000E9H   DATA     BYTE      MMCR2
*SFR* 000000F5H   DATA     BYTE      MMDR
*SFR* 000000F4H   DATA     BYTE      MMICR
*SFR* 000000F2H   DATA     BYTE      MMSR
*SFR* 000000EBH   DATA     BYTE      MMWR1
*SFR* 000000ECH   DATA     BYTE      MMWR2
*SFR* 000000B3H   DATA     BYTE      MODPISEL
*SFR* 000000B7H   DATA     BYTE      MODPISEL1
*SFR* 000000BAH   DATA     BYTE      MODPISEL2
*SFR* 000000BDH   DATA     BYTE      MODSUSP
*SFR* 000000BBH   DATA     BYTE      NMICON
*SFR* 000000BCH   DATA     BYTE      NMISR
*SFR* 000000B6H   DATA     BYTE      OSC_CON
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 9


*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H   DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0_ALTSEL0
*SFR* 00000086H   DATA     BYTE      P0_ALTSEL1
*SFR* 00000080H   DATA     BYTE      P0_DATA
*SFR* 00000086H   DATA     BYTE      P0_DIR
*SFR* 00000080H   DATA     BYTE      P0_OD
*SFR* 00000086H   DATA     BYTE      P0_PUDEN
*SFR* 00000080H   DATA     BYTE      P0_PUDSEL
*SFR* 00000090H   DATA     BYTE      P1_ALTSEL0
*SFR* 00000091H   DATA     BYTE      P1_ALTSEL1
*SFR* 00000090H   DATA     BYTE      P1_DATA
*SFR* 00000091H   DATA     BYTE      P1_DIR
*SFR* 00000090H   DATA     BYTE      P1_OD
*SFR* 00000091H   DATA     BYTE      P1_PUDEN
*SFR* 00000090H   DATA     BYTE      P1_PUDSEL
*SFR* 000000A0H   DATA     BYTE      P2_DATA
*SFR* 000000A1H   DATA     BYTE      P2_DIR
*SFR* 000000A1H   DATA     BYTE      P2_PUDEN
*SFR* 000000A0H   DATA     BYTE      P2_PUDSEL
*SFR* 000000B0H   DATA     BYTE      P3_ALTSEL0
*SFR* 000000B1H   DATA     BYTE      P3_ALTSEL1
*SFR* 000000B0H   DATA     BYTE      P3_DATA
*SFR* 000000B1H   DATA     BYTE      P3_DIR
*SFR* 000000B0H   DATA     BYTE      P3_OD
*SFR* 000000B1H   DATA     BYTE      P3_PUDEN
*SFR* 000000B0H   DATA     BYTE      P3_PUDSEL
*SFR* 000000C8H   DATA     BYTE      P4_ALTSEL0
*SFR* 000000C9H   DATA     BYTE      P4_ALTSEL1
*SFR* 000000C8H   DATA     BYTE      P4_DATA
*SFR* 000000C9H   DATA     BYTE      P4_DIR
*SFR* 000000C8H   DATA     BYTE      P4_OD
*SFR* 000000C9H   DATA     BYTE      P4_PUDEN
*SFR* 000000C8H   DATA     BYTE      P4_PUDSEL
*SFR* 00000092H   DATA     BYTE      P5_ALTSEL0
*SFR* 00000093H   DATA     BYTE      P5_ALTSEL1
*SFR* 00000092H   DATA     BYTE      P5_DATA
*SFR* 00000093H   DATA     BYTE      P5_DIR
*SFR* 00000092H   DATA     BYTE      P5_OD
*SFR* 00000093H   DATA     BYTE      P5_PUDEN
*SFR* 00000092H   DATA     BYTE      P5_PUDSEL
*SFR* 000000F8H   DATA     BIT       PADC
*SFR* 000000BBH   DATA     BYTE      PASSWD
*SFR* 000000F8H.4 DATA     BIT       PCCIP0
*SFR* 000000F8H.5 DATA     BIT       PCCIP1
*SFR* 000000F8H.6 DATA     BIT       PCCIP2
*SFR* 000000F8H.7 DATA     BIT       PCCIP3
*SFR* 00000087H   DATA     BYTE      PCON
*SFR* 000000B7H   DATA     BYTE      PLL_CON
*SFR* 000000EAH   DATA     BYTE      PLL_CON1
*SFR* 000000B4H   DATA     BYTE      PMCON0
*SFR* 000000B5H   DATA     BYTE      PMCON1
*SFR* 000000BBH   DATA     BYTE      PMCON2
*SFR* 000000B2H   DATA     BYTE      PORT_PAGE
*SFR* 000000B8H.4 DATA     BIT       PS
*SFR* 000000F8H.1 DATA     BIT       PSSC
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H   DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
*SFR* 000000F8H.2 DATA     BIT       PX2
*SFR* 000000F8H.3 DATA     BIT       PXM
*SFR* 00000098H.2 DATA     BIT       RB8
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 10


*SFR* 000000C8H.2 DATA     BIT       RB8_1
*SFR* 00000098H.4 DATA     BIT       REN
*SFR* 000000C8H.4 DATA     BIT       REN_1
*SFR* 00000098H   DATA     BIT       RI
*SFR* 000000C8H   DATA     BIT       RI_1
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000098H   DATA     BYTE      SCON
*SFR* 000000BFH   DATA     BYTE      SCU_PAGE
*SFR* 00000098H.7 DATA     BIT       SM0
*SFR* 000000C8H.7 DATA     BIT       SM0_1
*SFR* 00000098H.6 DATA     BIT       SM1
*SFR* 000000C8H.6 DATA     BIT       SM1_1
*SFR* 00000098H.5 DATA     BIT       SM2
*SFR* 000000C8H.5 DATA     BIT       SM2_1
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000AFH   DATA     BYTE      SSC_BRH
*SFR* 000000AEH   DATA     BYTE      SSC_BRL
*SFR* 000000ABH   DATA     BYTE      SSC_CONH_O
*SFR* 000000ABH   DATA     BYTE      SSC_CONH_P
*SFR* 000000AAH   DATA     BYTE      SSC_CONL_O
*SFR* 000000AAH   DATA     BYTE      SSC_CONL_P
*SFR* 000000A9H   DATA     BYTE      SSC_PISEL
*SFR* 000000ADH   DATA     BYTE      SSC_RBL
*SFR* 000000ACH   DATA     BYTE      SSC_TBL
*SFR* 0000008FH   DATA     BYTE      SYSCON0
*SFR* 000000C3H   DATA     BYTE      T21_RC2H
*SFR* 000000C2H   DATA     BYTE      T21_RC2L
*SFR* 000000C2H   DATA     WORD      T21_RC2LH
*SFR* 000000C0H   DATA     BYTE      T21_T2CON
*SFR* 000000C5H   DATA     BYTE      T21_T2H
*SFR* 000000C4H   DATA     BYTE      T21_T2L
*SFR* 000000C4H   DATA     WORD      T21_T2LH
*SFR* 000000C1H   DATA     BYTE      T21_T2MOD
*SFR* 000000C3H   DATA     BYTE      T2_RC2H
*SFR* 000000C2H   DATA     BYTE      T2_RC2L
*SFR* 000000C2H   DATA     WORD      T2_RC2LH
*SFR* 000000C0H   DATA     BYTE      T2_T2CON
*SFR* 000000C5H   DATA     BYTE      T2_T2H
*SFR* 000000C4H   DATA     BYTE      T2_T2L
*SFR* 000000C4H   DATA     WORD      T2_T2LH
*SFR* 000000C1H   DATA     BYTE      T2_T2MOD
*SFR* 00000098H.3 DATA     BIT       TB8
*SFR* 000000C8H.3 DATA     BIT       TB8_1
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 000000C0H.7 DATA     BIT       TF2
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 00000098H.1 DATA     BIT       TI
*SFR* 000000C8H.1 DATA     BIT       TI_1
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 000000C0H.2 DATA     BIT       TR2
*SFR* 000000CAH   DATA     BYTE      UART1_BCON
*SFR* 000000CBH   DATA     BYTE      UART1_BG
*SFR* 000000CCH   DATA     BYTE      UART1_FDCON
*SFR* 000000CEH   DATA     BYTE      UART1_FDRES
*SFR* 000000CDH   DATA     BYTE      UART1_FDSTEP
*SFR* 000000C9H   DATA     BYTE      UART1_SBUF
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 11


*SFR* 000000C8H   DATA     BYTE      UART1_SCON
*SFR* 000000BBH   DATA     BYTE      WDTCON
*SFR* 000000BFH   DATA     BYTE      WDTH
*SFR* 000000BEH   DATA     BYTE      WDTL
*SFR* 000000BCH   DATA     BYTE      WDTREL
*SFR* 000000BDH   DATA     BYTE      WDTWINB
      00000080H   IDATA    ---       WLBuf
*SFR* 000000B3H   DATA     BYTE      XADDRH



SYMBOL TABLE OF MODULE:  bootload (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      000000A2H   PUBLIC    DATA     BYTE      ?C?DPSEL
      01000000H   PUBLIC    CODE     NEAR LAB  ?C_STARTUP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000F0H   SFRSYM    DATA     BYTE      B
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      00000096H   SFRSYM    DATA     BYTE      MEX3
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000EAH   SFRSYM    DATA     BYTE      PLL_CON1
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      01007013H   SYMBOL    CODE     NEAR LAB  DELAYXTAL
      01007011H   SYMBOL    CODE     NEAR LAB  DELAYXTAL0
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01007044H   SYMBOL    CODE     NEAR LAB  IDATALOOP
      00000001H   SYMBOL    NUMBER   ---       LIN_BSL
      00000001H   SYMBOL    NUMBER   ---       LIN_NAC
      00000001H   SYMBOL    NUMBER   ---       LIN_NAD
      0000000AH   SYMBOL    NUMBER   ---       NDIV
      00000002H   SYMBOL    NUMBER   ---       NDIV_XC86X
      00000018H   SYMBOL    NUMBER   ---       NDIV_XC87X_PLL_CON
      00000020H   SYMBOL    NUMBER   ---       NDIV_XC87X_PLL_CON1
      0000000AH   SYMBOL    NUMBER   ---       NDIV_XC88X
      00000000H   SYMBOL    NUMBER   ---       NR_XC87X
      00000000H   SYMBOL    NUMBER   ---       OD_XC87X
      01007017H   SYMBOL    CODE     NEAR LAB  OSCR_NOTSET
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      0000F000H   SYMBOL    NUMBER   ---       PDATASTART
      000000F0H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      01007000H   SYMBOL    CODE     NEAR LAB  STARTUP1
      01007034H   SYMBOL    CODE     NEAR LAB  WAIT_LOCK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00010000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       XC82X_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC864_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC866_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC874_CHIP_13FF
      00000000H   SYMBOL    NUMBER   ---       XC874_CHIP_16FF
      00000000H   SYMBOL    NUMBER   ---       XC878_CHIP_13FF
      00000000H   SYMBOL    NUMBER   ---       XC878_CHIP_16FF
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 12


      00000001H   SYMBOL    NUMBER   ---       XC88X_CHIP
      00000600H   SYMBOL    NUMBER   ---       XDATALEN
      0100704FH   SYMBOL    CODE     NEAR LAB  XDATALOOP
      0000F000H   SYMBOL    NUMBER   ---       XDATASTART
      00000001H   SYMBOL    NUMBER   ---       XTAL

      01000000H   BLOCK     CODE     NEAR LAB  LVL=0
      01000000H   LINE      CODE     ---       #312
      ---         BLOCKEND  ---      ---       LVL=0

      01007000H   BLOCK     CODE     NEAR LAB  LVL=0
      01007000H   LINE      CODE     ---       #367
      01007003H   LINE      CODE     ---       #368
      01007006H   LINE      CODE     ---       #369
      01007009H   LINE      CODE     ---       #370
      0100700CH   LINE      CODE     ---       #371
      0100700FH   LINE      CODE     ---       #373
      01007011H   LINE      CODE     ---       #375
      01007013H   LINE      CODE     ---       #377
      01007015H   LINE      CODE     ---       #378
      01007017H   LINE      CODE     ---       #382
      01007019H   LINE      CODE     ---       #383
      0100701CH   LINE      CODE     ---       #388
      0100701EH   LINE      CODE     ---       #389
      01007020H   LINE      CODE     ---       #390
      01007023H   LINE      CODE     ---       #394
      01007026H   LINE      CODE     ---       #395
      01007029H   LINE      CODE     ---       #396
      0100702CH   LINE      CODE     ---       #397
      0100702FH   LINE      CODE     ---       #400
      01007032H   LINE      CODE     ---       #401
      01007034H   LINE      CODE     ---       #409
      01007036H   LINE      CODE     ---       #410
      01007038H   LINE      CODE     ---       #411
      0100703BH   LINE      CODE     ---       #413
      0100703EH   LINE      CODE     ---       #414
      01007041H   LINE      CODE     ---       #419
      01007043H   LINE      CODE     ---       #420
      01007044H   LINE      CODE     ---       #421
      01007045H   LINE      CODE     ---       #422
      01007047H   LINE      CODE     ---       #439
      0100704AH   LINE      CODE     ---       #440
      0100704CH   LINE      CODE     ---       #444
      0100704EH   LINE      CODE     ---       #446
      0100704FH   LINE      CODE     ---       #447
      01007050H   LINE      CODE     ---       #448
      01007051H   LINE      CODE     ---       #449
      01007053H   LINE      CODE     ---       #450
      01007055H   LINE      CODE     ---       #486
      01007058H   LINE      CODE     ---       #488
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      010070DBH   PUBLIC    CODE     ---       main
      01007374H   PUBLIC    CODE     ---       MAIN_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 13


      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 14


      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 15


      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 16


      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 17


      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 18


      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      01007374H   BLOCK     CODE     ---       LVL=0
      01007374H   LINE      ---      ---       #122
      01007374H   LINE      ---      ---       #123
      01007374H   LINE      ---      ---       #138
      01007377H   LINE      ---      ---       #140
      0100737AH   LINE      ---      ---       #142
      0100737DH   LINE      ---      ---       #151
      0100737FH   LINE      ---      ---       #156
      01007382H   LINE      ---      ---       #157
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 19


      01007384H   LINE      ---      ---       #158
      01007386H   LINE      ---      ---       #159
      01007388H   LINE      ---      ---       #172
      ---         BLOCKEND  ---      ---       LVL=0

      010070DBH   BLOCK     CODE     ---       LVL=0
      010070DBH   LINE      ---      ---       #196
      010070DBH   LINE      ---      ---       #197
      010070DBH   LINE      ---      ---       #202
      010070DDH   LINE      ---      ---       #205
      010070E0H   LINE      ---      ---       #206
      010070E3H   LINE      ---      ---       #207
      010070E8H   LINE      ---      ---       #211
      010070E8H   LINE      ---      ---       #212
      010070E8H   LINE      ---      ---       #215
      010070EBH   LINE      ---      ---       #219
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       CAN
      0100770BH   PUBLIC    CODE     ---       _CAN_vLoadData
      010076D7H   PUBLIC    CODE     ---       _CAN_vTransmit
      010074F0H   PUBLIC    CODE     ---       _CAN_ubRequestMsgObj
      010074E0H   PUBLIC    CODE     ---       _CAN_vSetListCommand
      010074BAH   PUBLIC    CODE     ---       _CAN_vWriteAMData
      01007389H   PUBLIC    CODE     ---       CAN_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 20


      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 21


      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 22


      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 23


      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 24


      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 25


      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      01007389H   BLOCK     CODE     ---       LVL=0
      01007389H   LINE      ---      ---       #124
      01007389H   LINE      ---      ---       #125
      01007389H   LINE      ---      ---       #140
      0100738FH   LINE      ---      ---       #141
      01007392H   LINE      ---      ---       #142
      01007397H   LINE      ---      ---       #144
      0100739AH   LINE      ---      ---       #145
      0100739CH   LINE      ---      ---       #156
      010073A2H   LINE      ---      ---       #157
      010073A5H   LINE      ---      ---       #158
      010073ADH   LINE      ---      ---       #163
      010073B0H   LINE      ---      ---       #168
      010073B8H   LINE      ---      ---       #184
      010073C0H   LINE      ---      ---       #204
      010073C3H   LINE      ---      ---       #205
      010073C6H   LINE      ---      ---       #206
      010073CEH   LINE      ---      ---       #217
      010073D1H   LINE      ---      ---       #218
      010073D9H   LINE      ---      ---       #231
      010073DCH   LINE      ---      ---       #232
      010073DEH   LINE      ---      ---       #233
      010073E0H   LINE      ---      ---       #234
      010073E8H   LINE      ---      ---       #247
      010073EEH   LINE      ---      ---       #248
      010073F1H   LINE      ---      ---       #249
      010073F9H   LINE      ---      ---       #259
      010073FCH   LINE      ---      ---       #260
      010073FFH   LINE      ---      ---       #261
      01007402H   LINE      ---      ---       #263
      01007405H   LINE      ---      ---       #264
      01007408H   LINE      ---      ---       #271
      0100740DH   LINE      ---      ---       #274
      01007427H   LINE      ---      ---       #291
      0100742DH   LINE      ---      ---       #293
      01007430H   LINE      ---      ---       #330
      01007435H   LINE      ---      ---       #351
      0100743CH   LINE      ---      ---       #363
      0100744AH   LINE      ---      ---       #376
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 26


      0100744DH   LINE      ---      ---       #413
      01007452H   LINE      ---      ---       #422
      01007459H   LINE      ---      ---       #441
      0100745FH   LINE      ---      ---       #454
      01007462H   LINE      ---      ---       #491
      01007467H   LINE      ---      ---       #500
      0100746EH   LINE      ---      ---       #519
      01007474H   LINE      ---      ---       #532
      0100747FH   LINE      ---      ---       #544
      0100748AH   LINE      ---      ---       #569
      0100748FH   LINE      ---      ---       #578
      01007496H   LINE      ---      ---       #596
      010074A0H   LINE      ---      ---       #730
      010074A6H   LINE      ---      ---       #731
      010074AEH   LINE      ---      ---       #732
      010074B1H   LINE      ---      ---       #733
      010074B9H   LINE      ---      ---       #742
      ---         BLOCKEND  ---      ---       LVL=0

      010074BAH   BLOCK     CODE     ---       LVL=0
      010074BAH   LINE      ---      ---       #781
      010074BAH   LINE      ---      ---       #782
      00000004H   SYMBOL    DATA     DWORD     ulValue
      010074BAH   BLOCK     CODE     NEAR LAB  LVL=1
      010074BAH   LINE      ---      ---       #784
      010074BFH   LINE      ---      ---       #786
      010074C5H   LINE      ---      ---       #787
      010074CBH   LINE      ---      ---       #788
      010074D1H   LINE      ---      ---       #789
      010074D7H   LINE      ---      ---       #790
      010074DFH   LINE      ---      ---       #791
      0200F000H   SYMBOL    XDATA    ---       ulData
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010074E0H   BLOCK     CODE     ---       LVL=0
      010074E0H   LINE      ---      ---       #816
      010074E0H   LINE      ---      ---       #817
      010074E0H   LINE      ---      ---       #818
      010074E2H   LINE      ---      ---       #819
      010074E5H   LINE      ---      ---       #820
      010074EAH   LINE      ---      ---       #822
      010074EDH   LINE      ---      ---       #823
      010074EFH   LINE      ---      ---       #824
      00000004H   SYMBOL    DATA     DWORD     ulVal
      ---         BLOCKEND  ---      ---       LVL=0

      010074F0H   BLOCK     CODE     ---       LVL=0
      010074F0H   LINE      ---      ---       #859
      010074F0H   LINE      ---      ---       #860
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      010074F0H   BLOCK     CODE     NEAR LAB  LVL=1
      010074F0H   LINE      ---      ---       #861
      010074F2H   LINE      ---      ---       #863
      01007503H   LINE      ---      ---       #865
      0100750EH   LINE      ---      ---       #868
      01007513H   LINE      ---      ---       #869
      01007513H   LINE      ---      ---       #870
      01007521H   LINE      ---      ---       #871
      01007523H   LINE      ---      ---       #872
      01007523H   LINE      ---      ---       #873
      0100752FH   LINE      ---      ---       #875
      01007531H   LINE      ---      ---       #876
      00000005H   SYMBOL    DATA     BYTE      ubReturn
      ---         BLOCKEND  CODE     ---       LVL=1
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 27


      ---         BLOCKEND  CODE     ---       LVL=0

      010076D7H   BLOCK     CODE     ---       LVL=0
      010076D7H   LINE      ---      ---       #905
      010076D7H   LINE      ---      ---       #906
      010076D7H   LINE      ---      ---       #907
      010076E8H   LINE      ---      ---       #909
      010076FEH   LINE      ---      ---       #912
      0100770AH   LINE      ---      ---       #914
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      ---         BLOCKEND  ---      ---       LVL=0

      0100770BH   BLOCK     CODE     ---       LVL=0
      0100770BH   LINE      ---      ---       #945
      0100770BH   LINE      ---      ---       #946
      0100770BH   LINE      ---      ---       #947
      0100771CH   LINE      ---      ---       #949
      01007731H   LINE      ---      ---       #953
      01007734H   LINE      ---      ---       #956
      01007737H   LINE      ---      ---       #959
      0100773AH   LINE      ---      ---       #961
      01007742H   LINE      ---      ---       #964
      01007745H   LINE      ---      ---       #966
      0100774CH   LINE      ---      ---       #968
      01007754H   LINE      ---      ---       #971
      01007757H   LINE      ---      ---       #974
      0100775AH   LINE      ---      ---       #976
      0100776EH   LINE      ---      ---       #978
      0100777AH   LINE      ---      ---       #980
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      00000001H   SYMBOL    DATA     ---       ulpubData
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       BOOT
      00000080H   PUBLIC    IDATA    ---       WLBuf
      01007800H   PUBLIC    CODE     ---       BootMain
      010075B7H   PUBLIC    CODE     ---       DFlashRead
      01007798H   PUBLIC    CODE     ---       _CheckFlProg
      0100777BH   PUBLIC    CODE     ---       CheckNull
      01007A48H   PUBLIC    CODE     ---       _CAN_ReadFIFO
      01007604H   PUBLIC    CODE     ---       _FlashRead
      01007697H   PUBLIC    CODE     ---       Flash_Wait
      01007587H   PUBLIC    CODE     ---       _CAN_sendAck
      01007532H   PUBLIC    CODE     ---       _CAN_waitTransmit
      010076BAH   PUBLIC    CODE     ---       CAN_setWDTReset
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 28


      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 29


      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 30


      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 31


      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 32


      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 33


      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH
      01000003H   SYMBOL    CONST    ---       _?ix1000
      0100000BH   SYMBOL    CONST    ---       _?ix1001

      010076BAH   BLOCK     CODE     ---       LVL=0
      010076BAH   LINE      ---      ---       #8
      010076BAH   LINE      ---      ---       #9
      010076BAH   LINE      ---      ---       #10
      010076BDH   LINE      ---      ---       #11
      010076C0H   LINE      ---      ---       #13
      010076C3H   LINE      ---      ---       #14
      010076C6H   LINE      ---      ---       #15
      010076C9H   LINE      ---      ---       #16
      010076CCH   LINE      ---      ---       #17
      010076CFH   LINE      ---      ---       #19
      010076D2H   LINE      ---      ---       #20
      010076D5H   LINE      ---      ---       #22
      ---         BLOCKEND  ---      ---       LVL=0

      01007532H   BLOCK     CODE     ---       LVL=0
      01007532H   LINE      ---      ---       #26
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 34


      01007537H   LINE      ---      ---       #27
      0200F018H   SYMBOL    XDATA    BYTE      RgMsgobj
      01007537H   BLOCK     CODE     NEAR LAB  LVL=1
      01007537H   LINE      ---      ---       #29
      01007582H   LINE      ---      ---       #30
      0200F019H   SYMBOL    XDATA    DWORD     i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007583H   BLOCK     CODE     ---       LVL=0
      01007587H   LINE      ---      ---       #37
      0100758FH   LINE      ---      ---       #38
      0200F00AH   SYMBOL    XDATA    BYTE      Ack0
      0200F00BH   SYMBOL    XDATA    BYTE      Ack1
      0100758FH   BLOCK     CODE     NEAR LAB  LVL=1
      0100758FH   LINE      ---      ---       #39
      0100759DH   LINE      ---      ---       #41
      010075A5H   LINE      ---      ---       #42
      010075ADH   LINE      ---      ---       #44
      0200F00CH   SYMBOL    XDATA    ---       Arrtmp
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007693H   BLOCK     CODE     ---       LVL=0
      01007697H   LINE      ---      ---       #55
      01007697H   LINE      ---      ---       #56
      01007697H   BLOCK     CODE     NEAR LAB  LVL=1
      01007697H   LINE      ---      ---       #59
      0100769AH   LINE      ---      ---       #60
      0100769DH   LINE      ---      ---       #61
      0100769EH   LINE      ---      ---       #62
      0100769EH   LINE      ---      ---       #63
      010076A0H   LINE      ---      ---       #64
      010076A0H   LINE      ---      ---       #65
      010076A2H   LINE      ---      ---       #66
      010076A2H   LINE      ---      ---       #67
      010076A7H   LINE      ---      ---       #68
      010076A7H   LINE      ---      ---       #69
      010076ABH   LINE      ---      ---       #71
      010076ABH   LINE      ---      ---       #72
      010076B0H   LINE      ---      ---       #73
      010076B5H   LINE      ---      ---       #74
      00000005H   SYMBOL    DATA     BYTE      ubCount0
      00000006H   SYMBOL    DATA     BYTE      ubCount1
      00000007H   SYMBOL    DATA     BYTE      ubCount2
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007604H   BLOCK     CODE     ---       LVL=0
      01007604H   LINE      ---      ---       #79
      0100760CH   LINE      ---      ---       #80
      0200F00AH   SYMBOL    XDATA    WORD      Lenth
      0100760CH   BLOCK     CODE     NEAR LAB  LVL=1
      0100760CH   LINE      ---      ---       #85
      01007615H   LINE      ---      ---       #87
      01007630H   LINE      ---      ---       #88
      01007630H   LINE      ---      ---       #89
      01007639H   LINE      ---      ---       #90
      01007642H   LINE      ---      ---       #91
      0100764BH   LINE      ---      ---       #92
      01007654H   LINE      ---      ---       #93
      0100765DH   LINE      ---      ---       #94
      01007666H   LINE      ---      ---       #95
      0100766FH   LINE      ---      ---       #96
      01007678H   LINE      ---      ---       #98
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 35


      01007682H   LINE      ---      ---       #101
      01007692H   LINE      ---      ---       #103
      0200F00CH   SYMBOL    XDATA    WORD      i
      0200F00EH   SYMBOL    XDATA    ---       pBootCode
      0200F010H   SYMBOL    XDATA    ---       tmp
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007A48H   BLOCK     CODE     ---       LVL=0
      01007A48H   LINE      ---      ---       #131
      01007A48H   LINE      ---      ---       #132
      00000001H   SYMBOL    DATA     ---       ulCANData
      01007A48H   BLOCK     CODE     NEAR LAB  LVL=1
      01007A48H   LINE      ---      ---       #135
      01007A4EH   LINE      ---      ---       #138
      01007A56H   LINE      ---      ---       #139
      01007A59H   LINE      ---      ---       #141
      01007A62H   LINE      ---      ---       #143
      01007A64H   LINE      ---      ---       #144
      01007A6AH   LINE      ---      ---       #145
      01007A6AH   LINE      ---      ---       #146
      01007A71H   LINE      ---      ---       #147
      01007A79H   LINE      ---      ---       #148
      01007A79H   LINE      ---      ---       #149
      01007A7AH   LINE      ---      ---       #150
      01007A7CH   LINE      ---      ---       #152
      01007A7CH   LINE      ---      ---       #153
      01007A82H   LINE      ---      ---       #154
      01007A82H   LINE      ---      ---       #157
      01007A86H   LINE      ---      ---       #158
      01007A86H   LINE      ---      ---       #159
      01007A93H   LINE      ---      ---       #160
      01007A96H   LINE      ---      ---       #161
      01007A96H   LINE      ---      ---       #163
      01007AA9H   LINE      ---      ---       #165
      01007AAEH   LINE      ---      ---       #166
      01007AB6H   LINE      ---      ---       #167
      01007ABEH   LINE      ---      ---       #168
      01007AC6H   LINE      ---      ---       #170
      01007AC9H   LINE      ---      ---       #172
      01007AD1H   LINE      ---      ---       #173
      01007AD9H   LINE      ---      ---       #174
      01007AE1H   LINE      ---      ---       #175
      01007AE9H   LINE      ---      ---       #176
      01007AF1H   LINE      ---      ---       #178
      01007AFAH   LINE      ---      ---       #180
      01007AFFH   LINE      ---      ---       #181
      01007AFFH   LINE      ---      ---       #182
      01007B06H   LINE      ---      ---       #183
      01007B09H   LINE      ---      ---       #184
      01007B09H   LINE      ---      ---       #185
      01007B0FH   LINE      ---      ---       #186
      01007B0FH   LINE      ---      ---       #187
      01007B14H   LINE      ---      ---       #188
      0200F00AH   SYMBOL    XDATA    BYTE      j
      00000007H   SYMBOL    DATA     BYTE      ubStatus
      0200F00BH   SYMBOL    XDATA    BYTE      ubReturn
      0200F01DH   SYMBOL    XDATA    BYTE      aubFIFOReadPtr0
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      0100777BH   BLOCK     CODE     ---       LVL=0
      0100777BH   LINE      ---      ---       #191
      0100777BH   LINE      ---      ---       #192
      0100777BH   BLOCK     CODE     NEAR LAB  LVL=1
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 36


      0100777BH   LINE      ---      ---       #195
      0100777EH   LINE      ---      ---       #197
      0100778BH   LINE      ---      ---       #198
      0100778BH   LINE      ---      ---       #199
      01007792H   LINE      ---      ---       #200
      01007793H   LINE      ---      ---       #201
      01007795H   LINE      ---      ---       #202
      01007797H   LINE      ---      ---       #203
      00000082H   SYMBOL    DATA     ---       pBootCode
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007798H   BLOCK     CODE     ---       LVL=0
      01007798H   LINE      ---      ---       #206
      01007798H   LINE      ---      ---       #207
      00000006H   SYMBOL    DATA     WORD      Add
      00000005H   SYMBOL    DATA     BYTE      Leth
      01007798H   BLOCK     CODE     NEAR LAB  LVL=1
      01007798H   LINE      ---      ---       #211
      01007798H   LINE      ---      ---       #213
      0100779FH   LINE      ---      ---       #214
      0100779FH   LINE      ---      ---       #215
      010077B9H   LINE      ---      ---       #216
      010077BCH   LINE      ---      ---       #217
      010077BEH   LINE      ---      ---       #218
      00000006H   SYMBOL    DATA     ---       pBootCode
      00000004H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010075B7H   BLOCK     CODE     ---       LVL=0
      010075B7H   LINE      ---      ---       #221
      010075B7H   LINE      ---      ---       #222
      010075B7H   BLOCK     CODE     NEAR LAB  LVL=1
      010075B7H   LINE      ---      ---       #227
      010075C0H   LINE      ---      ---       #229
      010075C4H   LINE      ---      ---       #230
      010075C4H   LINE      ---      ---       #231
      010075EDH   LINE      ---      ---       #240
      010075F7H   LINE      ---      ---       #243
      01007603H   LINE      ---      ---       #245
      0200F00AH   SYMBOL    XDATA    BYTE      i
      0200F00BH   SYMBOL    XDATA    ---       pBootCode
      0200F00DH   SYMBOL    XDATA    ---       tmp
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007800H   BLOCK     CODE     ---       LVL=0
      01007800H   LINE      ---      ---       #250
      01007800H   LINE      ---      ---       #251
      01007800H   BLOCK     CODE     NEAR LAB  LVL=1
      01007800H   LINE      ---      ---       #252
      0100780FH   LINE      ---      ---       #253
      01007814H   LINE      ---      ---       #259
      01007821H   LINE      ---      ---       #261
      01007827H   LINE      ---      ---       #262
      01007827H   LINE      ---      ---       #263
      01007835H   LINE      ---      ---       #264
      01007835H   LINE      ---      ---       #265
      01007835H   LINE      ---      ---       #266
      01007843H   LINE      ---      ---       #267
      01007843H   LINE      ---      ---       #268
      01007849H   LINE      ---      ---       #269
      0100784DH   LINE      ---      ---       #270
      0100785FH   LINE      ---      ---       #272
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 37


      01007866H   LINE      ---      ---       #277
      0100786CH   LINE      ---      ---       #278
      0100786CH   LINE      ---      ---       #279
      0100786FH   LINE      ---      ---       #280
      01007878H   LINE      ---      ---       #281
      0100787DH   LINE      ---      ---       #282
      0100787EH   LINE      ---      ---       #284
      0100787EH   LINE      ---      ---       #285
      01007880H   LINE      ---      ---       #286
      01007880H   LINE      ---      ---       #287
      01007880H   LINE      ---      ---       #288
      01007882H   LINE      ---      ---       #290
      0100788DH   LINE      ---      ---       #291
      0100788DH   LINE      ---      ---       #292
      0100788FH   LINE      ---      ---       #298
      0100788FH   LINE      ---      ---       #299
      01007891H   LINE      ---      ---       #301
      0100789DH   LINE      ---      ---       #302
      0100789DH   LINE      ---      ---       #303
      0100789FH   LINE      ---      ---       #304
      010078A1H   LINE      ---      ---       #305
      010078ABH   LINE      ---      ---       #306
      010078B2H   LINE      ---      ---       #308
      010078B2H   LINE      ---      ---       #309
      010078B4H   LINE      ---      ---       #311
      010078BFH   LINE      ---      ---       #312
      010078BFH   LINE      ---      ---       #313
      010078C2H   LINE      ---      ---       #314
      010078C4H   LINE      ---      ---       #315
      010078CBH   LINE      ---      ---       #316
      010078D2H   LINE      ---      ---       #317
      010078D9H   LINE      ---      ---       #319
      010078D9H   LINE      ---      ---       #320
      010078DBH   LINE      ---      ---       #322
      010078E7H   LINE      ---      ---       #323
      010078E7H   LINE      ---      ---       #324
      010078EAH   LINE      ---      ---       #325
      010078EDH   LINE      ---      ---       #326
      010078EDH   LINE      ---      ---       #328
      010078FBH   LINE      ---      ---       #329
      010078FBH   LINE      ---      ---       #330
      01007901H   LINE      ---      ---       #340
      01007901H   LINE      ---      ---       #341
      01007906H   LINE      ---      ---       #353
      01007914H   LINE      ---      ---       #354
      01007914H   LINE      ---      ---       #355
      0100791AH   LINE      ---      ---       #365
      0100791AH   LINE      ---      ---       #366
      0100791FH   LINE      ---      ---       #368
      01007923H   LINE      ---      ---       #370
      01007926H   LINE      ---      ---       #371
      01007929H   LINE      ---      ---       #372
      0100792CH   LINE      ---      ---       #374
      0100792FH   LINE      ---      ---       #375
      01007933H   LINE      ---      ---       #376
      01007935H   LINE      ---      ---       #378
      01007940H   LINE      ---      ---       #379
      01007940H   LINE      ---      ---       #382
      01007943H   LINE      ---      ---       #383
      01007943H   LINE      ---      ---       #385
      01007943H   LINE      ---      ---       #386
      01007945H   LINE      ---      ---       #389
      01007945H   LINE      ---      ---       #390
      01007953H   LINE      ---      ---       #391
      01007961H   LINE      ---      ---       #392
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 38


      01007961H   LINE      ---      ---       #393
      01007967H   LINE      ---      ---       #395
      0100796BH   LINE      ---      ---       #396
      0100797EH   LINE      ---      ---       #397
      01007989H   LINE      ---      ---       #398
      01007994H   LINE      ---      ---       #399
      0100799FH   LINE      ---      ---       #400
      010079AAH   LINE      ---      ---       #401
      010079B5H   LINE      ---      ---       #402
      010079C0H   LINE      ---      ---       #403
      010079CBH   LINE      ---      ---       #405
      010079D0H   LINE      ---      ---       #406
      010079D0H   LINE      ---      ---       #407
      010079D4H   LINE      ---      ---       #409
      010079D6H   LINE      ---      ---       #410
      010079D8H   LINE      ---      ---       #411
      010079E3H   LINE      ---      ---       #412
      010079E5H   LINE      ---      ---       #413
      010079E8H   LINE      ---      ---       #416
      010079F8H   LINE      ---      ---       #417
      010079F8H   LINE      ---      ---       #418
      010079FFH   LINE      ---      ---       #419
      01007A09H   LINE      ---      ---       #420
      01007A0BH   LINE      ---      ---       #422
      01007A0BH   LINE      ---      ---       #423
      01007A12H   LINE      ---      ---       #424
      01007A17H   LINE      ---      ---       #425
      01007A17H   LINE      ---      ---       #426
      01007A25H   LINE      ---      ---       #427
      01007A25H   LINE      ---      ---       #428
      01007A26H   LINE      ---      ---       #430
      01007A26H   LINE      ---      ---       #431
      01007A2BH   LINE      ---      ---       #432
      01007A2BH   LINE      ---      ---       #433
      01007A2CH   LINE      ---      ---       #436
      01007A2CH   LINE      ---      ---       #439
      01007A2CH   LINE      ---      ---       #442
      01007A2CH   LINE      ---      ---       #443
      01007A2CH   LINE      ---      ---       #445
      01007A2CH   LINE      ---      ---       #446
      01007A33H   LINE      ---      ---       #447
      01007A33H   LINE      ---      ---       #448
      01007A37H   LINE      ---      ---       #449
      01007A39H   LINE      ---      ---       #450
      01007A40H   LINE      ---      ---       #451
      01007A40H   LINE      ---      ---       #452
      01007A47H   LINE      ---      ---       #453
      01007A47H   LINE      ---      ---       #455
      0200F000H   SYMBOL    XDATA    ---       ulCANData
      0200F008H   SYMBOL    XDATA    BYTE      RgRtnReadFIFO
      0200F009H   SYMBOL    XDATA    BYTE      i
      0200F01EH   SYMBOL    XDATA    BYTE      RgState
      0200F01FH   SYMBOL    XDATA    BYTE      CntCanRx
      0200F020H   SYMBOL    XDATA    WORD      CntLenth
      0200F022H   SYMBOL    XDATA    WORD      RgCanRxLenth
      0200F024H   SYMBOL    XDATA    WORD      AddProg
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      ---         MODULE    ---      ---       XC88X_FLHANDLER
      010072DEH   PUBLIC    CODE     NEAR LAB  _DFLERASE
      01007338H   PUBLIC    CODE     NEAR LAB  _FLPROG
      0100730BH   PUBLIC    CODE     NEAR LAB  _PFLERASE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000F0H   SFRSYM    DATA     BYTE      B
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 39


      000000D0H   SFRSYM    DATA     BYTE      PSW
      01007303H   SYMBOL    CODE     NEAR LAB  _DERASE_FAIL
      01007330H   SYMBOL    CODE     NEAR LAB  _PERASE_FAIL
      01007350H   SYMBOL    CODE     NEAR LAB  _PROG_FAIL
      0100DFF9H   SYMBOL    CODE     BYTE      FLASH_ERASE
      0100DFF3H   SYMBOL    CODE     BYTE      FLASH_ERASE_ABORT
      0100DFF6H   SYMBOL    CODE     BYTE      FLASH_PROG
      0100DB06H   SYMBOL    CODE     BYTE      FLASH_PROTECT
      0100DFF0H   SYMBOL    CODE     BYTE      FLASH_READ_STATUS

      010072DEH   BLOCK     CODE     NEAR LAB  LVL=0
      010072DEH   LINE      CODE     ---       #61
      010072E0H   LINE      CODE     ---       #62
      010072E2H   LINE      CODE     ---       #64
      010072E4H   LINE      CODE     ---       #65
      010072E6H   LINE      CODE     ---       #67
      010072E7H   LINE      CODE     ---       #68
      010072E9H   LINE      CODE     ---       #69
      010072EBH   LINE      CODE     ---       #70
      010072EDH   LINE      CODE     ---       #72
      010072EFH   LINE      CODE     ---       #73
      010072F1H   LINE      CODE     ---       #74
      010072F3H   LINE      CODE     ---       #84
      010072F6H   LINE      CODE     ---       #87
      010072F9H   LINE      CODE     ---       #88
      010072FBH   LINE      CODE     ---       #89
      010072FDH   LINE      CODE     ---       #90
      010072FFH   LINE      CODE     ---       #91
      01007301H   LINE      CODE     ---       #92
      01007302H   LINE      CODE     ---       #93
      01007303H   LINE      CODE     ---       #95
      01007305H   LINE      CODE     ---       #96
      01007307H   LINE      CODE     ---       #97
      01007309H   LINE      CODE     ---       #98
      0100730AH   LINE      CODE     ---       #99
      ---         BLOCKEND  ---      ---       LVL=0

      0100730BH   BLOCK     CODE     NEAR LAB  LVL=0
      0100730BH   LINE      CODE     ---       #134
      0100730DH   LINE      CODE     ---       #135
      0100730FH   LINE      CODE     ---       #136
      01007311H   LINE      CODE     ---       #138
      01007312H   LINE      CODE     ---       #139
      01007314H   LINE      CODE     ---       #140
      01007316H   LINE      CODE     ---       #141
      01007318H   LINE      CODE     ---       #142
      0100731AH   LINE      CODE     ---       #143
      0100731CH   LINE      CODE     ---       #144
      0100731EH   LINE      CODE     ---       #145
      01007320H   LINE      CODE     ---       #155
      01007323H   LINE      CODE     ---       #159
      01007326H   LINE      CODE     ---       #160
      01007328H   LINE      CODE     ---       #161
      0100732AH   LINE      CODE     ---       #162
      0100732CH   LINE      CODE     ---       #163
      0100732EH   LINE      CODE     ---       #164
      0100732FH   LINE      CODE     ---       #165
      01007330H   LINE      CODE     ---       #167
      01007332H   LINE      CODE     ---       #168
      01007334H   LINE      CODE     ---       #169
      01007336H   LINE      CODE     ---       #170
      01007337H   LINE      CODE     ---       #171
      ---         BLOCKEND  ---      ---       LVL=0

      01007338H   BLOCK     CODE     NEAR LAB  LVL=0
LX51 LINKER/LOCATER V4.58                                                             07/07/2013  18:32:47  PAGE 40


      01007338H   LINE      CODE     ---       #192
      0100733AH   LINE      CODE     ---       #200
      0100733CH   LINE      CODE     ---       #201
      0100733EH   LINE      CODE     ---       #202
      01007340H   LINE      CODE     ---       #212
      01007343H   LINE      CODE     ---       #215
      01007346H   LINE      CODE     ---       #216
      01007348H   LINE      CODE     ---       #217
      0100734AH   LINE      CODE     ---       #218
      0100734CH   LINE      CODE     ---       #219
      0100734EH   LINE      CODE     ---       #220
      0100734FH   LINE      CODE     ---       #221
      01007350H   LINE      CODE     ---       #223
      01007352H   LINE      CODE     ---       #224
      01007354H   LINE      CODE     ---       #225
      01007356H   LINE      CODE     ---       #226
      01007357H   LINE      CODE     ---       #227
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C_INIT
      01007096H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?CSTPTR
      010070EDH   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      010070FFH   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?ILDIX
      01007121H   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULCMP
      01007137H   PUBLIC    CODE     ---       ?C?ULCMP

      ---         MODULE    ---      ---       ?C?ULSHR
      01007148H   PUBLIC    CODE     ---       ?C?ULSHR

      ---         MODULE    ---      ---       ?C?LLDPTR
      0100715BH   PUBLIC    CODE     ---       ?C?LLDPTR

      ---         MODULE    ---      ---       ?C?LSTXDATA
      0100717BH   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      01007187H   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?COPY517
      01007284H   PUBLIC    CODE     ---       ?C?COPY517

      ---         MODULE    ---      ---       ?C?LLDIDATA
      010072AAH   PUBLIC    CODE     ---       ?C?LLDIDATA

      ---         MODULE    ---      ---       ?C?LLDXDATA
      010072B6H   PUBLIC    CODE     ---       ?C?LLDXDATA

      ---         MODULE    ---      ---       ?C?LLDPDATA
      010072C2H   PUBLIC    CODE     ---       ?C?LLDPDATA

      ---         MODULE    ---      ---       ?C?LLDCODE
      010072CEH   PUBLIC    CODE     ---       ?C?LLDCODE

Program Size: data=89.0 xdata=38 const=16 code=3075
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)

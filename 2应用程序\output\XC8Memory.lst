C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 1   


C51 COMPILER V9.50a, COMPILATION OF MODULE XC8MEMORY
OBJECT MODULE PLACED IN .\output\XC8Memory.obj
COMPILER INVOKED BY: C:\Keil\C51\BIN\C51.EXE XC8Memory.c LARGE OMF2 BROWSE MOD517(NOAU) DEBUG CODE LISTINCLUDE SYMBOLS P
                    -RINT(.\output\XC8Memory.lst) TABS(2) PREPRINT(.\output\XC8Memory.i) OBJECT(.\output\XC8Memory.obj)

line level    source

   1          //****************************************************************************
   2          // File Name     XC8Memory.c
   3          //----------------------------------------------------------------------------
   4          // Derivatives   Infineon XC88x AA and AB
   5          //
   6          // Description   Sets of function to handle the memory in the XC88x AA.
   7          //               In particular, the Flash handling is taken care of here.
   8          //
   9          //----------------------------------------------------------------------------
  10          // Date          14.03.2006 10:48:38
  11          // Copyright     (c) 2006 Infineon Technologies
  12          //****************************************************************************
  13          
  14          #include "XC8Memory.h"
   1      =1  //****************************************************************************
   2      =1  // File Name     XC8Memory.h
   3      =1  //----------------------------------------------------------------------------
   4      =1  // Derivatives   Infineon XC88x AA and AB
   5      =1  //
   6      =1  // Description   Sets of function to handle the memory in the XC88x AA.
   7      =1  //               In particular, the Flash handling is taken care of here.
   8      =1  //
   9      =1  //----------------------------------------------------------------------------
  10      =1  // Date          14.03.2006 10:48:38
  11      =1  // Copyright     (c) 2006 Infineon Technologies
  12      =1  //****************************************************************************
  13      =1  
  14      =1  // Header for XC8Memory.c
  15      =1  // Memory handling of XC88x AA derivatives
  16      =1  
  17      =1  #ifndef _XC8MEMORY_H_
  18      =1  #define _XC8MEMORY_H_
  19      =1  
  20      =1  #include "XC88x_FLADDR.H"
   1      =2  //***************************************************************************
   2      =2  //*     BootROM entries for XC88x-AA and AB Step                            *
   3      =2  //*     Updated  : 2006-03-14                                               *
   4      =2  //*     Copyright: (c) 2006 Infineon Technologies AG                        *
   5      =2  //***************************************************************************
   6      =2  
   7      =2  #ifndef  __XC88x_FLADDR_H__
   8      =2  #define  __XC88x_FLADDR_H__
   9      =2  
  10      =2  // User Configuration //
  11      =2  #define XC88xAA           1    // Must be set to '1/0' (for AA step)
  12      =2  #define XC88xAB           0    // Must be set to '1/0' (set to '1' for AB step)
  13      =2  #define XC88xAC           0    // Must be set to '1/0' (Set to '1' for AC step)
  14      =2  
  15      =2  #define USE_BANK          3    // Register Bank used during Program / Erase
  16      =2                                 // Default = 3
  17      =2  #define WORDLINE_BUFFER_ADDRESS  0x80  // Starting address of the Buffer in the IRAM
  18      =2                                         // This Buffer will be used to store data
  19      =2                                         // before it is being programmed to flash
  20      =2  #define USE_64BYTE_BUFFER 0    // If PFlash will be programmed, set this to '1'
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 2   

  21      =2                                 // Programming PFLash will need 64Byte WordLine Buffer
  22      =2                                 // Else, default buffer size is set to 32byte.
  23      =2  #define USE_PFLERASE      1    // Set to '1' if PFlErase() routine is to be used.
  24      =2                                 // Set to '0' if not used, to save some code space.
  25      =2  #define USE_DLFERASE      1    // Set to '1' if DFlErase() routine is to be used.
  26      =2                                 // Set to '0' if not used, to save some code space.
  27      =2  
  28      =2  // End of user configuration
  29      =2  
  30      =2  
  31      =2  //+----------------------------------------------+
  32      =2  //|   ROM entries taken from manual              |
  33      =2  //|   and AppNote aboute programming the FLASH   |
  34      =2  //|   used in XC88x_FLHANDLER.ASM                |
  35      =2  //+----------------------------------------------+
  36      =2  
  37      =2  //====================================================================================
  38      =2  #if (XC88xAA || XC88xAB || XC88xAC)             // Use the FSM to program / erase flash
  39      =2  #define FLASH_ERASE_BROM_ENTRY          0xDFF9  // Erase Flash Bank
  40      =2  #define FLASH_PROGRAM_BROM_ENTRY        0xDFF6  // Program Flash Bank.
  41      =2  #define FLASH_ERASE_ABORT_BROM_ENTRY    0xDFF3  // Abort the on going erase process
  42      =2  #define FLASH_READ_STATUS_BROM_ENTRY    0xDFF0  // Read Flash Status
  43      =2  #endif
  44      =2  #if (XC88xAA)
  45      =2  #define FLASH_PROTECT_BROM_ENTRY        0xDB06
  46      =2  #endif
  47      =2  #if (XC88xAB || XC88xAC)
           =2 #define FLASH_PROTECT_BROM_ENTRY        0xDFD8
           =2 #endif
  50      =2  //====================================================================================
  51      =2  
  52      =2  //+-----------------------------------------+
  53      =2  //|   General definitions of used sources   |
  54      =2  //+-----------------------------------------+
  55      =2  // used buffer address in IRAM for Wordline erase/program 
  56      =2  #if (XC88xAA || XC88xAB || XC88xAC)  
  57      =2  #define MAGIC_MEMORY                  0x36  // reserve magic memory from D:0x36 .. D:0x3E
  58      =2  #if (USE_64BYTE_BUFFER == 1)
           =2 #define BYTES_PER_WORDLINE            64    //
           =2 #else
  61      =2  #define BYTES_PER_WORDLINE            32    //
  62      =2  #endif
  63      =2  #define RESERVED_BYTES                0x08
  64      =2  #define PFLASH0                       0     // PFlash 0
  65      =2  #define PFLASH1                       1     // PFlash 1
  66      =2  #define PFLASH2                       2     // PFlash 2
  67      =2  #define DFLASH0                       3     // DFlash 0
  68      =2  #define DFLASH1                       4     // DFlash 1
  69      =2  
  70      =2  #endif
  71      =2  //+------------------------------------------------+
  72      =2  //|   End of general definitions of used sources   |
  73      =2  //+------------------------------------------------+
  74      =2  
  75      =2  
  76      =2  
  77      =2  //************************************************************************************************
  78      =2  //*   Definitions of Bank-, Sector- and Wordlineaddresses
  79      =2  //* 
  80      =2  //************************************************************************************************
  81      =2  // Sectors of PFlash0 for erasing -- transferred in  PFlErase() first parameter
  82      =2  
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 3   

  83      =2  // Sector.07/Sector.06/Sector.05/Sector.04/Sector.03/Sector.02/ Sector.01/ Sector.00 /
  84      =2  // ---------/---------/---------/---------/---------/Pfl0Sec02/ Pfl0Sec01/ Pfl0Sec00 /
  85      =2  // ---------/---------/---------/---------/---------/-256B-4WL/-256B--4WL/-7.5k-120WL/
  86      =2  // ---------/---------/---------/---------/---------/-0x1F00--/--0x1E00--/--0x0000----/
  87      =2  #define PFL0_SEC0   0x0001
  88      =2  #define PFL0_SEC1   0x0002
  89      =2  #define PFL0_SEC2   0x0004
  90      =2  
  91      =2  // Sectors of PFlash1 for erasing -- transferred in  PFlErase() Second parameter
  92      =2  
  93      =2  // Sector.07/Sector.06/Sector.05/Sector.04/Sector.03/Sector.02/ Sector.01/ Sector.00  /
  94      =2  // ---------/---------/---------/---------/---------/Pfl1Sec02/ Pfl1Sec01/ Pfl1Sec00  /
  95      =2  // ---------/---------/---------/---------/---------/-256B-4WL/-256B--4WL/-7.5k-120WL/
  96      =2  // ---------/---------/---------/---------/---------/-0x3F00--/--0x3E00--/--0x2000----/
  97      =2  #define PFL1_SEC0   0x0001
  98      =2  #define PFL1_SEC1   0x0002
  99      =2  #define PFL1_SEC2   0x0004
 100      =2  
 101      =2  // Sectors of PFlash2 for erasing -- transferred in  PFlErase() Third parameter
 102      =2  
 103      =2  // Sector.07/Sector.06/Sector.05/Sector.04/Sector.03/Sector.02/ Sector.01/ Sector.00  /
 104      =2  // ---------/---------/---------/---------/---------/Pfl2Sec02/ Pfl2Sec01/ Pfl2Sec00  /
 105      =2  // ---------/---------/---------/---------/---------/-256B-4WL/-256B--4WL/-7.5k-120WL/
 106      =2  // ---------/---------/---------/---------/---------/-0x5F00--/--0x5E00--/--0x4000----/
 107      =2  #define PFL2_SEC0   0x0001
 108      =2  #define PFL2_SEC1   0x0002
 109      =2  #define PFL2_SEC2   0x0004
 110      =2  
 111      =2  // Sectors of DFlash 0 for erasing -- transfered in DFlErase() First parameter
 112      =2  // DFlash 0 address is also mirrorred to 0x7xxx
 113      =2  
 114      =2  // Sector.15/Sector.14/Sector.13/Sector.12/Sector.11/Sector.10/Sector.09/Sector.08/
 115      =2  // ---------/---------/---------/---------/---------/---------/Dfl0Sec09/Dfl0Sec08/
 116      =2  // ---------/---------/---------/---------/---------/---------/-128-4WL-/-128-4WL-/
 117      =2  // ---------/---------/---------/---------/---------/---------/--0xAF80-/--0xAF00-/
 118      =2   
 119      =2  // Sector.07/Sector.06/Sector.05/Sector.04/Sector.03/Sector.02/Sector.01/Sector.00/
 120      =2  // Dfl0Sec07/Dfl0Sec06/Dfl0Sec05/Dfl0Sec04/Dfl0Sec03/Dfl0Sec02/Dfl0Sec01/Dfl0Sec00/
 121      =2  // -128-4WL-/-128-4WL-/-256-8WL-/-256-8WL-/-512-16WL/-512-16WL/-1k--32WL/-1k--32WL/
 122      =2  // --0xAE80-/--0xAE00-/--0xAD00-/--0xAC00-/-0xAA00--/-0xA800--/--0xA400-/--0xA000-/
 123      =2  
 124      =2  #define DFL0_SEC0   0x0001
 125      =2  #define DFL0_SEC1   0x0002
 126      =2  #define DFL0_SEC2   0x0004
 127      =2  #define DFL0_SEC3   0x0008
 128      =2  #define DFL0_SEC4   0x0010
 129      =2  #define DFL0_SEC5   0x0020
 130      =2  #define DFL0_SEC6   0x0040
 131      =2  #define DFL0_SEC7   0x0080
 132      =2  #define DFL0_SEC8   0x0100
 133      =2  #define DFL0_SEC9   0x0200
 134      =2  #define DFL0_SECALL 0x3FF
 135      =2  
 136      =2  // Sectors of DFlash 1 for erasing -- transfered in DFlErase() Second parameter
 137      =2  // DFlash 1 address is also mirrorred to 0x6xxx
 138      =2  
 139      =2  // Sector.15/Sector.14/Sector.13/Sector.12/Sector.11/Sector.10/Sector.09/Sector.08/
 140      =2  // ---------/---------/---------/---------/---------/---------/Dfl1Sec09/Dfl1Sec08/
 141      =2  // ---------/---------/---------/---------/---------/---------/-128-4WL-/-128-4WL-/
 142      =2  // ---------/---------/---------/---------/---------/---------/--0xAF80-/--0xAF00-/
 143      =2   
 144      =2  // Sector.07/Sector.06/Sector.05/Sector.04/Sector.03/Sector.02/Sector.01/Sector.00/
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 4   

 145      =2  // Dfl1Sec07/Dfl1Sec06/Dfl1Sec05/Dfl1Sec04/Dfl1Sec03/Dfl1Sec02/Dfl1Sec01/Dfl1Sec00/
 146      =2  // -128-4WL-/-128-4WL-/-256-8WL-/-256-8WL-/-512-16WL/-512-16WL/-1k--32WL/-1k--32WL/
 147      =2  // --0xAE80-/--0xAE00-/--0xAD00-/--0xAC00-/-0xAA00--/-0xA800--/--0xA400-/--0xA000-/
 148      =2  
 149      =2  #define DFL1_SEC0   0x0001
 150      =2  #define DFL1_SEC1   0x0002
 151      =2  #define DFL1_SEC2   0x0004
 152      =2  #define DFL1_SEC3   0x0008
 153      =2  #define DFL1_SEC4   0x0010
 154      =2  #define DFL1_SEC5   0x0020
 155      =2  #define DFL1_SEC6   0x0040
 156      =2  #define DFL1_SEC7   0x0080
 157      =2  #define DFL1_SEC8   0x0100
 158      =2  #define DFL1_SEC9   0x0200
 159      =2  #define DFL1_SECALL 0x3FF
 160      =2  
 161      =2  //###################################################################################
 162      =2  // WordLine startaddresses for Sectors in PFlas 0, each WL with 64bytes
 163      =2  
 164      =2  // Sector 0 -- 7.5 kbytes -- 120 Wordlines
 165      =2  #define PFL0_SEC0_WL00  0x0000
 166      =2  #define PFL0_SEC0_WL01  0x0040
 167      =2  #define PFL0_SEC0_WL02  0x0080
 168      =2  #define PFL0_SEC0_WL03  0x00C0
 169      =2  #define PFL0_SEC0_WL04  0x0100
 170      =2  #define PFL0_SEC0_WL05  0x0140
 171      =2  #define PFL0_SEC0_WL06  0x0180
 172      =2  #define PFL0_SEC0_WL07  0x01C0
 173      =2  #define PFL0_SEC0_WL08  0x0200
 174      =2  #define PFL0_SEC0_WL09  0x0240
 175      =2  #define PFL0_SEC0_WL10  0x0280
 176      =2  #define PFL0_SEC0_WL11  0x02C0
 177      =2  #define PFL0_SEC0_WL12  0x0300
 178      =2  #define PFL0_SEC0_WL13  0x0340
 179      =2  #define PFL0_SEC0_WL14  0x0380
 180      =2  #define PFL0_SEC0_WL15  0x03C0
 181      =2  #define PFL0_SEC0_WL16  0x0400
 182      =2  #define PFL0_SEC0_WL17  0x0440
 183      =2  #define PFL0_SEC0_WL18  0x0480
 184      =2  #define PFL0_SEC0_WL19  0x04C0
 185      =2  #define PFL0_SEC0_WL20  0x0500
 186      =2  #define PFL0_SEC0_WL21  0x0540
 187      =2  #define PFL0_SEC0_WL22  0x0580
 188      =2  #define PFL0_SEC0_WL23  0x05C0
 189      =2  #define PFL0_SEC0_WL24  0x0600
 190      =2  #define PFL0_SEC0_WL25  0x0640
 191      =2  #define PFL0_SEC0_WL26  0x0680
 192      =2  #define PFL0_SEC0_WL27  0x06C0
 193      =2  #define PFL0_SEC0_WL28  0x0700
 194      =2  #define PFL0_SEC0_WL29  0x0740
 195      =2  #define PFL0_SEC0_WL30  0x0780
 196      =2  #define PFL0_SEC0_WL31  0x07C0
 197      =2  #define PFL0_SEC0_WL32  0x0800
 198      =2  #define PFL0_SEC0_WL33  0x0840
 199      =2  #define PFL0_SEC0_WL34  0x0880
 200      =2  #define PFL0_SEC0_WL35  0x08C0
 201      =2  #define PFL0_SEC0_WL36  0x0900
 202      =2  #define PFL0_SEC0_WL37  0x0940
 203      =2  #define PFL0_SEC0_WL38  0x0980
 204      =2  #define PFL0_SEC0_WL39  0x09C0
 205      =2  #define PFL0_SEC0_WL40  0x0A00
 206      =2  #define PFL0_SEC0_WL41  0x0A40
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 5   

 207      =2  #define PFL0_SEC0_WL42  0x0A80
 208      =2  #define PFL0_SEC0_WL43  0x0AC0
 209      =2  #define PFL0_SEC0_WL44  0x0B00
 210      =2  #define PFL0_SEC0_WL45  0x0B40
 211      =2  #define PFL0_SEC0_WL46  0x0B80
 212      =2  #define PFL0_SEC0_WL47  0x0BC0
 213      =2  #define PFL0_SEC0_WL48  0x0C00
 214      =2  #define PFL0_SEC0_WL49  0x0C40
 215      =2  #define PFL0_SEC0_WL50  0x0C80
 216      =2  #define PFL0_SEC0_WL51  0x0CC0
 217      =2  #define PFL0_SEC0_WL52  0x0D00
 218      =2  #define PFL0_SEC0_WL53  0x0D40
 219      =2  #define PFL0_SEC0_WL54  0x0D80
 220      =2  #define PFL0_SEC0_WL55  0x0DC0
 221      =2  #define PFL0_SEC0_WL56  0x0E00
 222      =2  #define PFL0_SEC0_WL57  0x0E40
 223      =2  #define PFL0_SEC0_WL58  0x0E80
 224      =2  #define PFL0_SEC0_WL59  0x0EC0
 225      =2  #define PFL0_SEC0_WL60  0x0F00
 226      =2  #define PFL0_SEC0_WL61  0x0F40
 227      =2  #define PFL0_SEC0_WL62  0x0F80
 228      =2  #define PFL0_SEC0_WL63  0x0FC0
 229      =2  #define PFL0_SEC0_WL64  0x1000
 230      =2  #define PFL0_SEC0_WL65  0x1040
 231      =2  #define PFL0_SEC0_WL66  0x1080
 232      =2  #define PFL0_SEC0_WL67  0x10C0
 233      =2  #define PFL0_SEC0_WL68  0x1100
 234      =2  #define PFL0_SEC0_WL69  0x1140
 235      =2  #define PFL0_SEC0_WL70  0x1180
 236      =2  #define PFL0_SEC0_WL71  0x11C0
 237      =2  #define PFL0_SEC0_WL72  0x1200
 238      =2  #define PFL0_SEC0_WL73  0x1240
 239      =2  #define PFL0_SEC0_WL74  0x1280
 240      =2  #define PFL0_SEC0_WL75  0x12C0
 241      =2  #define PFL0_SEC0_WL76  0x1300
 242      =2  #define PFL0_SEC0_WL77  0x1340
 243      =2  #define PFL0_SEC0_WL78  0x1380
 244      =2  #define PFL0_SEC0_WL79  0x13C0
 245      =2  #define PFL0_SEC0_WL80  0x1400
 246      =2  #define PFL0_SEC0_WL81  0x1440
 247      =2  #define PFL0_SEC0_WL82  0x1480
 248      =2  #define PFL0_SEC0_WL83  0x14C0
 249      =2  #define PFL0_SEC0_WL84  0x1500
 250      =2  #define PFL0_SEC0_WL85  0x1540
 251      =2  #define PFL0_SEC0_WL86  0x1580
 252      =2  #define PFL0_SEC0_WL87  0x15C0
 253      =2  #define PFL0_SEC0_WL88  0x1600
 254      =2  #define PFL0_SEC0_WL89  0x1640
 255      =2  #define PFL0_SEC0_WL90  0x1680
 256      =2  #define PFL0_SEC0_WL91  0x16C0
 257      =2  #define PFL0_SEC0_WL92  0x1700
 258      =2  #define PFL0_SEC0_WL93  0x1740
 259      =2  #define PFL0_SEC0_WL94  0x1780
 260      =2  #define PFL0_SEC0_WL95  0x17C0
 261      =2  #define PFL0_SEC0_WL96  0x1800
 262      =2  #define PFL0_SEC0_WL97  0x1840
 263      =2  #define PFL0_SEC0_WL98  0x1880
 264      =2  #define PFL0_SEC0_WL99  0x18C0
 265      =2  #define PFL0_SEC0_WL100 0x1900
 266      =2  #define PFL0_SEC0_WL101 0x1940
 267      =2  #define PFL0_SEC0_WL102 0x1980
 268      =2  #define PFL0_SEC0_WL103 0x19C0
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 6   

 269      =2  #define PFL0_SEC0_WL104 0x1A00
 270      =2  #define PFL0_SEC0_WL105 0x1A40
 271      =2  #define PFL0_SEC0_WL106 0x1A80
 272      =2  #define PFL0_SEC0_WL107 0x1AC0
 273      =2  #define PFL0_SEC0_WL108 0x1B00
 274      =2  #define PFL0_SEC0_WL109 0x1B40
 275      =2  #define PFL0_SEC0_WL110 0x1B80
 276      =2  #define PFL0_SEC0_WL111 0x1BC0
 277      =2  #define PFL0_SEC0_WL112 0x1C00
 278      =2  #define PFL0_SEC0_WL113 0x1C40
 279      =2  #define PFL0_SEC0_WL114 0x1C80
 280      =2  #define PFL0_SEC0_WL115 0x1CC0
 281      =2  #define PFL0_SEC0_WL116 0x1D00
 282      =2  #define PFL0_SEC0_WL117 0x1D40
 283      =2  #define PFL0_SEC0_WL118 0x1D80
 284      =2  #define PFL0_SEC0_WL119 0x1DC0
 285      =2  
 286      =2  // Sector 1 -- 256 bytes -- 4 Wordlines
 287      =2  #define PFL0_SEC1_WL120 0x1E00
 288      =2  #define PFL0_SEC1_WL121 0x1E40
 289      =2  #define PFL0_SEC1_WL122 0x1E80
 290      =2  #define PFL0_SEC1_WL123 0x1EC0
 291      =2  
 292      =2  // Sector 2 -- 256 bytes -- 4 Wordlines
 293      =2  #define PFL0_SEC2_WL124 0x1F00
 294      =2  #define PFL0_SEC2_WL125 0x1F40
 295      =2  #define PFL0_SEC2_WL126 0x1F80
 296      =2  #define PFL0_SEC2_WL127 0x1FC0
 297      =2  
 298      =2  //###################################################################################
 299      =2  // WordLine startaddresses for Sectors in PFlash 1, each _WL with 64bytes
 300      =2  
 301      =2  // Sector 0 -- 7.5 kbytes -- 120 Wordlines
 302      =2  #define PFL1_SEC0_WL00  0x2000
 303      =2  #define PFL1_SEC0_WL01  0x2040
 304      =2  #define PFL1_SEC0_WL02  0x2080
 305      =2  #define PFL1_SEC0_WL03  0x20C0
 306      =2  #define PFL1_SEC0_WL04  0x2100
 307      =2  #define PFL1_SEC0_WL05  0x2140
 308      =2  #define PFL1_SEC0_WL06  0x2180
 309      =2  #define PFL1_SEC0_WL07  0x21C0
 310      =2  #define PFL1_SEC0_WL08  0x2200
 311      =2  #define PFL1_SEC0_WL09  0x2240
 312      =2  #define PFL1_SEC0_WL10  0x2280
 313      =2  #define PFL1_SEC0_WL11  0x22C0
 314      =2  #define PFL1_SEC0_WL12  0x2300
 315      =2  #define PFL1_SEC0_WL13  0x2340
 316      =2  #define PFL1_SEC0_WL14  0x2380
 317      =2  #define PFL1_SEC0_WL15  0x23C0
 318      =2  #define PFL1_SEC0_WL16  0x2400
 319      =2  #define PFL1_SEC0_WL17  0x2440
 320      =2  #define PFL1_SEC0_WL18  0x2480
 321      =2  #define PFL1_SEC0_WL19  0x24C0
 322      =2  #define PFL1_SEC0_WL20  0x2500
 323      =2  #define PFL1_SEC0_WL21  0x2540
 324      =2  #define PFL1_SEC0_WL22  0x2580
 325      =2  #define PFL1_SEC0_WL23  0x25C0
 326      =2  #define PFL1_SEC0_WL24  0x2600
 327      =2  #define PFL1_SEC0_WL25  0x2640
 328      =2  #define PFL1_SEC0_WL26  0x2680
 329      =2  #define PFL1_SEC0_WL27  0x26C0
 330      =2  #define PFL1_SEC0_WL28  0x2700
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 7   

 331      =2  #define PFL1_SEC0_WL29  0x2740
 332      =2  #define PFL1_SEC0_WL30  0x2780
 333      =2  #define PFL1_SEC0_WL31  0x27C0
 334      =2  #define PFL1_SEC0_WL32  0x2800
 335      =2  #define PFL1_SEC0_WL33  0x2840
 336      =2  #define PFL1_SEC0_WL34  0x2880
 337      =2  #define PFL1_SEC0_WL35  0x28C0
 338      =2  #define PFL1_SEC0_WL36  0x2900
 339      =2  #define PFL1_SEC0_WL37  0x2940
 340      =2  #define PFL1_SEC0_WL38  0x2980
 341      =2  #define PFL1_SEC0_WL39  0x29C0
 342      =2  #define PFL1_SEC0_WL40  0x2A00
 343      =2  #define PFL1_SEC0_WL41  0x2A40
 344      =2  #define PFL1_SEC0_WL42  0x2A80
 345      =2  #define PFL1_SEC0_WL43  0x2AC0
 346      =2  #define PFL1_SEC0_WL44  0x2B00
 347      =2  #define PFL1_SEC0_WL45  0x2B40
 348      =2  #define PFL1_SEC0_WL46  0x2B80
 349      =2  #define PFL1_SEC0_WL47  0x2BC0
 350      =2  #define PFL1_SEC0_WL48  0x2C00
 351      =2  #define PFL1_SEC0_WL49  0x2C40
 352      =2  #define PFL1_SEC0_WL50  0x2C80
 353      =2  #define PFL1_SEC0_WL51  0x2CC0
 354      =2  #define PFL1_SEC0_WL52  0x2D00
 355      =2  #define PFL1_SEC0_WL53  0x2D40
 356      =2  #define PFL1_SEC0_WL54  0x2D80
 357      =2  #define PFL1_SEC0_WL55  0x2DC0
 358      =2  #define PFL1_SEC0_WL56  0x2E00
 359      =2  #define PFL1_SEC0_WL57  0x2E40
 360      =2  #define PFL1_SEC0_WL58  0x2E80
 361      =2  #define PFL1_SEC0_WL59  0x2EC0
 362      =2  #define PFL1_SEC0_WL60  0x2F00
 363      =2  #define PFL1_SEC0_WL61  0x2F40
 364      =2  #define PFL1_SEC0_WL62  0x2F80
 365      =2  #define PFL1_SEC0_WL63  0x2FC0
 366      =2  #define PFL1_SEC0_WL64  0x3000
 367      =2  #define PFL1_SEC0_WL65  0x3040
 368      =2  #define PFL1_SEC0_WL66  0x3080
 369      =2  #define PFL1_SEC0_WL67  0x30C0
 370      =2  #define PFL1_SEC0_WL68  0x3100
 371      =2  #define PFL1_SEC0_WL69  0x3140
 372      =2  #define PFL1_SEC0_WL70  0x3180
 373      =2  #define PFL1_SEC0_WL71  0x31C0
 374      =2  #define PFL1_SEC0_WL72  0x3200
 375      =2  #define PFL1_SEC0_WL73  0x3240
 376      =2  #define PFL1_SEC0_WL74  0x3280
 377      =2  #define PFL1_SEC0_WL75  0x32C0
 378      =2  #define PFL1_SEC0_WL76  0x3300
 379      =2  #define PFL1_SEC0_WL77  0x3340
 380      =2  #define PFL1_SEC0_WL78  0x3380
 381      =2  #define PFL1_SEC0_WL79  0x33C0
 382      =2  #define PFL1_SEC0_WL80  0x3400
 383      =2  #define PFL1_SEC0_WL81  0x3440
 384      =2  #define PFL1_SEC0_WL82  0x3480
 385      =2  #define PFL1_SEC0_WL83  0x34C0
 386      =2  #define PFL1_SEC0_WL84  0x3500
 387      =2  #define PFL1_SEC0_WL85  0x3540
 388      =2  #define PFL1_SEC0_WL86  0x3580
 389      =2  #define PFL1_SEC0_WL87  0x35C0
 390      =2  #define PFL1_SEC0_WL88  0x3600
 391      =2  #define PFL1_SEC0_WL89  0x3640
 392      =2  #define PFL1_SEC0_WL90  0x3680
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 8   

 393      =2  #define PFL1_SEC0_WL91  0x36C0
 394      =2  #define PFL1_SEC0_WL92  0x3700
 395      =2  #define PFL1_SEC0_WL93  0x3740
 396      =2  #define PFL1_SEC0_WL94  0x3780
 397      =2  #define PFL1_SEC0_WL95  0x37C0
 398      =2  #define PFL1_SEC0_WL96  0x3800
 399      =2  #define PFL1_SEC0_WL97  0x3840
 400      =2  #define PFL1_SEC0_WL98  0x3880
 401      =2  #define PFL1_SEC0_WL99  0x38C0
 402      =2  #define PFL1_SEC0_WL100 0x3900
 403      =2  #define PFL1_SEC0_WL101 0x3940
 404      =2  #define PFL1_SEC0_WL102 0x3980
 405      =2  #define PFL1_SEC0_WL103 0x39C0
 406      =2  #define PFL1_SEC0_WL104 0x3A00
 407      =2  #define PFL1_SEC0_WL105 0x3A40
 408      =2  #define PFL1_SEC0_WL106 0x3A80
 409      =2  #define PFL1_SEC0_WL107 0x3AC0
 410      =2  #define PFL1_SEC0_WL108 0x3B00
 411      =2  #define PFL1_SEC0_WL109 0x3B40
 412      =2  #define PFL1_SEC0_WL110 0x3B80
 413      =2  #define PFL1_SEC0_WL111 0x3BC0
 414      =2  #define PFL1_SEC0_WL112 0x3C00
 415      =2  #define PFL1_SEC0_WL113 0x3C40
 416      =2  #define PFL1_SEC0_WL114 0x3C80
 417      =2  #define PFL1_SEC0_WL115 0x3CC0
 418      =2  #define PFL1_SEC0_WL116 0x3D00
 419      =2  #define PFL1_SEC0_WL117 0x3D40
 420      =2  #define PFL1_SEC0_WL118 0x3D80
 421      =2  #define PFL1_SEC0_WL119 0x3DC0
 422      =2  
 423      =2  // Sector 1 -- 256 bytes -- 4 Wordlines
 424      =2  #define PFL1_SEC1_WL120 0x3E00
 425      =2  #define PFL1_SEC1_WL121 0x3E40
 426      =2  #define PFL1_SEC1_WL122 0x3E80
 427      =2  #define PFL1_SEC1_WL123 0x3EC0
 428      =2  
 429      =2  // Sector 2 -- 256 bytes -- 4 Wordlines
 430      =2  #define PFL1_SEC2_WL124 0x3F00
 431      =2  #define PFL1_SEC2_WL125 0x3F40
 432      =2  #define PFL1_SEC2_WL126 0x3F80
 433      =2  #define PFL1_SEC2_WL127 0x3FC0
 434      =2  
 435      =2  //###################################################################################
 436      =2  // WordLine startaddresses for Sectors in PFlash 2, each WL with 64bytes
 437      =2  
 438      =2  // Sector 0 -- 7.5 kbytes -- 120 Wordlines
 439      =2  #define PFL2_SEC0_WL00  0x4000
 440      =2  #define PFL2_SEC0_WL01  0x4040
 441      =2  #define PFL2_SEC0_WL02  0x4080
 442      =2  #define PFL2_SEC0_WL03  0x40C0
 443      =2  #define PFL2_SEC0_WL04  0x4100
 444      =2  #define PFL2_SEC0_WL05  0x4140
 445      =2  #define PFL2_SEC0_WL06  0x4180
 446      =2  #define PFL2_SEC0_WL07  0x41C0
 447      =2  #define PFL2_SEC0_WL08  0x4200
 448      =2  #define PFL2_SEC0_WL09  0x4240
 449      =2  #define PFL2_SEC0_WL10  0x4280
 450      =2  #define PFL2_SEC0_WL11  0x42C0
 451      =2  #define PFL2_SEC0_WL12  0x4300
 452      =2  #define PFL2_SEC0_WL13  0x4340
 453      =2  #define PFL2_SEC0_WL14  0x4380
 454      =2  #define PFL2_SEC0_WL15  0x43C0
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 9   

 455      =2  #define PFL2_SEC0_WL16  0x4400
 456      =2  #define PFL2_SEC0_WL17  0x4440
 457      =2  #define PFL2_SEC0_WL18  0x4480
 458      =2  #define PFL2_SEC0_WL19  0x44C0
 459      =2  #define PFL2_SEC0_WL20  0x4500
 460      =2  #define PFL2_SEC0_WL21  0x4540
 461      =2  #define PFL2_SEC0_WL22  0x4580
 462      =2  #define PFL2_SEC0_WL23  0x45C0
 463      =2  #define PFL2_SEC0_WL24  0x4600
 464      =2  #define PFL2_SEC0_WL25  0x4640
 465      =2  #define PFL2_SEC0_WL26  0x4680
 466      =2  #define PFL2_SEC0_WL27  0x46C0
 467      =2  #define PFL2_SEC0_WL28  0x4700
 468      =2  #define PFL2_SEC0_WL29  0x4740
 469      =2  #define PFL2_SEC0_WL30  0x4780
 470      =2  #define PFL2_SEC0_WL31  0x47C0
 471      =2  #define PFL2_SEC0_WL32  0x4800
 472      =2  #define PFL2_SEC0_WL33  0x4840
 473      =2  #define PFL2_SEC0_WL34  0x4880
 474      =2  #define PFL2_SEC0_WL35  0x48C0
 475      =2  #define PFL2_SEC0_WL36  0x4900
 476      =2  #define PFL2_SEC0_WL37  0x4940
 477      =2  #define PFL2_SEC0_WL38  0x4980
 478      =2  #define PFL2_SEC0_WL39  0x49C0
 479      =2  #define PFL2_SEC0_WL40  0x4A00
 480      =2  #define PFL2_SEC0_WL41  0x4A40
 481      =2  #define PFL2_SEC0_WL42  0x4A80
 482      =2  #define PFL2_SEC0_WL43  0x4AC0
 483      =2  #define PFL2_SEC0_WL44  0x4B00
 484      =2  #define PFL2_SEC0_WL45  0x4B40
 485      =2  #define PFL2_SEC0_WL46  0x4B80
 486      =2  #define PFL2_SEC0_WL47  0x4BC0
 487      =2  #define PFL2_SEC0_WL48  0x4C00
 488      =2  #define PFL2_SEC0_WL49  0x4C40
 489      =2  #define PFL2_SEC0_WL50  0x4C80
 490      =2  #define PFL2_SEC0_WL51  0x4CC0
 491      =2  #define PFL2_SEC0_WL52  0x4D00
 492      =2  #define PFL2_SEC0_WL53  0x4D40
 493      =2  #define PFL2_SEC0_WL54  0x4D80
 494      =2  #define PFL2_SEC0_WL55  0x4DC0
 495      =2  #define PFL2_SEC0_WL56  0x4E00
 496      =2  #define PFL2_SEC0_WL57  0x4E40
 497      =2  #define PFL2_SEC0_WL58  0x4E80
 498      =2  #define PFL2_SEC0_WL59  0x4EC0
 499      =2  #define PFL2_SEC0_WL60  0x4F00
 500      =2  #define PFL2_SEC0_WL61  0x4F40
 501      =2  #define PFL2_SEC0_WL62  0x4F80
 502      =2  #define PFL2_SEC0_WL63  0x4FC0
 503      =2  #define PFL2_SEC0_WL64  0x5000
 504      =2  #define PFL2_SEC0_WL65  0x5040
 505      =2  #define PFL2_SEC0_WL66  0x5080
 506      =2  #define PFL2_SEC0_WL67  0x50C0
 507      =2  #define PFL2_SEC0_WL68  0x5100
 508      =2  #define PFL2_SEC0_WL69  0x5140
 509      =2  #define PFL2_SEC0_WL70  0x5180
 510      =2  #define PFL2_SEC0_WL71  0x51C0
 511      =2  #define PFL2_SEC0_WL72  0x5200
 512      =2  #define PFL2_SEC0_WL73  0x5240
 513      =2  #define PFL2_SEC0_WL74  0x5280
 514      =2  #define PFL2_SEC0_WL75  0x52C0
 515      =2  #define PFL2_SEC0_WL76  0x5300
 516      =2  #define PFL2_SEC0_WL77  0x5340
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 10  

 517      =2  #define PFL2_SEC0_WL78  0x5380
 518      =2  #define PFL2_SEC0_WL79  0x53C0
 519      =2  #define PFL2_SEC0_WL80  0x5400
 520      =2  #define PFL2_SEC0_WL81  0x5440
 521      =2  #define PFL2_SEC0_WL82  0x5480
 522      =2  #define PFL2_SEC0_WL83  0x54C0
 523      =2  #define PFL2_SEC0_WL84  0x5500
 524      =2  #define PFL2_SEC0_WL85  0x5540
 525      =2  #define PFL2_SEC0_WL86  0x5580
 526      =2  #define PFL2_SEC0_WL87  0x55C0
 527      =2  #define PFL2_SEC0_WL88  0x5600
 528      =2  #define PFL2_SEC0_WL89  0x5640
 529      =2  #define PFL2_SEC0_WL90  0x5680
 530      =2  #define PFL2_SEC0_WL91  0x56C0
 531      =2  #define PFL2_SEC0_WL92  0x5700
 532      =2  #define PFL2_SEC0_WL93  0x5740
 533      =2  #define PFL2_SEC0_WL94  0x5780
 534      =2  #define PFL2_SEC0_WL95  0x57C0
 535      =2  #define PFL2_SEC0_WL96  0x5800
 536      =2  #define PFL2_SEC0_WL97  0x5840
 537      =2  #define PFL2_SEC0_WL98  0x5880
 538      =2  #define PFL2_SEC0_WL99  0x58C0
 539      =2  #define PFL2_SEC0_WL100 0x5900
 540      =2  #define PFL2_SEC0_WL101 0x5940
 541      =2  #define PFL2_SEC0_WL102 0x5980
 542      =2  #define PFL2_SEC0_WL103 0x59C0
 543      =2  #define PFL2_SEC0_WL104 0x5A00
 544      =2  #define PFL2_SEC0_WL105 0x5A40
 545      =2  #define PFL2_SEC0_WL106 0x5A80
 546      =2  #define PFL2_SEC0_WL107 0x5AC0
 547      =2  #define PFL2_SEC0_WL108 0x5B00
 548      =2  #define PFL2_SEC0_WL109 0x5B40
 549      =2  #define PFL2_SEC0_WL110 0x5B80
 550      =2  #define PFL2_SEC0_WL111 0x5BC0
 551      =2  #define PFL2_SEC0_WL112 0x5C00
 552      =2  #define PFL2_SEC0_WL113 0x5C40
 553      =2  #define PFL2_SEC0_WL114 0x5C80
 554      =2  #define PFL2_SEC0_WL115 0x5CC0
 555      =2  #define PFL2_SEC0_WL116 0x5D00
 556      =2  #define PFL2_SEC0_WL117 0x5D40
 557      =2  #define PFL2_SEC0_WL118 0x5D80
 558      =2  #define PFL2_SEC0_WL119 0x5DC0
 559      =2  
 560      =2  // Sector 1 -- 256 bytes -- 4 Wordlines
 561      =2  #define PFL2_SEC1_WL120 0x5E00
 562      =2  #define PFL2_SEC1_WL121 0x5E40
 563      =2  #define PFL2_SEC1_WL122 0x5E80
 564      =2  #define PFL2_SEC1_WL123 0x5EC0
 565      =2  
 566      =2  // Sector 2 -- 256 bytes -- 4 Wordlines
 567      =2  #define PFL2_SEC2_WL124 0x5F00
 568      =2  #define PFL2_SEC2_WL125 0x5F40
 569      =2  #define PFL2_SEC2_WL126 0x5F80
 570      =2  #define PFL2_SEC2_WL127 0x5FC0
 571      =2  
 572      =2  //###################################################################################
 573      =2  // WordLine startaddresses for sectors in DFlash 0, each WL with 32bytes
 574      =2  // DFlash 0 address is also mirrorred to 0x7xxx
 575      =2  
 576      =2  // Sector 0 -- 1024 bytes -- 32 Wordlines
 577      =2  #define DFL0_SEC0_WL00  0xA000
 578      =2  #define DFL0_SEC0_WL01  0xA020
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 11  

 579      =2  #define DFL0_SEC0_WL02  0xA040
 580      =2  #define DFL0_SEC0_WL03  0xA060
 581      =2  #define DFL0_SEC0_WL04  0xA080
 582      =2  #define DFL0_SEC0_WL05  0xA0A0
 583      =2  #define DFL0_SEC0_WL06  0xA0C0
 584      =2  #define DFL0_SEC0_WL07  0xA0E0
 585      =2  #define DFL0_SEC0_WL08  0xA100
 586      =2  #define DFL0_SEC0_WL09  0xA120
 587      =2  #define DFL0_SEC0_WL10  0xA140
 588      =2  #define DFL0_SEC0_WL11  0xA160
 589      =2  #define DFL0_SEC0_WL12  0xA180
 590      =2  #define DFL0_SEC0_WL13  0xA1A0
 591      =2  #define DFL0_SEC0_WL14  0xA1C0
 592      =2  #define DFL0_SEC0_WL15  0xA1E0
 593      =2  #define DFL0_SEC0_WL16  0xA200
 594      =2  #define DFL0_SEC0_WL17  0xA220
 595      =2  #define DFL0_SEC0_WL18  0xA240
 596      =2  #define DFL0_SEC0_WL19  0xA260
 597      =2  #define DFL0_SEC0_WL20  0xA280
 598      =2  #define DFL0_SEC0_WL21  0xA2A0
 599      =2  #define DFL0_SEC0_WL22  0xA2C0
 600      =2  #define DFL0_SEC0_WL23  0xA2E0
 601      =2  #define DFL0_SEC0_WL24  0xA300
 602      =2  #define DFL0_SEC0_WL25  0xA320
 603      =2  #define DFL0_SEC0_WL26  0xA340
 604      =2  #define DFL0_SEC0_WL27  0xA360
 605      =2  #define DFL0_SEC0_WL28  0xA380
 606      =2  #define DFL0_SEC0_WL29  0xA3A0
 607      =2  #define DFL0_SEC0_WL30  0xA3C0
 608      =2  #define DFL0_SEC0_WL31  0xA3E0
 609      =2  
 610      =2  // Sector 1 -- 1024 bytes -- 32 Wordlines
 611      =2  #define DFL0_SEC1_WL32  0xA400
 612      =2  #define DFL0_SEC1_WL33  0xA420
 613      =2  #define DFL0_SEC1_WL34  0xA440
 614      =2  #define DFL0_SEC1_WL35  0xA460
 615      =2  #define DFL0_SEC1_WL36  0xA480
 616      =2  #define DFL0_SEC1_WL37  0xA4A0
 617      =2  #define DFL0_SEC1_WL38  0xA4C0
 618      =2  #define DFL0_SEC1_WL39  0xA4E0
 619      =2  #define DFL0_SEC1_WL40  0xA500
 620      =2  #define DFL0_SEC1_WL41  0xA520
 621      =2  #define DFL0_SEC1_WL42  0xA540
 622      =2  #define DFL0_SEC1_WL43  0xA560
 623      =2  #define DFL0_SEC1_WL44  0xA580
 624      =2  #define DFL0_SEC1_WL45  0xA5A0
 625      =2  #define DFL0_SEC1_WL46  0xA5C0
 626      =2  #define DFL0_SEC1_WL47  0xA5E0
 627      =2  #define DFL0_SEC1_WL48  0xA600
 628      =2  #define DFL0_SEC1_WL49  0xA620
 629      =2  #define DFL0_SEC1_WL50  0xA640
 630      =2  #define DFL0_SEC1_WL51  0xA660
 631      =2  #define DFL0_SEC1_WL52  0xA680
 632      =2  #define DFL0_SEC1_WL53  0xA6A0
 633      =2  #define DFL0_SEC1_WL54  0xA6C0
 634      =2  #define DFL0_SEC1_WL55  0xA6E0
 635      =2  #define DFL0_SEC1_WL56  0xA700
 636      =2  #define DFL0_SEC1_WL57  0xA720
 637      =2  #define DFL0_SEC1_WL58  0xA740
 638      =2  #define DFL0_SEC1_WL59  0xA760
 639      =2  #define DFL0_SEC1_WL60  0xA780
 640      =2  #define DFL0_SEC1_WL61  0xA7A0
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 12  

 641      =2  #define DFL0_SEC1_WL62  0xA7C0
 642      =2  #define DFL0_SEC1_WL63  0xA7E0
 643      =2  
 644      =2  // Sector 2 -- 512 bytes -- 16 Wordlines
 645      =2  #define DFL0_SEC2_WL64  0xA800
 646      =2  #define DFL0_SEC2_WL65  0xA820
 647      =2  #define DFL0_SEC2_WL66  0xA840
 648      =2  #define DFL0_SEC2_WL67  0xA860
 649      =2  #define DFL0_SEC2_WL68  0xA880
 650      =2  #define DFL0_SEC2_WL69  0xA8A0
 651      =2  #define DFL0_SEC2_WL70  0xA8C0
 652      =2  #define DFL0_SEC2_WL71  0xA8E0
 653      =2  #define DFL0_SEC2_WL72  0xA900
 654      =2  #define DFL0_SEC2_WL73  0xA920
 655      =2  #define DFL0_SEC2_WL74  0xA940
 656      =2  #define DFL0_SEC2_WL75  0xA960
 657      =2  #define DFL0_SEC2_WL76  0xA980
 658      =2  #define DFL0_SEC2_WL77  0xA9A0
 659      =2  #define DFL0_SEC2_WL78  0xA9C0
 660      =2  #define DFL0_SEC2_WL79  0xA9E0
 661      =2  
 662      =2  // Sector 3 -- 512 bytes -- 16 Wordlines
 663      =2  #define DFL0_SEC3_WL80  0xAA00
 664      =2  #define DFL0_SEC3_WL81  0xAA20
 665      =2  #define DFL0_SEC3_WL82  0xAA40
 666      =2  #define DFL0_SEC3_WL83  0xAA60
 667      =2  #define DFL0_SEC3_WL84  0xAA80
 668      =2  #define DFL0_SEC3_WL85  0xAAA0
 669      =2  #define DFL0_SEC3_WL86  0xAAC0
 670      =2  #define DFL0_SEC3_WL87  0xAAE0
 671      =2  #define DFL0_SEC3_WL88  0xAB00
 672      =2  #define DFL0_SEC3_WL89  0xAB20
 673      =2  #define DFL0_SEC3_WL90  0xAB40
 674      =2  #define DFL0_SEC3_WL91  0xAB60
 675      =2  #define DFL0_SEC3_WL92  0xAB80
 676      =2  #define DFL0_SEC3_WL93  0xABA0
 677      =2  #define DFL0_SEC3_WL94  0xABC0
 678      =2  #define DFL0_SEC3_WL95  0xABE0
 679      =2  
 680      =2  // Sector 4 -- 256 bytes -- 8 Wordlines
 681      =2  #define DFL0_SEC4_WL96  0xAC00
 682      =2  #define DFL0_SEC4_WL97  0xAC20
 683      =2  #define DFL0_SEC4_WL98  0xAC40
 684      =2  #define DFL0_SEC4_WL99  0xAC60
 685      =2  #define DFL0_SEC4_WL100 0xAC80
 686      =2  #define DFL0_SEC4_WL101 0xACA0
 687      =2  #define DFL0_SEC4_WL102 0xACC0
 688      =2  #define DFL0_SEC4_WL103 0xACE0
 689      =2  
 690      =2  // Sector 5 -- 256 bytes -- 8 Wordlines
 691      =2  #define DFL0_SEC5_WL104 0xAD00
 692      =2  #define DFL0_SEC5_WL105 0xAD20
 693      =2  #define DFL0_SEC5_WL106 0xAD40
 694      =2  #define DFL0_SEC5_WL107 0xAD60
 695      =2  #define DFL0_SEC5_WL108 0xAD80
 696      =2  #define DFL0_SEC5_WL109 0xADA0
 697      =2  #define DFL0_SEC5_WL110 0xADC0
 698      =2  #define DFL0_SEC5_WL111 0xADE0
 699      =2  
 700      =2  // Sector 6 -- 128 bytes -- 4 Wordlines
 701      =2  #define DFL0_SEC6_WL112 0xAE00
 702      =2  #define DFL0_SEC6_WL113 0xAE20
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 13  

 703      =2  #define DFL0_SEC6_WL114 0xAE40
 704      =2  #define DFL0_SEC6_WL115 0xAE60
 705      =2  
 706      =2  // Sector 7 -- 128 bytes -- 4 Wordlines
 707      =2  #define DFL0_SEC7_WL116 0xAE80
 708      =2  #define DFL0_SEC7_WL117 0xAEA0
 709      =2  #define DFL0_SEC7_WL118 0xAEC0
 710      =2  #define DFL0_SEC7_WL119 0xAEE0
 711      =2  
 712      =2  // Sector 8 -- 128 bytes -- 4 Wordlines
 713      =2  #define DFL0_SEC8_WL120 0xAF00
 714      =2  #define DFL0_SEC8_WL121 0xAF20
 715      =2  #define DFL0_SEC8_WL122 0xAF40
 716      =2  #define DFL0_SEC8_WL123 0xAF60
 717      =2  
 718      =2  // Sector 9 -- 128 bytes -- 4 Wordlines
 719      =2  #define DFL0_SEC9_WL124 0xAF80
 720      =2  #define DFL0_SEC9_WL125 0xAFA0
 721      =2  #define DFL0_SEC9_WL126 0xAFC0
 722      =2  #define DFL0_SEC9_WL127 0xAFE0
 723      =2  
 724      =2  //###################################################################################
 725      =2  // WordLine startaddresses for sectors in DFlash 1, each WL with 32bytes
 726      =2  // DFlash 0 address is also mirrorred to 0x6xxx
 727      =2  
 728      =2  // Sector 0 -- 1024 bytes -- 32 Wordlines
 729      =2  #define DFL1_SEC0_WL00  0xA000
 730      =2  #define DFL1_SEC0_WL01  0xA020
 731      =2  #define DFL1_SEC0_WL02  0xA040
 732      =2  #define DFL1_SEC0_WL03  0xA060
 733      =2  #define DFL1_SEC0_WL04  0xA080
 734      =2  #define DFL1_SEC0_WL05  0xA0A0
 735      =2  #define DFL1_SEC0_WL06  0xA0C0
 736      =2  #define DFL1_SEC0_WL07  0xA0E0
 737      =2  #define DFL1_SEC0_WL08  0xA100
 738      =2  #define DFL1_SEC0_WL09  0xA120
 739      =2  #define DFL1_SEC0_WL10  0xA140
 740      =2  #define DFL1_SEC0_WL11  0xA160
 741      =2  #define DFL1_SEC0_WL12  0xA180
 742      =2  #define DFL1_SEC0_WL13  0xA1A0
 743      =2  #define DFL1_SEC0_WL14  0xA1C0
 744      =2  #define DFL1_SEC0_WL15  0xA1E0
 745      =2  #define DFL1_SEC0_WL16  0xA200
 746      =2  #define DFL1_SEC0_WL17  0xA220
 747      =2  #define DFL1_SEC0_WL18  0xA240
 748      =2  #define DFL1_SEC0_WL19  0xA260
 749      =2  #define DFL1_SEC0_WL20  0xA280
 750      =2  #define DFL1_SEC0_WL21  0xA2A0
 751      =2  #define DFL1_SEC0_WL22  0xA2C0
 752      =2  #define DFL1_SEC0_WL23  0xA2E0
 753      =2  #define DFL1_SEC0_WL24  0xA300
 754      =2  #define DFL1_SEC0_WL25  0xA320
 755      =2  #define DFL1_SEC0_WL26  0xA340
 756      =2  #define DFL1_SEC0_WL27  0xA360
 757      =2  #define DFL1_SEC0_WL28  0xA380
 758      =2  #define DFL1_SEC0_WL29  0xA3A0
 759      =2  #define DFL1_SEC0_WL30  0xA3C0
 760      =2  #define DFL1_SEC0_WL31  0xA3E0
 761      =2  
 762      =2  // Sector 1 -- 1024 bytes -- 32 Wordlines
 763      =2  #define DFL1_SEC1_WL32  0xA400
 764      =2  #define DFL1_SEC1_WL33  0xA420
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 14  

 765      =2  #define DFL1_SEC1_WL34  0xA440
 766      =2  #define DFL1_SEC1_WL35  0xA460
 767      =2  #define DFL1_SEC1_WL36  0xA480
 768      =2  #define DFL1_SEC1_WL37  0xA4A0
 769      =2  #define DFL1_SEC1_WL38  0xA4C0
 770      =2  #define DFL1_SEC1_WL39  0xA4E0
 771      =2  #define DFL1_SEC1_WL40  0xA500
 772      =2  #define DFL1_SEC1_WL41  0xA520
 773      =2  #define DFL1_SEC1_WL42  0xA540
 774      =2  #define DFL1_SEC1_WL43  0xA560
 775      =2  #define DFL1_SEC1_WL44  0xA580
 776      =2  #define DFL1_SEC1_WL45  0xA5A0
 777      =2  #define DFL1_SEC1_WL46  0xA5C0
 778      =2  #define DFL1_SEC1_WL47  0xA5E0
 779      =2  #define DFL1_SEC1_WL48  0xA600
 780      =2  #define DFL1_SEC1_WL49  0xA620
 781      =2  #define DFL1_SEC1_WL50  0xA640
 782      =2  #define DFL1_SEC1_WL51  0xA660
 783      =2  #define DFL1_SEC1_WL52  0xA680
 784      =2  #define DFL1_SEC1_WL53  0xA6A0
 785      =2  #define DFL1_SEC1_WL54  0xA6C0
 786      =2  #define DFL1_SEC1_WL55  0xA6E0
 787      =2  #define DFL1_SEC1_WL56  0xA700
 788      =2  #define DFL1_SEC1_WL57  0xA720
 789      =2  #define DFL1_SEC1_WL58  0xA740
 790      =2  #define DFL1_SEC1_WL59  0xA760
 791      =2  #define DFL1_SEC1_WL60  0xA780
 792      =2  #define DFL1_SEC1_WL61  0xA7A0
 793      =2  #define DFL1_SEC1_WL62  0xA7C0
 794      =2  #define DFL1_SEC1_WL63  0xA7E0
 795      =2  
 796      =2  // Sector 2 -- 512 bytes -- 16 Wordlines
 797      =2  #define DFL1_SEC2_WL64  0xA800
 798      =2  #define DFL1_SEC2_WL65  0xA820
 799      =2  #define DFL1_SEC2_WL66  0xA840
 800      =2  #define DFL1_SEC2_WL67  0xA860
 801      =2  #define DFL1_SEC2_WL68  0xA880
 802      =2  #define DFL1_SEC2_WL69  0xA8A0
 803      =2  #define DFL1_SEC2_WL70  0xA8C0
 804      =2  #define DFL1_SEC2_WL71  0xA8E0
 805      =2  #define DFL1_SEC2_WL72  0xA900
 806      =2  #define DFL1_SEC2_WL73  0xA920
 807      =2  #define DFL1_SEC2_WL74  0xA940
 808      =2  #define DFL1_SEC2_WL75  0xA960
 809      =2  #define DFL1_SEC2_WL76  0xA980
 810      =2  #define DFL1_SEC2_WL77  0xA9A0
 811      =2  #define DFL1_SEC2_WL78  0xA9C0
 812      =2  #define DFL1_SEC2_WL79  0xA9E0
 813      =2  
 814      =2  // Sector 3 -- 512 bytes -- 16 Wordlines
 815      =2  #define DFL1_SEC3_WL80  0xAA00
 816      =2  #define DFL1_SEC3_WL81  0xAA20
 817      =2  #define DFL1_SEC3_WL82  0xAA40
 818      =2  #define DFL1_SEC3_WL83  0xAA60
 819      =2  #define DFL1_SEC3_WL84  0xAA80
 820      =2  #define DFL1_SEC3_WL85  0xAAA0
 821      =2  #define DFL1_SEC3_WL86  0xAAC0
 822      =2  #define DFL1_SEC3_WL87  0xAAE0
 823      =2  #define DFL1_SEC3_WL88  0xAB00
 824      =2  #define DFL1_SEC3_WL89  0xAB20
 825      =2  #define DFL1_SEC3_WL90  0xAB40
 826      =2  #define DFL1_SEC3_WL91  0xAB60
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 15  

 827      =2  #define DFL1_SEC3_WL92  0xAB80
 828      =2  #define DFL1_SEC3_WL93  0xABA0
 829      =2  #define DFL1_SEC3_WL94  0xABC0
 830      =2  #define DFL1_SEC3_WL95  0xABE0
 831      =2  
 832      =2  // Sector 4 -- 256 bytes -- 8 Wordlines
 833      =2  #define DFL1_SEC4_WL96  0xAC00
 834      =2  #define DFL1_SEC4_WL97  0xAC20
 835      =2  #define DFL1_SEC4_WL98  0xAC40
 836      =2  #define DFL1_SEC4_WL99  0xAC60
 837      =2  #define DFL1_SEC4_WL100 0xAC80
 838      =2  #define DFL1_SEC4_WL101 0xACA0
 839      =2  #define DFL1_SEC4_WL102 0xACC0
 840      =2  #define DFL1_SEC4_WL103 0xACE0
 841      =2  
 842      =2  // Sector 5 -- 256 bytes -- 8 Wordlines
 843      =2  #define DFL1_SEC5_WL104 0xAD00
 844      =2  #define DFL1_SEC5_WL105 0xAD20
 845      =2  #define DFL1_SEC5_WL106 0xAD40
 846      =2  #define DFL1_SEC5_WL107 0xAD60
 847      =2  #define DFL1_SEC5_WL108 0xAD80
 848      =2  #define DFL1_SEC5_WL109 0xADA0
 849      =2  #define DFL1_SEC5_WL110 0xADC0
 850      =2  #define DFL1_SEC5_WL111 0xADE0
 851      =2  
 852      =2  // Sector 6 -- 128 bytes -- 4 Wordlines
 853      =2  #define DFL1_SEC6_WL112 0xAE00
 854      =2  #define DFL1_SEC6_WL113 0xAE20
 855      =2  #define DFL1_SEC6_WL114 0xAE40
 856      =2  #define DFL1_SEC6_WL115 0xAE60
 857      =2  
 858      =2  // Sector 7 -- 128 bytes -- 4 Wordlines
 859      =2  #define DFL1_SEC7_WL116 0xAE80
 860      =2  #define DFL1_SEC7_WL117 0xAEA0
 861      =2  #define DFL1_SEC7_WL118 0xAEC0
 862      =2  #define DFL1_SEC7_WL119 0xAEE0
 863      =2  
 864      =2  // Sector 8 -- 128 bytes -- 4 Wordlines
 865      =2  #define DFL1_SEC8_WL120 0xAF00
 866      =2  #define DFL1_SEC8_WL121 0xAF20
 867      =2  #define DFL1_SEC8_WL122 0xAF40
 868      =2  #define DFL1_SEC8_WL123 0xAF60
 869      =2  
 870      =2  // Sector 9 -- 128 bytes -- 4 Wordlines
 871      =2  #define DFL1_SEC9_WL124 0xAF80
 872      =2  #define DFL1_SEC9_WL125 0xAFA0
 873      =2  #define DFL1_SEC9_WL126 0xAFC0
 874      =2  #define DFL1_SEC9_WL127 0xAFE0
 875      =2  
 876      =2  #endif  // #ifndef  __XC88x_FLADDR_H__
  21      =1  
  22      =1  
  23      =1  //+------------------------------------------------+
  24      =1  //|   Global variables needed
  25      =1  //+------------------------------------------------+
  26      =1  // Buffer for the 32byte data before being written to eEPROM
  27      =1  extern unsigned char idata WLBuf[BYTES_PER_WORDLINE];
  28      =1  
  29      =1  // Function to program the WLBuf to Flash Memory
  30      =1  bit ProgWL(unsigned char code *AdrBnkXSecYWLZ);
  31      =1  // Function to load the data from XDATA memory to WLBuf
  32      =1  void LoadXD2WLBuf(unsigned char xdata *address);
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 16  

  33      =1  // Function to load the data from CODE memory to WLBuf
  34      =1  // This is used to copy data from one Flash location to another.
  35      =1  void LoadConst2WLBuf(unsigned char code *address);
  36      =1  // Function to load the data from CODE memory to XDATA memory
  37      =1  void LoadConst2XD(unsigned char xdata *dstaddress, unsigned char code *srcaddress, unsigned char length);
  38      =1  // Function to read a byte from CODE memory
  39      =1  unsigned char ReadConst(unsigned char code *address);
  40      =1  
  41      =1  // All of the description of the functions below is done in XC88x_FLHANDER.ASM
  42      =1  // Erase Flash Sectors
  43      =1  // Required Stack Size = 12
  44      =1  extern bit PFlErase (unsigned char Bank0Sector, unsigned char Bank1Sector, unsigned char Bank2Sector);
  45      =1  extern bit DFlErase (unsigned int DFlash0Sector, unsigned int DFlash1Sector);
  46      =1  
  47      =1  // Program Flash
  48      =1  // Required Stack Size = 12
  49      =1  extern bit FlProg(unsigned char idata *SrcBuf);
  50      =1  
  51      =1  // Check if Flash is ready to read
  52      =1  // Bank = Bank Number to be checked
  53      =1  // Required Stack Size = 6
  54      =1  extern bit FlReady(unsigned char Bank);
  55      =1  
  56      =1  // Abort the existing erase process
  57      =1  // Required Stack Size = 6
  58      =1  extern bit FlEraseAbort(void);
  59      =1  
  60      =1  // Program / Remove Password.
  61      =1  // Detail is available in FLHANDLER.asm
  62      =1  extern bit FlProtect(unsigned char Password);
  63      =1  
  64      =1  extern unsigned char _FlReadByte(void);
  65      =1  extern void FlExecute(unsigned char AddrH, unsigned char AddrL);
  66      =1  
  67      =1  sfr MEM_DPH    = 0x83;    
  68      =1  sfr MEM_DPL    = 0x82;
  69      =1  sfr MEM_NMICON = 0xBB;
  70      =1  sfr MEM_NMISR  = 0xBC;
  71      =1  
  72      =1  #endif  // _XC8MEMORY_H_
  15          
  16          /*
  17          // **************************************************************************
  18          // Function Name:    void LoadXD2WLBuf (unsigned char xdata *address)
  19          // Description:      Load  32byte of data from the XDATA to the WLBuf
  20          //                   WLBuf is a buffer that is located in the IDATA-space
  21          // Input Parameter:  *address ==>  pointer to the address
  22          //                                 in the XDATA memory
  23          // Output Parameter: none
  24          
  25          // NOTE: Execution time and code can saved, 
  26          //       if you have the possibility to write directly in
  27          //       the buffer
  28          // **************************************************************************
  29          void LoadXD2WLBuf(unsigned char xdata *address)
  30          {
  31          // Loads Wordline Buffer with 32bytes from given address in XDATA
  32          unsigned char i;
  33           for (i=0; i<BYTES_PER_WORDLINE; i++) {WLBuf[i] = *address++;}
  34          }
  35          
  36          // ***********************************************************************
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 17  

  37          // Function Name:    void LoadConst2WLBuf (unsigned char code *address)
  38          // Description:      Load 32byte of data from the CODE to the WLBuf
  39          //                   WLBuf is a buffer that is located in the IDATA-space
  40          //                   This is used to copy data from one Flash location to another.
  41          // Input Parameter:  *address ==>  pointer to the address
  42          //            in the CODE memory
  43          // Output Parameter: none
  44          
  45          // NOTE: Execution time and code can saved, 
  46          //       if you have the possibility to write directly in
  47          //       the buffer
  48          // ***********************************************************************
  49          void LoadConst2WLBuf(unsigned char code *address)
  50          {
  51          // Loads Wordline Buffer with 32bytes from given address in CODE
  52          unsigned char i;
  53           for (i=0; i<BYTES_PER_WORDLINE; i++) 
  54           { WLBuf[i] = *address++;
  55           }
  56          }
  57          
  58          // **************************************************************************************
  59          // Function Name:    bit ProgWL (unsigned char code *AdrBnkXSecYWLZ)
  60          // Description:      Program the data from the WLBuf to the location in the Flash
  61          //                   which is pointed by *AdrBnkXSecYWLZ
  62          // Input Parameter:  *AdrBnkXSecYWLZ ==> pointer to the location in the FLASH.
  63          //                                       The address must be aligned to a 32byte boundary
  64          // Output Parameter: 1 = Success
  65          //                   0 = Fail
  66          // Note:             used Stack Size = 12
  67          // **************************************************************************************
  68          bit ProgWL(unsigned char code *AdrBnkXSecYWLZ)
  69          {
  70          unsigned int i;
  71           i = AdrBnkXSecYWLZ;
  72           MEM_DPH = i>>8;
  73           MEM_DPL = i;
  74           return (FlProg(&WLBuf[0]));
  75          }
  76          
  77          // *************************************************************************************
  78          // Function Name:    void LoadConst2XD (unsigned char xdata *dstaddress, 
  79          //                   unsigned char code *srcaddress, unsigned int length)
  80          // Description:      Copy the data from the CODE memory to XDATA memory
  81          // Input Parameter:  *srcaddress ==> pointer to the location in the FLASH/EEPROM.
  82          //                   lenght      ==> The number of byte to be copied
  83          // Output Parameter: *dstaddress ==> pointer to the location in the XDATA memory.
  84          //                                   The data will be copied to this location
  85          
  86          // NOTE: Execution time and code can saved, 
  87          //       if you have the possibility to write directly in
  88          //       the buffer
  89          // *************************************************************************************
  90          void LoadConst2XD(unsigned char xdata *dstaddress, unsigned char code *srcaddress, unsigned char length)
  91          {
  92          // Loads number of constants to XDATA at given addresses
  93          unsigned char i;
  94           for (i=0; i<length; i++) 
  95           { *dstaddress++ = *srcaddress++;
  96           }
  97          }
  98          
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 18  

  99          // *************************************************************************************
 100          // Function Name:    void LoadXD2XD (unsigned char xdata *dstaddress, 
 101          //                   unsigned char xdata *srcaddress, unsigned int length)
 102          // Description:      Copy the data from the XDATA memory to XDATA memory
 103          // Input Parameter:  *srcaddress ==> pointer to the location in the XDATA memory.
 104          //                                   No byte alignment is necessary
 105          //                   lenght      ==> The number of byte to be copied
 106          // Output Parameter: *dstaddress ==> pointer to the location in the XDATA memory.
 107          //                                   The data will be copied to this location
 108          //
 109          // NOTE: Execution time and code can saved, 
 110          //       if you have the possibility to write directly in
 111          //       the buffer
 112          // *************************************************************************************
 113          void LoadXD2XD(unsigned char xdata *dstaddress, unsigned char xdata *srcaddress, unsigned int length)
 114          {
 115          // Copies number of bytes in XDATA to XDATA at given addresses
 116          unsigned int i;
 117           for (i=0; i<length; i++) 
 118           { *dstaddress++ = *srcaddress++;
 119           }
 120          }
 121          
 122          // *************************************************************************************
 123          // Function Name:    unsigned char ReadConst (unsigned char code *address) 
 124          // Description:      Read from CODE memory
 125          // Input Parameter:  *address ==> pointer to the location in the CODE memory.
 126          //                                No byte alignment is necessary
 127          // Return Value:  Data Byte
 128          // *************************************************************************************
 129          unsigned char ReadConst(unsigned char code *address)
 130          {
 131           return (*address);
 132          }
 133          
 134          */
C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 19  

ASSEMBLY LISTING OF GENERATED OBJECT CODE


C51 COMPILER V9.50a   XC8MEMORY                                                            07/06/2013 16:28:02 PAGE 20  

NAME                                    CLASS   MSPACE  TYPE    OFFSET  SIZE
====                                    =====   ======  ====    ======  ====


MEM_NMICON . . . . . . . . . . . . . .  SFR      DATA   U_CHAR   00BBH  1
MEM_DPH. . . . . . . . . . . . . . . .  SFR      DATA   U_CHAR   0083H  1
MEM_DPL. . . . . . . . . . . . . . . .  SFR      DATA   U_CHAR   0082H  1
MEM_NMISR. . . . . . . . . . . . . . .  SFR      DATA   U_CHAR   00BCH  1


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =   ----    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
   EDATA SIZE       =   ----    ----
   HDATA SIZE       =   ----    ----
   XDATA CONST SIZE =   ----    ----
   FAR CONST SIZE   =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)

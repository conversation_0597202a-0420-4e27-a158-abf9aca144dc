
#line 1 "IO.C" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
  
#line 1 "MAIN.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 sfr ACC        = 0xE0;    
 sfr ADC_CHCTR0 = 0xCA;    
 sfr ADC_CHCTR1 = 0xCB;    
 sfr ADC_CHCTR2 = 0xCC;    
 sfr ADC_CHCTR3 = 0xCD;    
 sfr ADC_CHCTR4 = 0xCE;    
 sfr ADC_CHCTR5 = 0xCF;    
 sfr ADC_CHCTR6 = 0xD2;    
 sfr ADC_CHCTR7 = 0xD3;    
 sfr ADC_CHINCR = 0xCB;    
 sfr ADC_CHINFR = 0xCA;    
 sfr ADC_CHINPR = 0xCD;    
 sfr ADC_CHINSR = 0xCC;    
 sfr ADC_CRCR1  = 0xCA;    
 sfr ADC_CRMR1  = 0xCC;    
 sfr ADC_CRPR1  = 0xCB;    
 sfr ADC_ETRCR  = 0xCF;    
 sfr ADC_EVINCR = 0xCF;    
 sfr ADC_EVINFR = 0xCE;    
 sfr ADC_EVINPR = 0xD3;    
 sfr ADC_EVINSR = 0xD2;    
 sfr ADC_GLOBCTR = 0xCA;    
 sfr ADC_GLOBSTR = 0xCB;    
 sfr ADC_INPCR0 = 0xCE;    
 sfr ADC_LCBR   = 0xCD;    
 sfr ADC_PAGE   = 0xD1;    
 sfr ADC_PRAR   = 0xCC;    
 sfr ADC_Q0R0   = 0xCF;    
 sfr ADC_QBUR0  = 0xD2;    
 sfr ADC_QINR0  = 0xD2;    
 sfr ADC_QMR0   = 0xCD;    
 sfr ADC_QSR0   = 0xCE;    
 sfr ADC_RCR0   = 0xCA;    
 sfr ADC_RCR1   = 0xCB;    
 sfr ADC_RCR2   = 0xCC;    
 sfr ADC_RCR3   = 0xCD;    
 sfr ADC_RESR0H = 0xCB;    
 sfr ADC_RESR0L = 0xCA;    
 sfr ADC_RESR1H = 0xCD;    
 sfr ADC_RESR1L = 0xCC;    
 sfr ADC_RESR2H = 0xCF;    
 sfr ADC_RESR2L = 0xCE;    
 sfr ADC_RESR3H = 0xD3;    
 sfr ADC_RESR3L = 0xD2;    
 sfr ADC_RESRA0H = 0xCB;    
 sfr ADC_RESRA0L = 0xCA;    
 sfr ADC_RESRA1H = 0xCD;    
 sfr ADC_RESRA1L = 0xCC;    
 sfr ADC_RESRA2H = 0xCF;    
 sfr ADC_RESRA2L = 0xCE;    
 sfr ADC_RESRA3H = 0xD3;    
 sfr ADC_RESRA3L = 0xD2;    
 sfr ADC_VFCR   = 0xCE;    
 sfr B          = 0xF0;    
 sfr BCON       = 0xBD;    
 sfr BG         = 0xBE;    
 sfr CAN_ADCON  = 0xD8;    
 sfr CAN_ADH    = 0xDA;    
 sfr CAN_ADL    = 0xD9;    
 sfr CAN_DATA0  = 0xDB;    
 sfr CAN_DATA1  = 0xDC;    
 sfr CAN_DATA2  = 0xDD;    
 sfr CAN_DATA3  = 0xDE;    
 sfr CCU6_CC60RH = 0xFB;    
 sfr CCU6_CC60RL = 0xFA;    
 sfr CCU6_CC60SRH = 0xFB;    
 sfr CCU6_CC60SRL = 0xFA;    
 sfr CCU6_CC61RH = 0xFD;    
 sfr CCU6_CC61RL = 0xFC;    
 sfr CCU6_CC61SRH = 0xFD;    
 sfr CCU6_CC61SRL = 0xFC;    
 sfr CCU6_CC62RH = 0xFF;    
 sfr CCU6_CC62RL = 0xFE;    
 sfr CCU6_CC62SRH = 0xFF;    
 sfr CCU6_CC62SRL = 0xFE;    
 sfr CCU6_CC63RH = 0x9B;    
 sfr CCU6_CC63RL = 0x9A;    
 sfr CCU6_CC63SRH = 0x9B;    
 sfr CCU6_CC63SRL = 0x9A;    
 sfr CCU6_CMPMODIFH = 0xA7;    
 sfr CCU6_CMPMODIFL = 0xA6;    
 sfr CCU6_CMPSTATH = 0xFF;    
 sfr CCU6_CMPSTATL = 0xFE;    
 sfr CCU6_IENH  = 0x9D;    
 sfr CCU6_IENL  = 0x9C;    
 sfr CCU6_INPH  = 0x9F;    
 sfr CCU6_INPL  = 0x9E;    
 sfr CCU6_ISH   = 0x9D;    
 sfr CCU6_ISL   = 0x9C;    
 sfr CCU6_ISRH  = 0xA5;    
 sfr CCU6_ISRL  = 0xA4;    
 sfr CCU6_ISSH  = 0xA5;    
 sfr CCU6_ISSL  = 0xA4;    
 sfr CCU6_MCMCTR = 0xA7;    
 sfr CCU6_MCMOUTH = 0x9B;    
 sfr CCU6_MCMOUTL = 0x9A;    
 sfr CCU6_MCMOUTSH = 0x9F;    
 sfr CCU6_MCMOUTSL = 0x9E;    
 sfr CCU6_MODCTRH = 0xFD;    
 sfr CCU6_MODCTRL = 0xFC;    
 sfr CCU6_PAGE  = 0xA3;    
 sfr CCU6_PISEL0H = 0x9F;    
 sfr CCU6_PISEL0L = 0x9E;    
 sfr CCU6_PISEL2 = 0xA4;    
 sfr CCU6_PSLR  = 0xA6;    
 sfr CCU6_T12DTCH = 0xA5;    
 sfr CCU6_T12DTCL = 0xA4;    
 sfr CCU6_T12H  = 0xFB;    
 sfr CCU6_T12L  = 0xFA;    
 sfr CCU6_T12MSELH = 0x9B;    
 sfr CCU6_T12MSELL = 0x9A;    
 sfr CCU6_T12PRH = 0x9D;    
 sfr CCU6_T12PRL = 0x9C;    
 sfr CCU6_T13H  = 0xFD;    
 sfr CCU6_T13L  = 0xFC;    
 sfr CCU6_T13PRH = 0x9F;    
 sfr CCU6_T13PRL = 0x9E;    
 sfr CCU6_TCTR0H = 0xA7;    
 sfr CCU6_TCTR0L = 0xA6;    
 sfr CCU6_TCTR2H = 0xFB;    
 sfr CCU6_TCTR2L = 0xFA;    
 sfr CCU6_TCTR4H = 0x9D;    
 sfr CCU6_TCTR4L = 0x9C;    
 sfr CCU6_TRPCTRH = 0xFF;    
 sfr CCU6_TRPCTRL = 0xFE;    
 sfr CD_CON     = 0xA1;    
 sfr CD_CORDXH  = 0x9B;    
 sfr CD_CORDXL  = 0x9A;    
 sfr CD_CORDYH  = 0x9D;    
 sfr CD_CORDYL  = 0x9C;    
 sfr CD_CORDZH  = 0x9F;    
 sfr CD_CORDZL  = 0x9E;    
 sfr CD_STATC   = 0xA0;    
 sfr CMCON      = 0xBA;    
 sfr COCON      = 0xBE;    
 sfr DPH        = 0x83;    
 sfr DPL        = 0x82;    
 sfr EO         = 0xA2;    
 sfr EXICON0    = 0xB7;    
 sfr EXICON1    = 0xBA;    
 sfr FDCON      = 0xE9;    
 sfr FDRES      = 0xEB;    
 sfr FDSTEP     = 0xEA;    
 sfr FEAH       = 0xBD;    
 sfr FEAL       = 0xBC;    
 sfr HWBPDR     = 0xF7;    
 sfr HWBPSR     = 0xF6;    
 sfr ID         = 0xB3;    
 sfr IEN0       = 0xA8;    
 sfr IEN1       = 0xE8;    
 sfr IP         = 0xB8;    
 sfr IP1        = 0xF8;    
 sfr IPH        = 0xB9;    
 sfr IPH1       = 0xF9;    
 sfr IRCON0     = 0xB4;    
 sfr IRCON1     = 0xB5;    
 sfr IRCON2     = 0xB6;    
 sfr IRCON3     = 0xB4;    
 sfr IRCON4     = 0xB5;    
 sfr MDU_MD0    = 0xB2;    
 sfr MDU_MD1    = 0xB3;    
 sfr MDU_MD2    = 0xB4;    
 sfr MDU_MD3    = 0xB5;    
 sfr MDU_MD4    = 0xB6;    
 sfr MDU_MD5    = 0xB7;    
 sfr MDU_MDUCON = 0xB1;    
 sfr MDU_MDUSTAT = 0xB0;    
 sfr MDU_MR0    = 0xB2;    
 sfr MDU_MR1    = 0xB3;    
 sfr MDU_MR2    = 0xB4;    
 sfr MDU_MR3    = 0xB5;    
 sfr MDU_MR4    = 0xB6;    
 sfr MDU_MR5    = 0xB7;    
 sfr MISC_CON   = 0xE9;    
 sfr MMBPCR     = 0xF3;    
 sfr MMCR       = 0xF1;    
 sfr MMCR2      = 0xE9;    
 sfr MMDR       = 0xF5;    
 sfr MMICR      = 0xF4;    
 sfr MMSR       = 0xF2;    
 sfr MMWR1      = 0xEB;    
 sfr MMWR2      = 0xEC;    
 sfr MODPISEL   = 0xB3;    
 sfr MODPISEL1  = 0xB7;    
 sfr MODPISEL2  = 0xBA;    
 sfr MODSUSP    = 0xBD;    
 sfr NMICON     = 0xBB;    
 sfr NMISR      = 0xBC;    
 sfr OSC_CON    = 0xB6;    
 sfr P0_ALTSEL0 = 0x80;    
 sfr P0_ALTSEL1 = 0x86;    
 sfr P0_DATA    = 0x80;    
 sfr P0_DIR     = 0x86;    
 sfr P0_OD      = 0x80;    
 sfr P0_PUDEN   = 0x86;    
 sfr P0_PUDSEL  = 0x80;    
 sfr P1_ALTSEL0 = 0x90;    
 sfr P1_ALTSEL1 = 0x91;    
 sfr P1_DATA    = 0x90;    
 sfr P1_DIR     = 0x91;    
 sfr P1_OD      = 0x90;    
 sfr P1_PUDEN   = 0x91;    
 sfr P1_PUDSEL  = 0x90;    
 sfr P2_DATA    = 0xA0;    
 sfr P2_DIR     = 0xA1;    
 sfr P2_PUDEN   = 0xA1;    
 sfr P2_PUDSEL  = 0xA0;    
 sfr P3_ALTSEL0 = 0xB0;    
 sfr P3_ALTSEL1 = 0xB1;    
 sfr P3_DATA    = 0xB0;    
 sfr P3_DIR     = 0xB1;    
 sfr P3_OD      = 0xB0;    
 sfr P3_PUDEN   = 0xB1;    
 sfr P3_PUDSEL  = 0xB0;    
 sfr P4_ALTSEL0 = 0xC8;    
 sfr P4_ALTSEL1 = 0xC9;    
 sfr P4_DATA    = 0xC8;    
 sfr P4_DIR     = 0xC9;    
 sfr P4_OD      = 0xC8;    
 sfr P4_PUDEN   = 0xC9;    
 sfr P4_PUDSEL  = 0xC8;    
 sfr P5_ALTSEL0 = 0x92;    
 sfr P5_ALTSEL1 = 0x93;    
 sfr P5_DATA    = 0x92;    
 sfr P5_DIR     = 0x93;    
 sfr P5_OD      = 0x92;    
 sfr P5_PUDEN   = 0x93;    
 sfr P5_PUDSEL  = 0x92;    
 sfr PASSWD     = 0xBB;    
 sfr PCON       = 0x87;    
 sfr PLL_CON    = 0xB7;    
 sfr PMCON0     = 0xB4;    
 sfr PMCON1     = 0xB5;    
 sfr PMCON2     = 0xBB;    
 sfr PORT_PAGE  = 0xB2;    
 sfr PSW        = 0xD0;    
 sfr SBUF       = 0x99;    
 sfr SCON       = 0x98;    
 sfr SCU_PAGE   = 0xBF;    
 sfr SP         = 0x81;    
 sfr SSC_BRH    = 0xAF;    
 sfr SSC_BRL    = 0xAE;    
 sfr SSC_CONH_O = 0xAB;    
 sfr SSC_CONH_P = 0xAB;    
 sfr SSC_CONL_O = 0xAA;    
 sfr SSC_CONL_P = 0xAA;    
 sfr SSC_PISEL  = 0xA9;    
 sfr SSC_RBL    = 0xAD;    
 sfr SSC_TBL    = 0xAC;    
 sfr SYSCON0    = 0x8F;    
 sfr T21_RC2H   = 0xC3;    
 sfr T21_RC2L   = 0xC2;    
 sfr T21_T2CON  = 0xC0;    
 sfr T21_T2H    = 0xC5;    
 sfr T21_T2L    = 0xC4;    
 sfr T21_T2MOD  = 0xC1;    
 sfr T2_RC2H    = 0xC3;    
 sfr T2_RC2L    = 0xC2;    
 sfr T2_T2CON   = 0xC0;    
 sfr T2_T2H     = 0xC5;    
 sfr T2_T2L     = 0xC4;    
 sfr T2_T2MOD   = 0xC1;    
 sfr TCON       = 0x88;    
 sfr TH0        = 0x8C;    
 sfr TH1        = 0x8D;    
 sfr TL0        = 0x8A;    
 sfr TL1        = 0x8B;    
 sfr TMOD       = 0x89;    
 sfr UART1_BCON = 0xCA;    
 sfr UART1_BG   = 0xCB;    
 sfr UART1_FDCON = 0xCC;    
 sfr UART1_FDRES = 0xCE;    
 sfr UART1_FDSTEP = 0xCD;    
 sfr UART1_SBUF = 0xC9;    
 sfr UART1_SCON = 0xC8;    
 sfr WDTCON     = 0xBB;     
 sfr WDTH       = 0xBF;     
 sfr WDTL       = 0xBE;     
 sfr WDTREL     = 0xBC;     
 sfr WDTWINB    = 0xBD;     
 sfr XADDRH     = 0xB3;    
 
 
 
 
 sbit CD_BSY     = 0xA0;    
 sbit DMAP       = 0xA4;    
 sbit EOC        = 0xA2;    
 sbit ERROR      = 0xA1;    
 sbit INT_EN     = 0xA3;    
 sbit KEEPX      = 0xA5;    
 sbit KEEPY      = 0xA6;    
 sbit KEEPZ      = 0xA7;    
 
 
 sbit EA         = 0xAF;    
 sbit ES         = 0xAC;    
 sbit ET0        = 0xA9;    
 sbit ET1        = 0xAB;    
 sbit ET2        = 0xAD;    
 sbit EX0        = 0xA8;    
 sbit EX1        = 0xAA;    
 
 
 sbit EADC       = 0xE8;    
 sbit ECCIP0     = 0xEC;    
 sbit ECCIP1     = 0xED;    
 sbit ECCIP2     = 0xEE;    
 sbit ECCIP3     = 0xEF;    
 sbit ESSC       = 0xE9;    
 sbit EX2        = 0xEA;    
 sbit EXM        = 0xEB;    
 
 
 sbit PADC       = 0xF8;    
 sbit PCCIP0     = 0xFC;    
 sbit PCCIP1     = 0xFD;    
 sbit PCCIP2     = 0xFE;    
 sbit PCCIP3     = 0xFF;    
 sbit PSSC       = 0xF9;    
 sbit PX2        = 0xFA;    
 sbit PXM        = 0xFB;    
 
 
 sbit PS         = 0xBC;    
 sbit PT0        = 0xB9;    
 sbit PT1        = 0xBB;    
 sbit PT2        = 0xBD;    
 sbit PX0        = 0xB8;    
 sbit PX1        = 0xBA;    
 
 
 sbit IERR       = 0xB1;    
 sbit IRDY       = 0xB0;    
 sbit MDU_BSY    = 0xB2;    
 
 
 sbit AC         = 0xD6;    
 sbit CY         = 0xD7;    
 sbit F0         = 0xD5;    
 sbit F1         = 0xD1;    
 sbit OV         = 0xD2;    
 sbit P          = 0xD0;    
 sbit RS0        = 0xD3;    
 sbit RS1        = 0xD4;    
 
 
 sbit RB8        = 0x9A;    
 sbit REN        = 0x9C;    
 sbit RI         = 0x98;    
 sbit SM0        = 0x9F;    
 sbit SM1        = 0x9E;    
 sbit SM2        = 0x9D;    
 sbit TB8        = 0x9B;    
 sbit TI         = 0x99;    
 
 
 sbit C_T2       = 0xC1;    
 sbit CP_RL2     = 0xC0;    
 sbit EXEN2      = 0xC3;    
 sbit EXF2       = 0xC6;    
 sbit TF2        = 0xC7;    
 sbit TR2        = 0xC2;    
 
 
 sbit IE0        = 0x89;    
 sbit IE1        = 0x8B;    
 sbit IT0        = 0x88;    
 sbit IT1        = 0x8A;    
 sbit TF0        = 0x8D;    
 sbit TF1        = 0x8F;    
 sbit TR0        = 0x8C;    
 sbit TR1        = 0x8E;    
 
 
 sbit RB8_1      = 0xCA;    
 sbit REN_1      = 0xCC;    
 sbit RI_1       = 0xC8;    
 sbit SM0_1      = 0xCF;    
 sbit SM1_1      = 0xCE;    
 sbit SM2_1      = 0xCD;    
 sbit TB8_1      = 0xCB;    
 sbit TI_1       = 0xC9;    
 
 
 
 
 
 sfr16 ADC_RESR0LH = 0xCA;         
 sfr16 ADC_RESR1LH = 0xCC;         
 sfr16 ADC_RESR2LH = 0xCE;         
 sfr16 ADC_RESR3LH = 0xD2;         
 sfr16 ADC_RESRA0LH = 0xCA;        
 sfr16 ADC_RESRA1LH = 0xCC;        
 sfr16 ADC_RESRA2LH = 0xCE;        
 sfr16 ADC_RESRA3LH = 0xD2;        
 sfr16 CAN_ADLH = 0xD9;            
 sfr16 CAN_DATA01 = 0xDB;          
 sfr16 CAN_DATA23 = 0xDD;          
 sfr16 CCU6_CC60RLH = 0xFA;        
 sfr16 CCU6_CC60SRLH = 0xFA;       
 sfr16 CCU6_CC61RLH = 0xFC;        
 sfr16 CCU6_CC61SRLH = 0xFC;       
 sfr16 CCU6_CC62RLH = 0xFE;        
 sfr16 CCU6_CC62SRLH = 0xFE;       
 sfr16 CCU6_CC63RLH = 0x9A;        
 sfr16 CCU6_CC63SRLH = 0x9A;       
 sfr16 CCU6_T12LH = 0xFA;          
 sfr16 CCU6_T12PRLH = 0x9C;        
 sfr16 CCU6_T13LH = 0xFC;          
 sfr16 CCU6_T13PRLH = 0x9E;        
 sfr16 T21_RC2LH = 0xC2;           
 sfr16 T21_T2LH = 0xC4;            
 sfr16 T2_RC2LH = 0xC2;            
 sfr16 T2_T2LH = 0xC4;             
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 

 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
  
#line 1 "C:\Keil\C51\Inc\intrins.h" /0






 
 
 
 
 
 extern void          _nop_     (void);
 extern bit           _testbit_ (bit);
 extern unsigned char _cror_    (unsigned char, unsigned char);
 extern unsigned int  _iror_    (unsigned int,  unsigned char);
 extern unsigned long _lror_    (unsigned long, unsigned char);
 extern unsigned char _crol_    (unsigned char, unsigned char);
 extern unsigned int  _irol_    (unsigned int,  unsigned char);
 extern unsigned long _lrol_    (unsigned long, unsigned char);
 extern unsigned char _chkfloat_(float);
 
 extern void          _push_    (unsigned char _sfr);
 extern void          _pop_     (unsigned char _sfr);
 
 
 
 
#line 614 "MAIN.H" /0
 
 
  
#line 1 "IO.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 sbit P0_0       = 0x80;    
 sbit P0_1       = 0x81;    
 sbit P0_2       = 0x82;    
 sbit P0_3       = 0x83;    
 sbit P0_4       = 0x84;    
 sbit P0_5       = 0x85;    
 sbit P0_7       = 0x87;    
 
 
 
 sbit P1_0       = 0x90;    
 sbit P1_1       = 0x91;    
 sbit P1_2       = 0x92;    
 sbit P1_3       = 0x93;    
 sbit P1_4       = 0x94;    
 sbit P1_5       = 0x95;    
 sbit P1_6       = 0x96;    
 sbit P1_7       = 0x97;    
 
 
 
 sbit P2_0       = 0xA0;    
 sbit P2_1       = 0xA1;    
 sbit P2_2       = 0xA2;    
 sbit P2_3       = 0xA3;    
 sbit P2_4       = 0xA4;    
 sbit P2_5       = 0xA5;    
 sbit P2_6       = 0xA6;    
 sbit P2_7       = 0xA7;    
 
 
 
 sbit P3_0       = 0xB0;    
 sbit P3_1       = 0xB1;    
 sbit P3_2       = 0xB2;    
 sbit P3_3       = 0xB3;    
 sbit P3_4       = 0xB4;    
 sbit P3_5       = 0xB5;    
 sbit P3_6       = 0xB6;    
 sbit P3_7       = 0xB7;    
 
 
 
 sbit P4_0       = 0xC8;    
 sbit P4_1       = 0xC9;    
 sbit P4_3       = 0xCB;    
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 void IO_vInit(void);
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
#line 616 "MAIN.H" /0
 
  
#line 1 "T2.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 void T2_vInit(void);
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
#line 617 "MAIN.H" /0
 
  
#line 1 "WDT.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 void WDT_vInit(void);
 void WDT_vDisable(void);
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 

 
 
 
 
 
 
 
 
 
 
 
 
#line 618 "MAIN.H" /0
 
  
#line 1 "SSC.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 void SSC_vInit(void);
 void SSC_vSendData(unsigned char Data);
  unsigned char SSC_vGetData(void);
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
#line 619 "MAIN.H" /0
 
  
#line 1 "CAN.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 typedef union ulValue {
  unsigned long ulVal;         
  unsigned char ubDB[4];       
 } un_32bit;
 
 
 
 
 
 
 
 
 
 
 
 
 
 typedef union uwValue {
  unsigned int uwVal;         
  unsigned char ubDB[2];       
 } un_16bit;
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 typedef struct
 {
  unsigned char ubMOdlc;        
  unsigned char ubMOcfg;        
 un_32bit ulID;        
 un_32bit ulMask;      
 un_32bit ulDATAL;     
 un_32bit ulDATAH;     
 un_16bit uwCounter;   
 }stCAN_SWObj;
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 void CAN_vInit(void);
 void CAN_vGetMsgObj(unsigned char ubObjNr, stCAN_SWObj *pstObj);
  unsigned char CAN_ubRequestMsgObj(unsigned char ubObjNr);
  unsigned char CAN_ubNewData(unsigned char ubObjNr);
 void CAN_vTransmit(unsigned char ubObjNr);
 void CAN_vLoadData(unsigned char ubObjNr, unsigned long *ulpubData);
 void CAN_vReleaseObj(unsigned char ubObjNr);
 void CAN_vWriteAMData(unsigned long ulValue);
 void CAN_vSetListCommand(unsigned long ulVal);
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 


 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 



 
 


 
 
 
 
 
 
 
 
 
 
 
 
 
 
#line 620 "MAIN.H" /0
 
  
#line 1 "SHARED_INT.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 void SHINT_vInit(void);
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
  
#line 1 "MAIN.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
#line 26 "MAIN.H" /1
  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
  
  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
  
  
  
  
 
 
  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
  
  
  
  
 
 
  
  
  
  
  
  
  
 
 
  
  
  
  
 
 
  
  
  
  
 
  
  
  
  
  
  
  
  
  
 
  
 
 
 
  
 
 
  
 
 
  
 
  

 
  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
  
 
  
  
  
  
  
  
 
 
 
 
 
 
 
 
  
 
 
 
 
#line 113 "SHARED_INT.H" /0
#line 113 "SHARED_INT.H" /0
 
 
 
 
 
 
 
 
 
#line 621 "MAIN.H" /0
 
 
 
 
 
 
 
 
 
  
#line 1 "Config.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 typedef union _SwSample
 {
 struct
 {
  unsigned char Arr[1][4];
 }ArrData;
 struct
 { 
 
 unsigned int  S6        : 1; 
 unsigned int  S5        : 1; 
 unsigned int  S4        : 1; 
 unsigned int  S3        : 1; 
 unsigned int  S1        : 1; 
 unsigned int  S2        : 1; 
 unsigned int            : 1; 
 unsigned int  S15       : 1; 
 unsigned int  S16       : 1; 
 unsigned int  S17       : 1; 
 unsigned int  S18       : 1; 
 unsigned int  S20       : 1; 
 unsigned int  S19       : 1; 
 unsigned int            : 1; 
 unsigned int  S10       : 1; 
 unsigned int  S9        : 1; 
 unsigned int  S8        : 1; 
 unsigned int  S7        : 1; 
 unsigned int  S14       : 1; 
 unsigned int  S13       : 1; 
 unsigned int  S12       : 1; 
 unsigned int  S11       : 1; 
 unsigned int            : 1; 
 unsigned int            : 1; 
 unsigned int            : 8; 
 
 }BitData;
 }UnSwSample,*pUnSwSample;
 
 
 typedef union _SwOut
 {
 struct
 {
  unsigned char Arr[1][4];	 
 }ArrData;
 struct
 { 
 
 unsigned int  S21  : 1; 
 unsigned int  S22  : 1; 
 unsigned int  S23  : 1; 
 unsigned int  S24  : 1; 
 unsigned int  S34  : 1; 
 unsigned int  S33  : 1; 
 unsigned int  S32  : 1; 
 unsigned int  S31  : 1; 
 unsigned int  S25  : 1; 
 unsigned int  S26  : 1; 
 unsigned int  S27  : 1; 
 unsigned int  S28  : 1; 
 unsigned int  S30  : 1; 
 unsigned int  S29  : 1; 
 unsigned int       : 1; 
 unsigned int  S35  : 1; 
 unsigned int  S36  : 1; 
 unsigned int  S37  : 1; 
 unsigned int  S38  : 1; 
 unsigned int  S40  : 1; 
 unsigned int  S39  : 1; 
 unsigned int       : 1; 
 unsigned int           : 1;	
 unsigned int           : 1;	  
 unsigned int           : 8;
 
 }BitData;
 }UnSwOut,*pUnSwOut;
 
 
 
 
 
 
 
 
 
 
 typedef union _InfCan
 {
 struct
 {
  unsigned char Arr[1][8];
  unsigned char ArrRX[1][8];
 }ArrData;
 struct
 {
 
 unsigned int	 S17        : 1  ;  
 unsigned int	 S18        : 1  ;  
 unsigned int	 S19        : 1  ;  
 unsigned int	 S20        : 1  ;  
 unsigned int   S21        : 1  ;  
 unsigned int	 S22        : 1  ;  
 unsigned int	 S23        : 1  ;  
 unsigned int	 S24        : 1  ;  
 
 unsigned int	 S25        : 1  ;  
 unsigned int	 S26        : 1  ;  
 unsigned int	 S27        : 1  ;  
 unsigned int	 S28        : 1  ;  
 unsigned int	 S29        : 1  ;  
 unsigned int	 S30        : 1  ;  
 unsigned int	 S31        : 1  ;  
 unsigned int	 S32        : 1  ;  
 
 unsigned int   S1         : 1  ;  
 unsigned int	 S2         : 1  ;  
 unsigned int	 S3         : 1  ;  
 unsigned int	 S4         : 1  ;  
 unsigned int	 S5         : 1  ;  
 unsigned int	 S6         : 1  ;  
 unsigned int	 S7         : 1  ;  
 unsigned int	 S8         : 1  ;  
 
 unsigned int	 S9         : 1  ;  
 unsigned int	 S10        : 1  ;  
 unsigned int	 S11        : 1  ;  
 unsigned int	 S12        : 1  ;  
 unsigned int	 S13        : 1  ;  
 unsigned int	 S14        : 1  ;  
 unsigned int	 S15        : 1  ;  
 unsigned int	 S16        : 1  ;  
 
 
 unsigned int   CntTx           : 16  ;   
 
 unsigned int	 S33        : 1  ;  
 unsigned int	 S34        : 1  ;  
 unsigned int	 S35        : 1  ;  
 unsigned int	 S36        : 1  ;  
 unsigned int	 S37        : 1  ;  
 unsigned int	 S38        : 1  ;  
 unsigned int	 S39        : 1  ;  
 unsigned int	 S40        : 1  ;  
 
 unsigned int   PasswordTx      : 8  ;   
 
 
 unsigned int	 D29        : 1  ;  
 unsigned int	 D30        : 1  ;  
 unsigned int	 D31        : 1  ;  
 unsigned int	 D32        : 1  ;  
 unsigned int	 D33        : 1  ;  
 unsigned int	 D34        : 1  ;  
 unsigned int	 D35        : 1  ;  
 unsigned int	 D36        : 1  ;  
 
 unsigned int   D21        : 1  ;  
 unsigned int	 D22        : 1  ;  
 unsigned int	 D23        : 1  ;  
 unsigned int	 D24        : 1  ;  
 unsigned int	 D25        : 1  ;  
 unsigned int	 D26        : 1  ;  
 unsigned int	 D27        : 1  ;  
 unsigned int	 D28        : 1  ;  
 
 unsigned int   D1         : 1  ;  
 unsigned int	 D2         : 1  ;  
 unsigned int	 D3         : 1  ;  
 unsigned int	 D4         : 1  ;  
 unsigned int	 D5         : 1  ;  
 unsigned int	 D6         : 1  ;  
 unsigned int	 D7         : 1  ;  
 unsigned int	 D8         : 1  ;  
 
 unsigned int	 D37        : 1  ;  
 unsigned int	 D38        : 1  ;  
 unsigned int	 D39        : 1  ;  
 unsigned int	 D40        : 1  ;  
 unsigned int	 D17        : 1  ;  
 unsigned int	 D18        : 1  ;  
 unsigned int	 D19        : 1  ;  
 unsigned int	 D20        : 1  ;  
 
 unsigned int   ConfigRx       : 8  ;   
 unsigned int   CntRx          : 8  ;   
 
 unsigned int	 D9         : 1  ;  
 unsigned int	 D10        : 1  ;  
 unsigned int	 D11        : 1  ;  
 unsigned int	 D12        : 1  ;  
 unsigned int	 D13        : 1  ;  
 unsigned int	 D14        : 1  ;  
 unsigned int	 D15        : 1  ;  
 unsigned int	 D16        : 1  ;  
 
 unsigned int   PasswordRx     : 8  ;   
 }BitData;       
 }UnInfCan,*pInfCan;
 
 
 
 
 
 
 
 
 
  
#line 1 "SwDriver.H" /0
 
 
 
 
 
 
 
 void SwStaSample(UnSwSample *ptmp);
 void InitSW(void);
 void LedLight(UnSwOut *tmpUn);
 
 
#line 237 "Config.H" /0
 
  
#line 1 "user.H" /0
 
 
 
 
 
 
 
 
 extern  UnSwOut	 UnSwOut_1;
 extern  UnInfCan UnInfCan_1;
 extern  unsigned int    CntCan_1;
 extern  bit      FlgCan_1;
 
 void CanRXTX(void);
 void SwSample(void);
 void LedDr(void);
 void RTCProcess(void);
 void CanErrorProcess(void);
 
 
#line 238 "Config.H" /0
 
  
#line 1 "boot.H" /0
 
 
  
#line 1 "XC88x_FLADDR.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
#line 48 "XC88x_FLADDR.H" /1
  
 
#line 50 "XC88x_FLADDR.H" /0
 
 
 
 
 
 
 
 
 
#line 59 "XC88x_FLADDR.H" /1
  
 
#line 61 "XC88x_FLADDR.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
#line 3 "boot.H" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 void BootMain(void);
 void CAN_waitTransmit(unsigned char RgMsgobj);
 
 
 
 
 
 
 
 extern unsigned char idata WLBuf[32];
 
 
 bit ProgWL(unsigned char code *AdrBnkXSecYWLZ);
 
 void LoadXD2WLBuf(unsigned char xdata *address);
 
 
 void LoadConst2WLBuf(unsigned char code *address);
 
 void LoadConst2XD(unsigned char xdata *dstaddress, unsigned char code *srcaddress, unsigned char length);
 
 unsigned char ReadConst(unsigned char code *address);
 
 
 
 
 extern bit PFlErase (unsigned char Bank0Sector, unsigned char Bank1Sector, unsigned char Bank2Sector);
 extern bit DFlErase (unsigned int DFlash0Sector, unsigned int DFlash1Sector);
 
 
 
 extern bit FlProg(unsigned char idata *SrcBuf);
 
 
 
 
 extern bit FlReady(unsigned char Bank);
 
 
 
 extern bit FlEraseAbort(void);
 
 
 
 extern bit FlProtect(unsigned char Password);
 
 extern unsigned char _FlReadByte(void);
 extern void FlExecute(unsigned char AddrH, unsigned char AddrL);
 
 sfr MEM_DPH    = 0x83;    
 sfr MEM_DPL    = 0x82;
 sfr MEM_NMICON = 0xBB;
 sfr MEM_NMISR  = 0xBC;
 
 
 
 
 
#line 239 "Config.H" /0
 
 
#line 630 "MAIN.H" /0
 
 
 
 
 
#line 29 "IO.C" /0
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 void IO_vInit(void)
 {
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 P1_DIR        =  0x0C;          
 P1_DATA       =  0x0C;          
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 }  
 
 
 
 
 
 
 
 

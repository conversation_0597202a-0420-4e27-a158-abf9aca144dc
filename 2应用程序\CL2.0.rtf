{\rtf1\ansi\deff0\deftab720{\fonttbl{\f0\fnil MS Sans Serif;}{\f1\fnil\fcharset2 Symbol;}{\f2\fswiss\fprq2 System;}{\f3\fnil Times New Roman;}{\f4\fswiss\fprq2 Arial;}}
{\colortbl\red0\green0\blue0;\red0\green0\blue128;\red255\green0\blue0;}
\deflang1031\pard\plain\f4\fs28\cf0 DAvE's Project Documentation
\par \plain\f4\fs22\cf0
\par \plain\f4\fs22\cf0 Project: \tab\tab\b CL2.0.dav
\par 
\par \plain\f4\fs22\cf0 Controller: \tab\tab\b XC886CLM-8FF
\par \plain\f4\fs22\cf0 Compiler: \tab\tab\b Keil
\par \plain\f4\fs22\cf0 Memory Model: \tab\b COMPACT
\par 
\par \plain\f4\fs22\cf0 Date: \tab\tab\tab\b 2013-7-5 07:00:10
\par 
\par 
\par \plain\f4\fs22\cf2\b Please read this document carefully and note
\par \plain\f4\fs22\cf2\b the red-colored hints.
\par 
\par \plain\f4\fs22\cf2\b If you miss a file in the generated files list
\par \plain\f4\fs22\cf2\b maybe you have forgotten to select the
\par \plain\f4\fs22\cf2\b initialisation function of the related module.
\par 
\par \plain\f4\fs22\cf0 Generated Files:
\plain\f4\fs20\cf0\b
\par \tab\tab\tab MAIN.H
\par \tab\tab\tab MAIN.C
\par \tab\tab\tab START_XC.A51
\par \tab\tab\tab SHARED_INT.H
\par \tab\tab\tab SHARED_INT.C
\par \tab\tab\tab IO.H
\par \tab\tab\tab IO.C
\par \tab\tab\tab T2.H
\par \tab\tab\tab T2.C
\par \tab\tab\tab WDT.H
\par \tab\tab\tab WDT.C
\par \tab\tab\tab SSC.H
\par \tab\tab\tab SSC.C
\par \tab\tab\tab CAN.H
\par \tab\tab\tab CAN.C
\par \tab\tab\tab CL2.0.ASM
\par 
\par 
\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul Project Settings
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void MAIN_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function initializes the microcontroller. It is

\par \tab \tab assumed that the SFRs are in their reset state.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void main(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the main function.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab Configuration of the System Clock:\par 
\tab - External Osc is selected (configuration is done in the startup file\par 
\tab 'START_XC.A51')\par 
\tab - PLL Mode, NDIV =  24\par 
\tab - input frequency is 8 MHz\par 
\par 
\tab *********************************************************************************\par 
\tab Note : All peripheral related IO configurations are done in the\par 
\tab respective peripheral modules (alternate functions selection)\par 
\tab *********************************************************************************\par 
\par 
\tab Initialization of module 'GPIO'\par 
\par 
\tab Initialization of module 'Timer 2'\par 
\par 
\tab Initialization of module 'Watch Dog Timer'\par 
\par 
\tab Initialization of module 'High Speed Synchronous Serial Interface(SSC)'\par 
\par 
\tab Initialization of module 'MultiCAN Controller '\par 
\par 
\tab Initialization of  'Shared interrupts'\par 
\par 
\tab \cf2Interrupt structure 2 mode 0 is selected.\cf0\par 
\par 
\tab \cf2Interrupt service routine choice 2 is selected.\cf0\par 
\par 

\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul Shared Interrupt Routines
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void SHINT_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function initializes the shared interrupts.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void SHINT_viXINTR5Isr(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the service routine for the shared interrupt node

\par \tab \tab XINTR5. XINTR5 ISR Node is shared by Timer 2, UART

\par \tab \tab Fractional Divider(BRG Interrupt),MultiCAN Node 0 and LIN.

\par \tab \tab Depending on the selected module interrupt it is called.

\par \tab \tab T2 - Depending on the selected operating mode it is called

\par \tab \tab when TF2 is set by an overflow or underflow of the timer 2

\par \tab \tab register or when EXF2 is set by a negative transition on

\par \tab \tab T2EX.

\par \tab \tab UART - It is called after the BRG timer overflows and sets

\par \tab \tab the NDOV bit.

\par \tab \tab CAN -  It is called for the  Service Request Node 0 of the

\par \tab \tab MultiCAN module.

\par \tab \tab Please note that you have to add application specific code

\par \tab \tab to this function.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b none\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab Configuration of the Shared  Interrupts:\par 
\tab - Timer 2 Interrupt is Selected\par 
\par 

\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul GPIO
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void IO_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the initialization function of the IO function

\par \tab \tab library. It is assumed that the SFRs used by this library

\par \tab \tab are in their reset state.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab ***********************************************************************\par 
\tab Note : All peripheral related IO configurations are done in the\par 
\tab respective peripheral modules (alternate functions selection)\par 
\tab \par 
\tab If no pins are selected DAvE assumes that registers are in default\par 
\tab settings\par 
\tab ***********************************************************************\par 
\par 
\tab Configuration of Port P0:\par 
\tab P0.3:\par 
\tab - is used as alternate output for the SSC Clock Output\par 
\tab - push/pull output is selected\par 
\tab - pull device is disabled (tristate)\par 
\tab P0.4:\par 
\tab - is used as alternate output for the SSC Master Transmit Output\par 
\tab - push/pull output is selected\par 
\tab - pull device is disabled (tristate)\par 
\tab P0.5:\par 
\tab - is used as alternate input for the SSC Master Receive Input\par 
\tab - pull device is disabled (tristate)\par 
\par 
\tab Configuration of Port P1:\par 
\tab P1.0:\par 
\tab - is used as alternate input for the MCAN Node 0 Receiver Input\par 
\tab - pull-up device is assigned\par 
\tab P1.1:\par 
\tab - is used as alternate output for the MCAN Node 0 Transmitter Output\par 
\tab - push/pull output is selected\par 
\tab - pull-up device is assigned\par 
\tab P1.2:\par 
\tab - is used as general purpose output\par 
\tab - push/pull output is selected\par 
\tab - the pin status is high level\par 
\tab - pull-up device is assigned\par 
\tab P1.3:\par 
\tab - is used as general purpose output\par 
\tab - push/pull output is selected\par 
\tab - the pin status is high level\par 
\tab - pull-up device is assigned\par 
\par 
\tab Configuration of Port P2:\par 
\tab - no pin of port P2 is used\par 
\par 
\tab Configuration of Port P3:\par 
\tab - no pin of port P3 is used\par 
\par 
\tab Configuration of Port P4:\par 
\tab - no pin of port P4 is used\par 
\par 

\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul Timer 2
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void T2_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the initialization function of the Timer 2 function

\par \tab \tab library. It is assumed that the SFRs used by this library

\par \tab \tab are in their reset state.

\par \tab \tab The following SFRs and SFR fields will be initialized:

\par \tab \tab T2_RC2H/RC2L     - reload/capture timer 2 register

\par \tab \tab T2_T2H/T2L     -  timer 2 register

\par \tab \tab ET2           - timer 2 interrupt enable

\par \tab \tab T2_T2MOD         - timer 2 mode register

\par \tab \tab CP/RL2        - Capture/Reload select

\par \tab \tab EXEN2         - External enable control

\par \tab \tab TR2           - Timer2 run control

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab 16-bit timer function with automatic reload when timer 2 overflows\par 
\tab the timer 2 resolution is .5 us\par 
\tab the timer 2 overflow is 1000.000 us\par 
\tab timer 2 interrupt: enabled\par 
\tab timer 2 will be started\par 
\par 
\tab timer 2 Interrupt enable bit is set in SHINT_vInit() function\par 
\par 

\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul Watch Dog Timer
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab WDT_vRefresh()\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This macro refreshes the watchdog timer to the reload

\par \tab \tab value. The interrupts are disabled during execution of the

\par \tab \tab instructions that set bit WDTRS.

\par \tab \tab Note: The user has to take care that this macro is called

\par \tab \tab before expiry of the watchdog timer.

\par \tab \tab pls refer to the Note on WDT_vInit function Description

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void WDT_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the initialization function of the 'Watch Dog

\par \tab \tab Timer' function library. It is assumed that the SFRs used

\par \tab \tab by this library are in their reset state.

\par \tab \tab The watchdog timer is used.

\par \tab \tab The following SFR fields will be initialized:

\par \tab \tab WDTREL - WDT reload value

\par \tab \tab WDTIN  - WDT input frequency selection

\par \tab \tab Then the watchdog timer will be refreshed.

\par \tab \tab Note: The user has to take care that RMAP bit in SYSCON0 is

\par \tab \tab to be set while using the registers of Watch dog timer.

\par \tab \tab Registers are WDTCON,WDTWINB,WDTREL,WDTH,WDTL

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void WDT_vDisable(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function disables the WDT.

\par \tab \tab pls refer to the Note on WDT_vInit function Description

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab the watchdog timer is used and it is enabled in INIT function,user has\par 
\tab to take care of WDT refresh\par 
\tab the RMAP bit of SYSCON0 is set\par 
\tab the input frequency is fSYS/128\par 
\tab the watchdog timer reload value is 0xDB\par 
\tab the watchdog timeout period is 50 ms\par 
\par 

\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul High Speed Synchronous Serial Interface(SSC)
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void SSC_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the initialization function of the SSC function

\par \tab \tab library. It is assumed that the SFRs used by this library

\par \tab \tab are in their reset state.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void SSC_vSendData(ubyte Data)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab The master device can initiate the first data transfer by

\par \tab \tab writing the transmit data into transmit buffer. This value

\par \tab \tab is copied into the shift register (which is assumed to be

\par \tab \tab empty at this time), and the selected first bit of the

\par \tab \tab transmit data is placed onto the MTSR line on the next

\par \tab \tab clock from the baud rate generator.

\par \tab \tab A slave device immediately outputs the selected first bit

\par \tab \tab (MSB or LSB of the transfer data) at pin MRST, when the

\par \tab \tab contents of the transmit buffer are copied into the slave's

\par \tab \tab shift register.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab Data:

\par \tab \tab Data to be send

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab ubyte SSC_vGetData(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function returns the contents of the receive buffer.

\par \tab \tab When the receive interrupt request flag is set this implies

\par \tab \tab that data is available for reading in the receive buffer.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b Received data\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab - Port Selection\par 
\par 
\tab Configuration of the used SSC Port Pins:\par 
\tab Pin SCLK (P0.3) is selected for SCLK ouput\par 
\tab Pin MTSR_1 (P0.4) is selected for Master Transmit Output\par 
\tab Pin MRST_1 (P0.5) is selected for Master Receive Input\par 
\par 
\tab Pin SCLK (P1.2) is Not selected for SCLK ouput\par 
\tab Pin MTSR_0 (P1.3) is Not selected for Master Transmit Output\par 
\tab Pin MRST_0 (P1.4) Not is selected for Master Receive Input\par 
\par 
\tab Configuration of the SSC Baud Rate Generator:\par 
\tab - required baud rate = 2000.000 kbaud\par 
\tab - real baud rate     = 2000.000 kbaud\par 
\tab - deviation          = 0.000 %\par 
\par 
\tab Configuration of the SSC Operation Mode:\par 
\tab - this device is configured as SSC master\par 
\tab - transfer data width is 8 bit\par 
\tab - transfer/receive MSB first\par 
\tab - shift transmit data on the leading clock edge, latch on trailing edge\par 
\tab - idle clock line is low, leading clock edge is low-to-high transition\par 
\tab - ignore receive error\par 
\tab - ignore phase error\par 
\par 
\tab - SSC interrupt is disabled\par 
\par 

\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul MultiCAN Controller
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_vReadEN()\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This macro enables Read mode (CAN Address/Data Control

\par \tab \tab Register).

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_vWriteEN(ubyte ubDCtrl)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This macro enables Write mode (CAN Address/Data Control

\par \tab \tab Register).

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubDCtrl:

\par \tab \tab Data Control Flags

\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_vWriteCANAddress(uword uwAdr)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This macro writes 16-bit CAN address to CAN Address

\par \tab \tab Register Low and High respectively.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab uwAdr:

\par \tab \tab 16-bit Address

\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_vWriteCANData(ulong ulValue)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This macro writes 32-bit Data to CAN Data Register's

\par \tab \tab 0-3respectively.

\par \tab \tab Note:

\par \tab \tab Write Process :

\par \tab \tab ->Write the address of the MultiCAN kernel register to the

\par \tab \tab CAN_ADL and CAN_ADH registers.

\par \tab \tab use macro : CAN_vWriteCANAddress.

\par \tab \tab ->Write the data to the

\par \tab \tab CAN_DATA0/CAN_DATA1/CAN_DATA2/CAN_DATA3 registers.

\par \tab \tab ->Write the register CAN_ADCON, including setting the valid

\par \tab \tab bit of the data registers and setting register bit RWEN to

\par \tab \tab 1.

\par \tab \tab ->The valid data will be written to the MultiCAN kernel

\par \tab \tab only once. Register bit BSY will become 1.

\par \tab \tab ->When Register bit BSY becomes 0, the transmission is

\par \tab \tab finished.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ulValue:

\par \tab \tab 32-bit Data

\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_pushAMRegs/_popAMRegs()\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab The macro CAN_pushAMRegs() PUSH the CAN Access Mediator

\par \tab \tab Registers.

\par \tab \tab The macro CAN_popAMRegs() POP the CAN Access Mediator

\par \tab \tab Registers.

\par \tab \tab Note:

\par \tab \tab This macro is used in CAN ISR/Function's to protect Access

\par \tab \tab Mediator Register

\par \tab \tab MultiCAN Access Mediator Registers:

\par \tab \tab ADL, ADH, DATA0, DATA1, DATA2, DATA3.

\par \tab \tab The _push_/_pop_ routine inserts a PUSH/POP instruction

\par \tab \tab into the program saving the contents of the Special

\par \tab \tab Function Register(sfr) on the Stack.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the initialization function of the CAN function

\par \tab \tab library. It is assumed that the SFRs used by this library

\par \tab \tab are in their reset state.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vWriteAMData(ulong ulValue)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function writes 32-bit Data to CAN Data Register's

\par \tab \tab 0-3respectively.

\par \tab \tab Note:

\par \tab \tab Write Process :

\par \tab \tab ->Write the address of the MultiCAN kernel register to the

\par \tab \tab CAN_ADL and CAN_ADH registers.

\par \tab \tab use macro : CAN_vWriteCANAddress.

\par \tab \tab ->Write the data to the

\par \tab \tab CAN_DATA0/CAN_DATA1/CAN_DATA2/CAN_DATA3 registers.

\par \tab \tab ->Write the register CAN_ADCON, including setting the valid

\par \tab \tab bit of the data registers and setting register bit RWEN to

\par \tab \tab 1.

\par \tab \tab ->The valid data will be written to the MultiCAN kernel

\par \tab \tab only once. Register bit BSY will become 1.

\par \tab \tab ->When Register bit BSY becomes 0, the transmission is

\par \tab \tab finished.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ulValue:

\par \tab \tab 32-bit Data

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vSetListCommand(ulong ulVal)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function write's 32-bit Data to CAN_PANCTR Register.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ulVal:

\par \tab \tab 32-bit Data

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vGetMsgObj(ubyte ubObjNr, stCAN_SWObj *pstObj)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function fills the forwarded SW message object with

\par \tab \tab the content of the chosen HW message object.

\par \tab \tab The structure of the SW message object is defined in the

\par \tab \tab header file CAN.H (see stCAN_SWObj).

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubObjNr:

\par \tab \tab Number of the message object to be read (0-31)

\par \tab \tab *pstObj:

\par \tab \tab Pointer on a message object to be filled by this function

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab ubyte CAN_ubRequestMsgObj(ubyte ubObjNr)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab If a TRANSMIT OBJECT is to be reconfigured it must first be

\par \tab \tab accessed. The access to the transmit object is exclusive.

\par \tab \tab This function checks whether the choosen message object is

\par \tab \tab still executing a transmit request, or if the object can be

\par \tab \tab accessed exclusively.

\par \tab \tab After the message object is reserved, it can be

\par \tab \tab reconfigured by using the function CAN_vConfigMsgObj or

\par \tab \tab CAN_vLoadData.

\par \tab \tab Both functions enable access to the object for the CAN

\par \tab \tab controller.

\par \tab \tab By calling the function CAN_vTransmit transfering of data

\par \tab \tab is started.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b 0 message object is busy (a transfer is active), else 1\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubObjNr:

\par \tab \tab Number of the message object (0-31)

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab ubyte CAN_ubNewData(ubyte ubObjNr)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function checks whether the selected RECEIVE OBJECT

\par \tab \tab has received a new message. If so the function returns the

\par \tab \tab value '1'.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b 1 the message object has received a new message, else 0\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubObjNr:

\par \tab \tab Number of the message object (0-31)

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vTransmit(ubyte ubObjNr)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function triggers the CAN controller to send the

\par \tab \tab selected message.

\par \tab \tab If the selected message object is a TRANSMIT OBJECT then

\par \tab \tab this function triggers the sending of a data frame. If

\par \tab \tab however the selected message object is a RECEIVE OBJECT

\par \tab \tab this function triggers the sending of a remote frame.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubObjNr:

\par \tab \tab Number of the message object (0-31)

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vLoadData(ubyte ubObjNr, ulong *ulpubData)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab If a hardware TRANSMIT OBJECT has to be loaded with data

\par \tab \tab but not with a new identifier, this function may be used

\par \tab \tab instead of the function CAN_vConfigMsgObj. The message

\par \tab \tab object should be accessed by calling the function

\par \tab \tab CAN_ubRequestMsgObj before calling this function. This

\par \tab \tab prevents the CAN controller from working with invalid data.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubObjNr:

\par \tab \tab Number of the message object to be configured (0-31)

\par \tab \tab *ulpubData:

\par \tab \tab Pointer on a data buffer

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vReleaseObj(ubyte ubObjNr)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function resets the NEWDAT flag of the selected

\par \tab \tab RECEIVE OBJECT, so that the CAN controller have access to

\par \tab \tab it. This function must be called if the function

\par \tab \tab CAN_ubNewData detects, that new data are present in the

\par \tab \tab message object and the actual data have been read by

\par \tab \tab calling the function CAN_vGetMsgObj.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubObjNr:

\par \tab \tab Number of the message object (0-31)

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab Configuration of the Module Clock:\par 
\tab - the CAN module clock = 48.00 MHz\par 
\tab - FCLK runs at 2 times the frequency of PCLK.\par 
\par 
\tab - CMCON - Clock Control Register is Configured in MAIN_vInit\par 
\par 
\tab Configuration of CAN Node 0:\par 
\par 
\tab General Configuration of the Node 0:\par 
\tab - set INIT and CCE\par 
\par 
\tab - load NODE 0 interrupt pointer register\par 
\par 
\tab Configuration of the used CAN Input Port Pins:\par 
\tab - Pin P1.0 is used as RXDC0_0 input\par 
\par 
\tab - Loop-back mode is disabled\par 
\par 
\tab Configuration of the Node 0 Baud Rate:\par 
\tab - required baud rate = 100.000 kbaud\par 
\tab - real baud rate     = 100.000 kbaud\par 
\tab - sample point       = 60.00 %\par 
\tab - there are 5 time quanta before sample point\par 
\tab - there are 4 time quanta after sample point\par 
\tab - the (re)synchronization jump width is 2 time quanta\par 
\par 
\tab Configuration of the Node 0 Error Counter:\par 
\tab - the error warning threshold value (warning level) is 96\par 
\par 
\tab Configuration of the Frame Counter:\par 
\tab - Frame Counter Mode: the counter is incremented upon the reception\par 
\tab and transmission of frames\par 
\tab - frame counter: 0x0000\par 
\par 
\tab Configuration of CAN Node 1:\par 
\par 
\tab General Configuration of the Node 1:\par 
\tab - set INIT and CCE\par 
\par 
\tab Configuration of the used CAN Output Port Pins:\par 
\tab Pin P1.1 is used as TXDC0_0 Output\par 
\tab - NODE1 TXD Pin's are not used\par 
\par 
\tab Configuration of the CAN Message Object List Structure:\par 
\par 
\tab Allocate MOs for list 1:\par 
\par 
\tab Configuration of the CAN Message Objects 0 - 31:\par 
\tab Configuration of Message Object 0:\par 
\tab - message object 0 is not valid\par 
\tab Configuration of Message Object 1:\par 
\tab - message object 1 is not valid\par 
\tab Configuration of Message Object 2:\par 
\tab - message object 2 is not valid\par 
\tab Configuration of Message Object 3:\par 
\tab - message object 3 is not valid\par 
\tab Configuration of Message Object 4:\par 
\tab - message object 4 is not valid\par 
\tab Configuration of Message Object 5:\par 
\tab - message object 5 is valid\par 
\tab - message object is used as receive object\par 
\tab - this message object is assigned to list 1 (node 0)\par 
\par 
\tab - priority class 3; transmit acceptance filtering is based on the list\par 
\tab order (like class 1)\par 
\tab - extended 29-bit identifier\par 
\tab - identifier 29-bit:      0x00000003\par 
\par 
\tab - only accept receive frames with matching IDE bit\par 
\tab - acceptance mask 29-bit: 0x1FFFFFFF\par 
\par 
\tab - use message pending register 0 bit position 5\par 
\par 
\tab - this object is a STANDARD MESSAGE OBJECT\par 
\tab - 8 valid data bytes\par 
\par 
\tab Configuration of Message Object 6:\par 
\tab - message object 6 is valid\par 
\tab - message object is used as transmit object\par 
\tab - this message object is assigned to list 1 (node 0)\par 
\par 
\tab - priority class 3; transmit acceptance filtering is based on the list\par 
\tab order (like class 1)\par 
\tab - extended 29-bit identifier\par 
\tab - identifier 29-bit:      0x10000001\par 
\par 
\tab - only accept receive frames with matching IDE bit\par 
\tab - acceptance mask 29-bit: 0x1FFFFFFF\par 
\par 
\tab - use message pending register 0 bit position 6\par 
\par 
\tab - this object is a STANDARD MESSAGE OBJECT\par 
\tab - 8 valid data bytes\par 
\par 
\tab Configuration of Message Object 7:\par 
\tab - message object 7 is valid\par 
\tab - message object is used as receive object\par 
\tab - this message object is assigned to list 1 (node 0)\par 
\par 
\tab - priority class 3; transmit acceptance filtering is based on the list\par 
\tab order (like class 1)\par 
\tab - extended 29-bit identifier\par 
\tab - identifier 29-bit:      0x10000002\par 
\par 
\tab - only accept receive frames with matching IDE bit\par 
\tab - acceptance mask 29-bit: 0x1FFFFFFF\par 
\par 
\tab - use message pending register 0 bit position 7\par 
\par 
\tab - this object is a STANDARD MESSAGE OBJECT\par 
\tab - 8 valid data bytes\par 
\par 
\tab Configuration of Message Object 8:\par 
\tab - message object 8 is not valid\par 
\tab Configuration of Message Object 9:\par 
\tab - message object 9 is valid\par 
\tab - message object is used as transmit object\par 
\tab - this message object is assigned to list 1 (node 0)\par 
\par 
\tab - priority class 3; transmit acceptance filtering is based on the list\par 
\tab order (like class 1)\par 
\tab - extended 29-bit identifier\par 
\tab - identifier 29-bit:      0x1FFFFFF0\par 
\par 
\tab - only accept receive frames with matching IDE bit\par 
\tab - acceptance mask 29-bit: 0x1FFFFFFF\par 
\par 
\tab - use message pending register 0 bit position 9\par 
\par 
\tab - this object is a STANDARD MESSAGE OBJECT\par 
\tab - 8 valid data bytes\par 
\par 
\tab Configuration of Message Object 10:\par 
\tab - message object 10 is not valid\par 
\tab Configuration of Message Object 11:\par 
\tab - message object 11 is not valid\par 
\tab Configuration of Message Object 12:\par 
\tab - message object 12 is not valid\par 
\tab Configuration of Message Object 13:\par 
\tab - message object 13 is not valid\par 
\tab Configuration of Message Object 14:\par 
\tab - message object 14 is not valid\par 
\tab Configuration of Message Object 15:\par 
\tab - message object 15 is not valid\par 
\tab Configuration of Message Object 16:\par 
\tab - message object 16 is not valid\par 
\tab Configuration of Message Object 17:\par 
\tab - message object 17 is not valid\par 
\tab Configuration of Message Object 18:\par 
\tab - message object 18 is not valid\par 
\tab Configuration of Message Object 19:\par 
\tab - message object 19 is not valid\par 
\tab Configuration of Message Object 20:\par 
\tab - message object 20 is not valid\par 
\tab Configuration of Message Object 21:\par 
\tab - message object 21 is not valid\par 
\tab Configuration of Message Object 22:\par 
\tab - message object 22 is not valid\par 
\tab Configuration of Message Object 23:\par 
\tab - message object 23 is not valid\par 
\tab Configuration of Message Object 24:\par 
\tab - message object 24 is not valid\par 
\tab Configuration of Message Object 25:\par 
\tab - message object 25 is not valid\par 
\tab Configuration of Message Object 26:\par 
\tab - message object 26 is not valid\par 
\tab Configuration of Message Object 27:\par 
\tab - message object 27 is not valid\par 
\tab Configuration of Message Object 28:\par 
\tab - message object 28 is not valid\par 
\tab Configuration of Message Object 29:\par 
\tab - message object 29 is not valid\par 
\tab Configuration of Message Object 30:\par 
\tab - message object 30 is not valid\par 
\tab Configuration of Message Object 31:\par 
\tab - message object 31 is not valid\par 
\par 
\tab Configuration of the Interrupts:\par 
\tab - CAN interrupt node 0 is disabled\par 
\tab - CAN interrupt node 1 is disabled\par 
\tab - CAN interrupt node 2 is disabled\par 
\tab - CAN interrupt node 3 is disabled\par 
\tab - CAN interrupt node 4 is disabled\par 
\tab - CAN interrupt node 5 is disabled\par 
\tab - CAN interrupt node 6 is disabled\par 
\tab - CAN interrupt node 7 is disabled\par 
\par 

}

<PERSON>X51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE     1


MACRO ASSEMBLER AX51 V3.07d
OBJECT MODULE PLACED IN XC88x_FLHANDLER.OBJ
ASSEMBLER INVOKED BY: C:\Keil\C51\BIN\AX51.EXE XC88x_FLHANDLER.ASM SET(LARGE) DEBUG EP

LOC    OBJ             LINE     SOURCE

                          1     ;************************************************************************************
                          2     ;*  FILE        : XC88x_FLHANDLER.ASM                                               *
                          3     ;*  DESCRIPTION : Flash handler routine for XC88x AA and AB                             
                                  *
                          4     ;*  COPYRIGHT   : (c) 2006 Infineon Technologies AG                                 *
                          5     ;*  UPDATED     : 14/03/2006                                                        *
                          6     ;************************************************************************************
                          7     $nomod51 
                          8     $debug 
                          9     ;#include "XC88x_FLADDR.H" 
                    +1   10     
                    +1   11     
                    +1   12     
                    +1   13     
                    +1   14     
                    +1   15     
                    +1   16     
                    +1   17     
                    +1   18     
                    +1   19     
                    +1   20     
                    +1   21     
                    +1   22     
                    +1   23     
                    +1   24     
                    +1   25                                    
                    +1   26     
                    +1   27                                            
                    +1   28                                            
                    +1   29     
                    +1   30                                    
                    +1   31                                    
                    +1   32     
                    +1   33                                    
                    +1   34     
                    +1   35                                    
                    +1   36     
                    +1   37     
                    +1   38     
                    +1   39     
                    +1   40     
                    +1   41     
                    +1   42     
                    +1   43     
                    +1   44     
                    +1   45     
                    +1   46     
                    +1   47     
                    +1   48     
                    +1   49     
                    +1   50     
                    +1   51     
                    +1   52     
                    +1   53     
                    +1   54     
                    +1   55     
                    +1   56     
                    +1          
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE     2

                    +1          
                    +1   59     
                    +1   60     
                    +1   61     
                    +1   62     
                    +1   63     
                    +1   64     
                    +1   65     
                    +1   66     
                    +1   67     
                    +1   68     
                    +1          
                    +1          
                    +1          
                    +1   72     
                    +1   73     
                    +1   74     
                    +1   75     
                    +1   76     
                    +1   77     
                    +1   78     
                    +1   79     
                    +1   80     
                    +1   81     
                    +1   82     
                    +1   83     
                    +1   84     
                    +1   85     
                    +1   86     
                    +1   87     
                    +1   88     
                    +1   89     
                    +1   90     
                    +1   91     
                    +1   92     
                    +1   93     
                    +1   94     
                    +1   95     
                    +1   96     
                    +1   97     
                    +1   98     
                    +1   99     
                    +1  100     
                    +1  101     
                    +1  102     
                    +1  103     
                    +1  104     
                    +1  105     
                    +1  106     
                    +1  107     
                    +1  108     
                    +1  109     
                    +1  110     
                    +1  111     
                    +1  112     
                    +1  113     
                    +1  114     
                    +1  115     
                    +1  116     
                    +1  117     
                    +1  118     
                    +1  119     
                    +1  120     
                    +1  121     
                    +1  122     
                    +1  123     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE     3

                    +1  124     
                    +1  125     
                    +1  126     
                    +1  127      
                    +1  128     
                    +1  129     
                    +1  130     
                    +1  131     
                    +1  132     
                    +1  133     
                    +1  134     
                    +1  135     
                    +1  136     
                    +1  137     
                    +1  138     
                    +1  139     
                    +1  140     
                    +1  141     
                    +1  142     
                    +1  143     
                    +1  144     
                    +1  145     
                    +1  146     
                    +1  147     
                    +1  148     
                    +1  149     
                    +1  150     
                    +1  151     
                    +1  152      
                    +1  153     
                    +1  154     
                    +1  155     
                    +1  156     
                    +1  157     
                    +1  158     
                    +1  159     
                    +1  160     
                    +1  161     
                    +1  162     
                    +1  163     
                    +1  164     
                    +1  165     
                    +1  166     
                    +1  167     
                    +1  168     
                    +1  169     
                    +1  170     
                    +1  171     
                    +1  172     
                    +1  173     
                    +1  174     
                    +1  175     
                    +1  176     
                    +1  177     
                    +1  178     
                    +1  179     
                    +1  180     
                    +1  181     
                    +1  182     
                    +1  183     
                    +1  184     
                    +1  185     
                    +1  186     
                    +1  187     
                    +1  188     
                    +1  189     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE     4

                    +1  190     
                    +1  191     
                    +1  192     
                    +1  193     
                    +1  194     
                    +1  195     
                    +1  196     
                    +1  197     
                    +1  198     
                    +1  199     
                    +1  200     
                    +1  201     
                    +1  202     
                    +1  203     
                    +1  204     
                    +1  205     
                    +1  206     
                    +1  207     
                    +1  208     
                    +1  209     
                    +1  210     
                    +1  211     
                    +1  212     
                    +1  213     
                    +1  214     
                    +1  215     
                    +1  216     
                    +1  217     
                    +1  218     
                    +1  219     
                    +1  220     
                    +1  221     
                    +1  222     
                    +1  223     
                    +1  224     
                    +1  225     
                    +1  226     
                    +1  227     
                    +1  228     
                    +1  229     
                    +1  230     
                    +1  231     
                    +1  232     
                    +1  233     
                    +1  234     
                    +1  235     
                    +1  236     
                    +1  237     
                    +1  238     
                    +1  239     
                    +1  240     
                    +1  241     
                    +1  242     
                    +1  243     
                    +1  244     
                    +1  245     
                    +1  246     
                    +1  247     
                    +1  248     
                    +1  249     
                    +1  250     
                    +1  251     
                    +1  252     
                    +1  253     
                    +1  254     
                    +1  255     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE     5

                    +1  256     
                    +1  257     
                    +1  258     
                    +1  259     
                    +1  260     
                    +1  261     
                    +1  262     
                    +1  263     
                    +1  264     
                    +1  265     
                    +1  266     
                    +1  267     
                    +1  268     
                    +1  269     
                    +1  270     
                    +1  271     
                    +1  272     
                    +1  273     
                    +1  274     
                    +1  275     
                    +1  276     
                    +1  277     
                    +1  278     
                    +1  279     
                    +1  280     
                    +1  281     
                    +1  282     
                    +1  283     
                    +1  284     
                    +1  285     
                    +1  286     
                    +1  287     
                    +1  288     
                    +1  289     
                    +1  290     
                    +1  291     
                    +1  292     
                    +1  293     
                    +1  294     
                    +1  295     
                    +1  296     
                    +1  297     
                    +1  298     
                    +1  299     
                    +1  300     
                    +1  301     
                    +1  302     
                    +1  303     
                    +1  304     
                    +1  305     
                    +1  306     
                    +1  307     
                    +1  308     
                    +1  309     
                    +1  310     
                    +1  311     
                    +1  312     
                    +1  313     
                    +1  314     
                    +1  315     
                    +1  316     
                    +1  317     
                    +1  318     
                    +1  319     
                    +1  320     
                    +1  321     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE     6

                    +1  322     
                    +1  323     
                    +1  324     
                    +1  325     
                    +1  326     
                    +1  327     
                    +1  328     
                    +1  329     
                    +1  330     
                    +1  331     
                    +1  332     
                    +1  333     
                    +1  334     
                    +1  335     
                    +1  336     
                    +1  337     
                    +1  338     
                    +1  339     
                    +1  340     
                    +1  341     
                    +1  342     
                    +1  343     
                    +1  344     
                    +1  345     
                    +1  346     
                    +1  347     
                    +1  348     
                    +1  349     
                    +1  350     
                    +1  351     
                    +1  352     
                    +1  353     
                    +1  354     
                    +1  355     
                    +1  356     
                    +1  357     
                    +1  358     
                    +1  359     
                    +1  360     
                    +1  361     
                    +1  362     
                    +1  363     
                    +1  364     
                    +1  365     
                    +1  366     
                    +1  367     
                    +1  368     
                    +1  369     
                    +1  370     
                    +1  371     
                    +1  372     
                    +1  373     
                    +1  374     
                    +1  375     
                    +1  376     
                    +1  377     
                    +1  378     
                    +1  379     
                    +1  380     
                    +1  381     
                    +1  382     
                    +1  383     
                    +1  384     
                    +1  385     
                    +1  386     
                    +1  387     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE     7

                    +1  388     
                    +1  389     
                    +1  390     
                    +1  391     
                    +1  392     
                    +1  393     
                    +1  394     
                    +1  395     
                    +1  396     
                    +1  397     
                    +1  398     
                    +1  399     
                    +1  400     
                    +1  401     
                    +1  402     
                    +1  403     
                    +1  404     
                    +1  405     
                    +1  406     
                    +1  407     
                    +1  408     
                    +1  409     
                    +1  410     
                    +1  411     
                    +1  412     
                    +1  413     
                    +1  414     
                    +1  415     
                    +1  416     
                    +1  417     
                    +1  418     
                    +1  419     
                    +1  420     
                    +1  421     
                    +1  422     
                    +1  423     
                    +1  424     
                    +1  425     
                    +1  426     
                    +1  427     
                    +1  428     
                    +1  429     
                    +1  430     
                    +1  431     
                    +1  432     
                    +1  433     
                    +1  434     
                    +1  435     
                    +1  436     
                    +1  437     
                    +1  438     
                    +1  439     
                    +1  440     
                    +1  441     
                    +1  442     
                    +1  443     
                    +1  444     
                    +1  445     
                    +1  446     
                    +1  447     
                    +1  448     
                    +1  449     
                    +1  450     
                    +1  451     
                    +1  452     
                    +1  453     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE     8

                    +1  454     
                    +1  455     
                    +1  456     
                    +1  457     
                    +1  458     
                    +1  459     
                    +1  460     
                    +1  461     
                    +1  462     
                    +1  463     
                    +1  464     
                    +1  465     
                    +1  466     
                    +1  467     
                    +1  468     
                    +1  469     
                    +1  470     
                    +1  471     
                    +1  472     
                    +1  473     
                    +1  474     
                    +1  475     
                    +1  476     
                    +1  477     
                    +1  478     
                    +1  479     
                    +1  480     
                    +1  481     
                    +1  482     
                    +1  483     
                    +1  484     
                    +1  485     
                    +1  486     
                    +1  487     
                    +1  488     
                    +1  489     
                    +1  490     
                    +1  491     
                    +1  492     
                    +1  493     
                    +1  494     
                    +1  495     
                    +1  496     
                    +1  497     
                    +1  498     
                    +1  499     
                    +1  500     
                    +1  501     
                    +1  502     
                    +1  503     
                    +1  504     
                    +1  505     
                    +1  506     
                    +1  507     
                    +1  508     
                    +1  509     
                    +1  510     
                    +1  511     
                    +1  512     
                    +1  513     
                    +1  514     
                    +1  515     
                    +1  516     
                    +1  517     
                    +1  518     
                    +1  519     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE     9

                    +1  520     
                    +1  521     
                    +1  522     
                    +1  523     
                    +1  524     
                    +1  525     
                    +1  526     
                    +1  527     
                    +1  528     
                    +1  529     
                    +1  530     
                    +1  531     
                    +1  532     
                    +1  533     
                    +1  534     
                    +1  535     
                    +1  536     
                    +1  537     
                    +1  538     
                    +1  539     
                    +1  540     
                    +1  541     
                    +1  542     
                    +1  543     
                    +1  544     
                    +1  545     
                    +1  546     
                    +1  547     
                    +1  548     
                    +1  549     
                    +1  550     
                    +1  551     
                    +1  552     
                    +1  553     
                    +1  554     
                    +1  555     
                    +1  556     
                    +1  557     
                    +1  558     
                    +1  559     
                    +1  560     
                    +1  561     
                    +1  562     
                    +1  563     
                    +1  564     
                    +1  565     
                    +1  566     
                    +1  567     
                    +1  568     
                    +1  569     
                    +1  570     
                    +1  571     
                    +1  572     
                    +1  573     
                    +1  574     
                    +1  575     
                    +1  576     
                    +1  577     
                    +1  578     
                    +1  579     
                    +1  580     
                    +1  581     
                    +1  582     
                    +1  583     
                    +1  584     
                    +1  585     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    10

                    +1  586     
                    +1  587     
                    +1  588     
                    +1  589     
                    +1  590     
                    +1  591     
                    +1  592     
                    +1  593     
                    +1  594     
                    +1  595     
                    +1  596     
                    +1  597     
                    +1  598     
                    +1  599     
                    +1  600     
                    +1  601     
                    +1  602     
                    +1  603     
                    +1  604     
                    +1  605     
                    +1  606     
                    +1  607     
                    +1  608     
                    +1  609     
                    +1  610     
                    +1  611     
                    +1  612     
                    +1  613     
                    +1  614     
                    +1  615     
                    +1  616     
                    +1  617     
                    +1  618     
                    +1  619     
                    +1  620     
                    +1  621     
                    +1  622     
                    +1  623     
                    +1  624     
                    +1  625     
                    +1  626     
                    +1  627     
                    +1  628     
                    +1  629     
                    +1  630     
                    +1  631     
                    +1  632     
                    +1  633     
                    +1  634     
                    +1  635     
                    +1  636     
                    +1  637     
                    +1  638     
                    +1  639     
                    +1  640     
                    +1  641     
                    +1  642     
                    +1  643     
                    +1  644     
                    +1  645     
                    +1  646     
                    +1  647     
                    +1  648     
                    +1  649     
                    +1  650     
                    +1  651     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    11

                    +1  652     
                    +1  653     
                    +1  654     
                    +1  655     
                    +1  656     
                    +1  657     
                    +1  658     
                    +1  659     
                    +1  660     
                    +1  661     
                    +1  662     
                    +1  663     
                    +1  664     
                    +1  665     
                    +1  666     
                    +1  667     
                    +1  668     
                    +1  669     
                    +1  670     
                    +1  671     
                    +1  672     
                    +1  673     
                    +1  674     
                    +1  675     
                    +1  676     
                    +1  677     
                    +1  678     
                    +1  679     
                    +1  680     
                    +1  681     
                    +1  682     
                    +1  683     
                    +1  684     
                    +1  685     
                    +1  686     
                    +1  687     
                    +1  688     
                    +1  689     
                    +1  690     
                    +1  691     
                    +1  692     
                    +1  693     
                    +1  694     
                    +1  695     
                    +1  696     
                    +1  697     
                    +1  698     
                    +1  699     
                    +1  700     
                    +1  701     
                    +1  702     
                    +1  703     
                    +1  704     
                    +1  705     
                    +1  706     
                    +1  707     
                    +1  708     
                    +1  709     
                    +1  710     
                    +1  711     
                    +1  712     
                    +1  713     
                    +1  714     
                    +1  715     
                    +1  716     
                    +1  717     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    12

                    +1  718     
                    +1  719     
                    +1  720     
                    +1  721     
                    +1  722     
                    +1  723     
                    +1  724     
                    +1  725     
                    +1  726     
                    +1  727     
                    +1  728     
                    +1  729     
                    +1  730     
                    +1  731     
                    +1  732     
                    +1  733     
                    +1  734     
                    +1  735     
                    +1  736     
                    +1  737     
                    +1  738     
                    +1  739     
                    +1  740     
                    +1  741     
                    +1  742     
                    +1  743     
                    +1  744     
                    +1  745     
                    +1  746     
                    +1  747     
                    +1  748     
                    +1  749     
                    +1  750     
                    +1  751     
                    +1  752     
                    +1  753     
                    +1  754     
                    +1  755     
                    +1  756     
                    +1  757     
                    +1  758     
                    +1  759     
                    +1  760     
                    +1  761     
                    +1  762     
                    +1  763     
                    +1  764     
                    +1  765     
                    +1  766     
                    +1  767     
                    +1  768     
                    +1  769     
                    +1  770     
                    +1  771     
                    +1  772     
                    +1  773     
                    +1  774     
                    +1  775     
                    +1  776     
                    +1  777     
                    +1  778     
                    +1  779     
                    +1  780     
                    +1  781     
                    +1  782     
                    +1  783     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    13

                    +1  784     
                    +1  785     
                    +1  786     
                    +1  787     
                    +1  788     
                    +1  789     
                    +1  790     
                    +1  791     
                    +1  792     
                    +1  793     
                    +1  794     
                    +1  795     
                    +1  796     
                    +1  797     
                    +1  798     
                    +1  799     
                    +1  800     
                    +1  801     
                    +1  802     
                    +1  803     
                    +1  804     
                    +1  805     
                    +1  806     
                    +1  807     
                    +1  808     
                    +1  809     
                    +1  810     
                    +1  811     
                    +1  812     
                    +1  813     
                    +1  814     
                    +1  815     
                    +1  816     
                    +1  817     
                    +1  818     
                    +1  819     
                    +1  820     
                    +1  821     
                    +1  822     
                    +1  823     
                    +1  824     
                    +1  825     
                    +1  826     
                    +1  827     
                    +1  828     
                    +1  829     
                    +1  830     
                    +1  831     
                    +1  832     
                    +1  833     
                    +1  834     
                    +1  835     
                    +1  836     
                    +1  837     
                    +1  838     
                    +1  839     
                    +1  840     
                    +1  841     
                    +1  842     
                    +1  843     
                    +1  844     
                    +1  845     
                    +1  846     
                    +1  847     
                    +1  848     
                    +1  849     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    14

                    +1  850     
                    +1  851     
                    +1  852     
                    +1  853     
                    +1  854     
                    +1  855     
                    +1  856     
                    +1  857     
                    +1  858     
                    +1  859     
                    +1  860     
                    +1  861     
                    +1  862     
                    +1  863     
                    +1  864     
                    +1  865     
                    +1  866     
                    +1  867     
                    +1  868     
                    +1  869     
                    +1  870     
                    +1  871     
                    +1  872     
                    +1  873     
                    +1  874     
                    +1  875     
                    +1  876     
                    +1  877     
                    +1  878     
                    +1  879     
                    +1  880     
                    +1  881     
                    +1  882     
                    +1  883     
                    +1  884     
                    +1  885     
                        886     
                        887     
 DFF9                   888     FLASH_ERASE        CODE  0xDFF9
 DFF6                   889     FLASH_PROG         CODE  0xDFF6
 DFF3                   890     FLASH_ERASE_ABORT  CODE  0xDFF3
 DFF0                   891     FLASH_READ_STATUS  CODE  0xDFF0
 DB06                   892     FLASH_PROTECT      CODE  0xDB06
                        893          
000036                  894     DSEG AT  0x36
000036                  895     DS   0x08                        ; Reserved for FLASH Handler in Boot ROM
                        896     
                        897     ;---------------------------------------------------------------------------------------
                               ---
                        898     ; Function Name   : bit DFlErase (unsigned int DFlash0Sector, unsigned int DFlash1Sector
                               )
                        899     ; Description     : Erase DFlash Sectors (Bank3 and Bank4)
                        900     ; Input Parameter : DFlash0Sector => Bit0 = Erase Sector0
                        901     ;                                    Bit1 = Erase Sector1
                        902     ;                                    Bit2 = Erase Sector2
                        903     ;                                    Bit3 = Erase Sector3
                        904     ;                                    Bit4 = Erase Sector4
                        905     ;                                    Bit5 = Erase Sector5
                        906     ;                                    Bit6 = Erase Sector6
                        907     ;                                    Bit7 = Erase Sector7
                        908     ;                                    Bit8 = Erase Sector8
                        909     ;                                    Bit9 = Erase Sector9
                        910     ;                   DFlash1Sector => Bit0 = Erase Sector0
                        911     ;                                    Bit1 = Erase Sector1
                        912     ;                                    Bit2 = Erase Sector2
                        913     ;                                    Bit3 = Erase Sector3
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    15

                        914     ;                                    Bit4 = Erase Sector4
                        915     ;                                    Bit5 = Erase Sector5
                        916     ;                                    Bit6 = Erase Sector6
                        917     ;                                    Bit7 = Erase Sector7
                        918     ;                                    Bit8 = Erase Sector8
                        919     ;                                    Bit9 = Erase Sector9
                        920     ;                                                   
                        921     ;                   e.g: DFlash0Sector = 0x202
                        922     ;                        DFlash1Sector = 0x380
                        923     ;                         ==> Will erase Bank3 Sector 1
                        924     ;                                  erase Bank3 Sector 9
                        925     ;                                  erase Bank4 Sector 7
                        926     ;                                  erase Bank4 Sector 8
                        927     ;                                  erase Bank4 Sector 9
                        928     ; Return Value    : Bit => 1 = Successfull
                        929     ;                          0 = Fail
                        930     ; Required Stack Size = 12
                        931     ;---------------------------------------------------------------------------------------
                               ---
                        932     
------                  933     ?PR?_DFlErase?XC88x_FLHANDLER SEGMENT CODE    
------                  934     RSEG ?PR?_DFlErase?XC88x_FLHANDLER
                        935     PUBLIC _DFlErase
000000                  936     _DFlErase:
                        937       USING  3
000000 8E19             938       MOV  AR1,R6  ; BANK3
000002 8F18             939       MOV  AR0,R7  ; BANK3
                        940     
000004 8C1C             941       MOV  AR4,R4 ; BANK4
000006 8D1B             942       MOV  AR3,R5 ; BANK4
                        943     
000008 E4               944       CLR    A
000009 F51D             945       MOV    AR5, A ; BANK0
00000B F51E             946       MOV    AR6, A ; BANK1
00000D F51F             947       MOV    AR7, A ; BANK2
                        948     
00000F C0D0             949       PUSH   PSW
000011 C0E0             950             PUSH   ACC
000013 C0F0             951             PUSH   B
                        952     
                                  
                                
                                  
                                  
                                
                                  
                                  
                                
000015 43D018                     ORL    PSW, #0x18
                        962     
                        963     
000018 12DFF9           964       LCALL  FLASH_ERASE
00001B 4008             965       JC     _derase_fail
00001D D0F0             966             POP    B
00001F D0E0             967             POP    ACC
000021 D0D0             968       POP    PSW
000023 D3               969       SETB   C
000024 22               970       RET
000025                  971     _derase_fail:
000025 D0F0             972             POP    B
000027 D0E0             973             POP    ACC
000029 D0D0             974       POP    PSW
00002B C3               975       CLR    C
00002C 22               976     RET
                        977     
                        978     
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    16

                        979     
                        980     
                        981     ;---------------------------------------------------------------------------------------
                               ---
                        982     ;;#if (XC88xAA)
                        983     ;---------------------------------------------------------------------------------------
                               ---
                        984     ; Function Name   : bit PFlErase (unsigned char B0Sector, unsigned char B1Sector, unsign
                               ed char B2Sector)
                        985     ; Description     : Erase PFlash Sectors (Bank0, Bank1, and Bank2)
                        986     ; Input Parameter : B0Sector => Bit0 = Erase Sector0
                        987     ;                               Bit1 = Erase Sector1
                        988     ;                               Bit2 = Erase Sector2
                        989     ;                   B0Sector => Bit0 = Erase Sector0
                        990     ;                               Bit1 = Erase Sector1
                        991     ;                               Bit2 = Erase Sector2
                        992     ;                   B2Sector => Bit0 = Erase Sector0
                        993     ;                               Bit1 = Erase Sector1
                        994     ;                               Bit2 = Erase Sector2
                        995     ;                                                   
                        996     ;                   (e.g: B0Sector = 0x2, B1Sector = 0x01, B2Sector = 0x3
                        997     ;                         ==> Will erase Bank0 Sector 1
                        998     ;                                  erase Bank1 Sector 0
                        999     ;                                  erase Bank2 Sector 0
                       1000     ;                                  erase Bank2 Sector 1
                       1001     ; Return Value    : Bit => 1 = Successfull
                       1002     ;                          0 = Fail
                       1003     ; Required Stack Size = 12
                       1004     ;---------------------------------------------------------------------------------------
                               ---
                       1005     
------                 1006     ?PR?_PFlErase?XC88x_FLHANDLER SEGMENT CODE    
------                 1007     RSEG ?PR?_PFlErase?XC88x_FLHANDLER
                       1008     PUBLIC _PFlErase
000000                 1009     _PFlErase:
                       1010       USING  3
000000 8D1E            1011       MOV  AR6,R5  ; BANK1
000002 8F1D            1012       MOV  AR5,R7  ; BANK0
000004 8B1F            1013       MOV  AR7,R3  ; BANK2
                       1014     
000006 E4              1015       CLR    A
000007 F519            1016       MOV    AR1, A;  Bank3
000009 F518            1017       MOV    AR0, A;  Bank3
00000B F51C            1018       MOV    AR4, A;  Bank4
00000D F51B            1019       MOV    AR3, A;  Bank4
00000F C0D0            1020       PUSH   PSW
000011 C0E0            1021             PUSH   ACC
000013 C0F0            1022             PUSH   B
                       1023     
                                  
                                
                                  
                                  
                                
                                  
                                  
                                
000015 43D018                     ORL    PSW, #0x18
                       1033     
                       1034      
                       1035      ;MOV    FLASH_VAR, A    ; FLASH_READY
000018 12DFF9          1036       LCALL  FLASH_ERASE
00001B 4008            1037       JC     _perase_fail
00001D D0F0            1038             POP    B
00001F D0E0            1039             POP    ACC
000021 D0D0            1040       POP    PSW
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    17

000023 D3              1041       SETB   C
000024 22              1042       RET
000025                 1043     _perase_fail:
000025 D0F0            1044             POP    B
000027 D0E0            1045             POP    ACC
000029 D0D0            1046       POP    PSW
00002B C3              1047       CLR    C
00002C 22              1048     RET
                       1049     
                       1050     
                       1051     
                       1052     
                       1053     ;---------------------------------------------------------------------------------------
                               ---
                       1054     ; Function Name:   bit FlProg(unsigned char idata *WlBuf)
                       1055     ; Description:     Program the 32byte data in the WlBuf to the EEPROM
                       1056     ;                  When calling this function, user should already programmed
                       1057     ;                  the DPTR with the EEPROM address (aligned to 32byte boundary)
                       1058     ; Input Parameter: *WlBuf  ==> The pointer to the starting location of the buffer
                       1059     ;                              in the IDATA memory.
                       1060     ; Return Value:    Bit => 1 = Successfull
                       1061     ;                         0 = Fail
                       1062     ; Required Stack Size = 12
                       1063     ;---------------------------------------------------------------------------------------
                               ---
------                 1064     ?PR?_FlProg?XC88x_FLHANDLER SEGMENT CODE    
------                 1065     RSEG ?PR?_FlProg?XC88x_FLHANDLER
                       1066     PUBLIC _FlProg
000000                 1067     _FlProg:
                       1068     using 3
000000 8F18            1069       MOV   AR0,R7          ; move C51 parameter registers to AR0
                       1070                            ; this is the pointer to the WLBuf
                       1071                                                                                             
                                
                       1072     ;       MOV             A,#0x6F                 
                       1073     ;       MOV             DPH,A
                       1074     ;       MOV             A,#0x0          ; the DPL will = DPL+0x20 after one successful p
                               rogram  
                       1075     ;       MOV             DPL,A                                                           
                                                        
                       1076                                                                                             
                                
000002 C0D0            1077       PUSH   PSW
000004 C0E0            1078             PUSH   ACC
000006 C0F0            1079             PUSH   B
                       1080     
                                  
                                
                                  
                                  
                                
                                  
                                  
                                
000008 43D018                     ORL    PSW, #0x18
                       1090     
                       1091             
00000B 12DFF6          1092       LCALL  FLASH_PROG     ; LCALL FLASH_PROG
00000E 4008            1093       JC     _prog_fail
000010 D0F0            1094             POP    B
000012 D0E0            1095             POP    ACC
000014 D0D0            1096       POP    PSW
000016 D3              1097       SETB   C
000017 22              1098       RET
000018                 1099     _prog_fail:
000018 D0F0            1100             POP    B
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    18

00001A D0E0            1101             POP    ACC
00001C D0D0            1102       POP    PSW
00001E C3              1103       CLR    C
00001F 22              1104     RET
                       1105     
                       1106     
                       1107     
                       1108     
                       1109     
                       1110     
                       1111     
                       1112     END
                       1113             
                       1114     ;;#endif    ;;#if (XC88xAA)
AX51 MACRO ASSEMBLER  XC88X_FLHANDLER                                                       07/13/13 20:09:09 PAGE    19

SYMBOL TABLE LISTING
------ ----- -------


N A M E                                    T Y P E  V A L U E     ATTRIBUTES

?PR?_DFLERASE?XC88X_FLHANDLER . . . . .    C  SEG   00002DH       REL=UNIT, ALN=BYTE
?PR?_FLPROG?XC88X_FLHANDLER . . . . . .    C  SEG   000020H       REL=UNIT, ALN=BYTE
?PR?_PFLERASE?XC88X_FLHANDLER . . . . .    C  SEG   00002DH       REL=UNIT, ALN=BYTE
_DERASE_FAIL. . . . . . . . . . . . . .    C  ADDR  0025H     R   SEG=?PR?_DFLERASE?XC88X_FLHANDLER
_DFLERASE . . . . . . . . . . . . . . .    C  ADDR  0000H     R   SEG=?PR?_DFLERASE?XC88X_FLHANDLER
_FLPROG . . . . . . . . . . . . . . . .    C  ADDR  0000H     R   SEG=?PR?_FLPROG?XC88X_FLHANDLER
_PERASE_FAIL. . . . . . . . . . . . . .    C  ADDR  0025H     R   SEG=?PR?_PFLERASE?XC88X_FLHANDLER
_PFLERASE . . . . . . . . . . . . . . .    C  ADDR  0000H     R   SEG=?PR?_PFLERASE?XC88X_FLHANDLER
_PROG_FAIL. . . . . . . . . . . . . . .    C  ADDR  0018H     R   SEG=?PR?_FLPROG?XC88X_FLHANDLER
ACC . . . . . . . . . . . . . . . . . .    D  ADDR  00E0H     A   
AR0 . . . . . . . . . . . . . . . . . .    D  ADDR  0018H     A   
AR1 . . . . . . . . . . . . . . . . . .    D  ADDR  0019H     A   
AR3 . . . . . . . . . . . . . . . . . .    D  ADDR  001BH     A   
AR4 . . . . . . . . . . . . . . . . . .    D  ADDR  001CH     A   
AR5 . . . . . . . . . . . . . . . . . .    D  ADDR  001DH     A   
AR6 . . . . . . . . . . . . . . . . . .    D  ADDR  001EH     A   
AR7 . . . . . . . . . . . . . . . . . .    D  ADDR  001FH     A   
B . . . . . . . . . . . . . . . . . . .    D  ADDR  00F0H     A   
FLASH_ERASE . . . . . . . . . . . . . .    C  ADDR  DFF9H     A   
FLASH_ERASE_ABORT . . . . . . . . . . .    C  ADDR  DFF3H     A   
FLASH_PROG. . . . . . . . . . . . . . .    C  ADDR  DFF6H     A   
FLASH_PROTECT . . . . . . . . . . . . .    C  ADDR  DB06H     A   
FLASH_READ_STATUS . . . . . . . . . . .    C  ADDR  DFF0H     A   
PSW . . . . . . . . . . . . . . . . . .    D  ADDR  00D0H     A   


REGISTER BANK(S) USED: 0 3 


ASSEMBLY COMPLETE.  0 WARNING(S), 0 ERROR(S).

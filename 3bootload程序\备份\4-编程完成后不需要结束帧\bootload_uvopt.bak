<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectOpt xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_opt.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Extensions>
    <cExt>*.c</cExt>
    <aExt>*.s*; *.src; *.a*</aExt>
    <oExt>*.obj</oExt>
    <lExt>*.lib</lExt>
    <tExt>*.txt; *.h; *.inc</tExt>
    <pExt>*.plm</pExt>
    <CppX>*.cpp</CppX>
  </Extensions>

  <DaveTm>
    <dwLowDateTime>1710172654</dwLowDateTime>
    <dwHighDateTime>30309051</dwHighDateTime>
  </DaveTm>

  <Target>
    <TargetName>Target 1</TargetName>
    <ToolsetNumber>0x0</ToolsetNumber>
    <ToolsetName>MCS-51</ToolsetName>
    <TargetOption>
      <CLK51>96000000</CLK51>
      <OPTTT>
        <gFlags>1</gFlags>
        <BeepAtEnd>1</BeepAtEnd>
        <RunSim>1</RunSim>
        <RunTarget>0</RunTarget>
      </OPTTT>
      <OPTHX>
        <HexSelection>0</HexSelection>
        <FlashByte>65535</FlashByte>
        <HexRangeLowAddress>0</HexRangeLowAddress>
        <HexRangeHighAddress>0</HexRangeHighAddress>
        <HexOffset>0</HexOffset>
      </OPTHX>
      <OPTLEX>
        <PageWidth>120</PageWidth>
        <PageLength>65</PageLength>
        <TabStop>8</TabStop>
        <ListingPath>.\</ListingPath>
      </OPTLEX>
      <ListingPage>
        <CreateCListing>1</CreateCListing>
        <CreateAListing>1</CreateAListing>
        <CreateLListing>1</CreateLListing>
        <CreateIListing>1</CreateIListing>
        <AsmCond>1</AsmCond>
        <AsmSymb>1</AsmSymb>
        <AsmXref>0</AsmXref>
        <CCond>1</CCond>
        <CCode>1</CCode>
        <CListInc>1</CListInc>
        <CSymb>1</CSymb>
        <LinkerCodeListing>1</LinkerCodeListing>
      </ListingPage>
      <OPTXL>
        <LMap>1</LMap>
        <LComments>1</LComments>
        <LGenerateSymbols>1</LGenerateSymbols>
        <LLibSym>1</LLibSym>
        <LLines>1</LLines>
        <LLocSym>1</LLocSym>
        <LPubSym>1</LPubSym>
        <LXref>0</LXref>
        <LExpSel>0</LExpSel>
      </OPTXL>
      <OPTFL>
        <tvExp>1</tvExp>
        <tvExpOptDlg>0</tvExpOptDlg>
        <IsCurrentTarget>1</IsCurrentTarget>
      </OPTFL>
      <CpuCode>6</CpuCode>
      <Books>
        <Book>
          <Number>0</Number>
          <Title>User's Manual</Title>
          <Path>DATASHTS\INFINEON\XC886_UM.PDF</Path>
        </Book>
        <Book>
          <Number>1</Number>
          <Title>XC800 Instruction Set Manual</Title>
          <Path>DATASHTS\INFINEON\XC800_IS.PDF</Path>
        </Book>
      </Books>
      <DllOpt>
        <SimDllName>S8051.DLL</SimDllName>
        <SimDllArguments>-m8051EW</SimDllArguments>
        <SimDlgDllName>DCore51.DLL</SimDlgDllName>
        <SimDlgDllArguments>-pXC886</SimDlgDllArguments>
        <TargetDllName>S8051.DLL</TargetDllName>
        <TargetDllArguments></TargetDllArguments>
        <TargetDlgDllName>TCore51.DLL</TargetDlgDllName>
        <TargetDlgDllArguments>-pXC886</TargetDlgDllArguments>
      </DllOpt>
      <DebugOpt>
        <uSim>0</uSim>
        <uTrg>1</uTrg>
        <sLdApp>1</sLdApp>
        <sGomain>1</sGomain>
        <sRbreak>1</sRbreak>
        <sRwatch>1</sRwatch>
        <sRmem>1</sRmem>
        <sRfunc>1</sRfunc>
        <sRbox>1</sRbox>
        <tLdApp>1</tLdApp>
        <tGomain>0</tGomain>
        <tRbreak>1</tRbreak>
        <tRwatch>1</tRwatch>
        <tRmem>1</tRmem>
        <tRfunc>0</tRfunc>
        <tRbox>1</tRbox>
        <sRunDeb>0</sRunDeb>
        <sLrtime>0</sLrtime>
        <nTsel>7</nTsel>
        <sDll></sDll>
        <sDllPa></sDllPa>
        <sDlgDll></sDlgDll>
        <sDlgPa></sDlgPa>
        <sIfile></sIfile>
        <tDll></tDll>
        <tDllPa></tDllPa>
        <tDlgDll></tDlgDll>
        <tDlgPa></tDlgPa>
        <tIfile></tIfile>
        <pMon>BIN\DAS2XC800.DLL</pMon>
      </DebugOpt>
      <TargetDriverDllRegistry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DLGTC51</Key>
          <Name>(5130=-1,-1,-1,-1,0)(5131=-1,-1,-1,-1,0)(5132=-1,-1,-1,-1,0)(5133=-1,-1,-1,-1,0)(5134=-1,-1,-1,-1,0)(1203=-1,-1,-1,-1,0)(5152=-1,-1,-1,-1,0)(94=-1,-1,-1,-1,0)(5150=-1,-1,-1,-1,0)(5140=-1,-1,-1,-1,0)(5141=-1,-1,-1,-1,0)(5142=-1,-1,-1,-1,0)(5143=-1,-1,-1,-1,0)(5144=-1,-1,-1,-1,0)(5145=-1,-1,-1,-1,0)(5019=-1,-1,-1,-1,0)(5020=-1,-1,-1,-1,0)(5018=-1,-1,-1,-1,0)(5021=-1,-1,-1,-1,0)(5022=-1,-1,-1,-1,0)(5151=-1,-1,-1,-1,0)(5155=-1,-1,-1,-1,0)(1219=-1,-1,-1,-1,0)(90=-1,-1,-1,-1,0)(91=-1,-1,-1,-1,0)(1207=-1,-1,-1,-1,0)(5154=-1,-1,-1,-1,0)(5153=-1,-1,-1,-1,0)(1208=-1,-1,-1,-1,0)(5156=-1,-1,-1,-1,0)(5157=-1,-1,-1,-1,0)(5158=-1,-1,-1,-1,0)(5159=-1,-1,-1,-1,0)(3047=-1,-1,-1,-1,0)(3050=-1,-1,-1,-1,0)(3051=-1,-1,-1,-1,0)</Name>
        </SetRegEntry>
        <SetRegEntry>
          <Number>0</Number>
          <Key>DAS2XC800</Key>
          <Name> -O511 -F29 -U768 -H"localhost" -S"UDAS" -D"MW000099A" -T4294967295</Name>
        </SetRegEntry>
      </TargetDriverDllRegistry>
      <Breakpoint/>
      <WatchWindow1>
        <Ww>
          <count>0</count>
          <WinNumber>1</WinNumber>
          <ItemText>ulCANData</ItemText>
        </Ww>
        <Ww>
          <count>1</count>
          <WinNumber>1</WinNumber>
          <ItemText>WLBuf</ItemText>
        </Ww>
      </WatchWindow1>
      <DebugFlag>
        <trace>0</trace>
        <periodic>1</periodic>
        <aLwin>1</aLwin>
        <aCover>0</aCover>
        <aSer1>0</aSer1>
        <aSer2>0</aSer2>
        <aPa>0</aPa>
        <viewmode>1</viewmode>
        <vrSel>0</vrSel>
        <aSym>0</aSym>
        <aTbox>0</aTbox>
        <AscS1>0</AscS1>
        <AscS2>0</AscS2>
        <AscS3>0</AscS3>
        <aSer3>0</aSer3>
        <eProf>0</eProf>
        <aLa>0</aLa>
        <aPa1>0</aPa1>
        <AscS4>0</AscS4>
        <aSer4>0</aSer4>
        <StkLoc>0</StkLoc>
        <TrcWin>0</TrcWin>
        <newCpu>0</newCpu>
        <uProt>0</uProt>
      </DebugFlag>
      <LintExecutable></LintExecutable>
      <LintConfigFile></LintConfigFile>
    </TargetOption>
  </Target>

  <Group>
    <GroupName>Dave Files</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>1</FileNumber>
      <FileType>2</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>306</TopLine>
      <CurrentLine>312</CurrentLine>
      <bDave2>1</bDave2>
      <PathWithFileName>.\START_XC.a51</PathWithFileName>
      <FilenameWithoutPath>START_XC.a51</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>2</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>10</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>197</TopLine>
      <CurrentLine>211</CurrentLine>
      <bDave2>1</bDave2>
      <PathWithFileName>.\MAIN.C</PathWithFileName>
      <FilenameWithoutPath>MAIN.C</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>1</GroupNumber>
      <FileNumber>3</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>0</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>0</TopLine>
      <CurrentLine>0</CurrentLine>
      <bDave2>1</bDave2>
      <PathWithFileName>.\CAN.C</PathWithFileName>
      <FilenameWithoutPath>CAN.C</FilenameWithoutPath>
    </File>
  </Group>

  <Group>
    <GroupName>User Files</GroupName>
    <tvExp>1</tvExp>
    <tvExpOptDlg>0</tvExpOptDlg>
    <cbSel>0</cbSel>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>4</FileNumber>
      <FileType>1</FileType>
      <tvExp>0</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>21</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>396</TopLine>
      <CurrentLine>410</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>.\boot.c</PathWithFileName>
      <FilenameWithoutPath>boot.c</FilenameWithoutPath>
    </File>
    <File>
      <GroupNumber>2</GroupNumber>
      <FileNumber>5</FileNumber>
      <FileType>2</FileType>
      <tvExp>1</tvExp>
      <Focus>0</Focus>
      <ColumnNumber>12</ColumnNumber>
      <tvExpOptDlg>0</tvExpOptDlg>
      <TopLine>72</TopLine>
      <CurrentLine>73</CurrentLine>
      <bDave2>0</bDave2>
      <PathWithFileName>.\XC88x_FLHANDLER.ASM</PathWithFileName>
      <FilenameWithoutPath>XC88x_FLHANDLER.ASM</FilenameWithoutPath>
    </File>
  </Group>

</ProjectOpt>

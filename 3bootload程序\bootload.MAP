LX51 LINKER/LOCATER V4.58                                                               07/13/2013  20:09:09  PAGE 1


LX51 LINKER/LOCATER V4.58, INVOKED BY:
C:\KEIL\C51\BIN\LX51.EXE START_XC.obj, MAIN.obj, CAN.obj, boot.obj, XC88x_FLHANDLER.obj TO bootload CODE RESERVE (I:0XC1
>> -I:0XFF) CLASSES (XDATA (X:0XF000-X:0XF5FF), HDATA (X:0XF000-X:0XF5FF), CODE (C:0X7003-C:0X7EFF)) SEGMENTS (?C_C51STA
>> RTUP (C:0X7003), ?PR?MAIN?MAIN)


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE


INPUT MODULES INCLUDED:
  START_XC.obj (?C_STARTUP)
         COMMENT TYPE 0: AX51 V3.07d
  MAIN.obj (MAIN)
         COMMENT TYPE 0: C51 V9.50a
  CAN.obj (CAN)
         COMMENT TYPE 0: C51 V9.50a
  boot.obj (BOOT)
         COMMENT TYPE 0: C51 V9.50a
  XC88x_FLHANDLER.obj (XC88X_FLHANDLER)
         COMMENT TYPE 0: AX51 V3.07d
  C:\KEIL\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?ULCMP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?ULSHR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?COPY517)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDIDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDPDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDCODE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  bootload (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
X:000000H   X:00F000H   X:00F5FFH   000026H   XDATA
X:000000H   X:00F000H   X:00F5FFH             HDATA
C:000000H   C:007003H   C:007EFFH   000C0CH   CODE
I:000000H   I:000000H   I:0000FFH   000041H   IDATA
C:000000H   C:000000H   C:00FFFFH   000010H   CONST
I:000000H   I:000000H   I:00007FH   000018H   DATA


MEMORY MAP OF MODULE:  bootload (?C_STARTUP)


LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 2


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   000017H   000010H   ---    ---      **GAP**
000018H   00001FH   000008H   ---    AT..     DATA           "REG BANK 3"
000020H   000035H   000016H   ---    ---      **GAP**
000036H   00003DH   000008H   ---    OFFS..   DATA           ?DT?XC88X_FLHANDLER?1
00003EH   00007FH   000042H   ---    ---      **GAP**
000080H   0000BFH   000040H   BYTE   OFFS..   IDATA          ?ID?BOOT?0
0000C0H   0000C0H   000001H   BYTE   UNIT     IDATA          ?STACK
0000C1H   0000FFH   00003FH   ---    ---      *** RESERVED MEMORY ***

* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   00000FH   000010H   BYTE   UNIT     CONST          ?CO?BOOT
000010H   006FFFH   006FF0H   ---    ---      **GAP**
007000H   007002H   000003H   ---    OFFS..   CODE           ?CO?START_XC?3
007003H   0070DDH   0000DBH   BYTE   UNIT     CODE           ?C_C51STARTUP
0070DEH   0070EFH   000012H   BYTE   INBLOCK  CODE           ?PR?MAIN?MAIN
0070F0H   0072E0H   0001F1H   BYTE   UNIT     CODE           ?C?LIB_CODE
0072E1H   00730DH   00002DH   BYTE   UNIT     CODE           ?PR?_DFLERASE?XC88X_FLHANDLER
00730EH   00733AH   00002DH   BYTE   UNIT     CODE           ?PR?_PFLERASE?XC88X_FLHANDLER
00733BH   00735AH   000020H   BYTE   UNIT     CODE           ?PR?_FLPROG?XC88X_FLHANDLER
00735BH   007376H   00001CH   BYTE   UNIT     CODE           ?C_INITSEG
007377H   00738BH   000015H   BYTE   INBLOCK  CODE           ?PR?MAIN_VINIT?MAIN
00738CH   0074BCH   000131H   BYTE   INBLOCK  CODE           ?PR?CAN_VINIT?CAN
0074BDH   0074E2H   000026H   BYTE   INBLOCK  CODE           ?PR?_CAN_VWRITEAMDATA?CAN
0074E3H   0074F2H   000010H   BYTE   INBLOCK  CODE           ?PR?_CAN_VSETLISTCOMMAND?CAN
0074F3H   007534H   000042H   BYTE   INBLOCK  CODE           ?PR?_CAN_UBREQUESTMSGOBJ?CAN
007535H   007585H   000051H   BYTE   INBLOCK  CODE           ?PR?_CAN_WAITTRANSMIT?BOOT
007586H   0075BAH   000035H   BYTE   INBLOCK  CODE           ?PR?_CAN_SENDACK?BOOT
0075BBH   007607H   00004DH   BYTE   INBLOCK  CODE           ?PR?DFLASHREAD?BOOT
007608H   00762EH   000027H   BYTE   INBLOCK  CODE           ?PR?FLASH_WAIT?BOOT
00762FH   00764BH   00001DH   BYTE   INBLOCK  CODE           ?PR?CAN_SETWDTRESET?BOOT
00764CH   00767FH   000034H   BYTE   INBLOCK  CODE           ?PR?_CAN_VTRANSMIT?CAN
007680H   0076EFH   000070H   BYTE   INBLOCK  CODE           ?PR?_CAN_VLOADDATA?CAN
0076F0H   00777EH   00008FH   BYTE   INBLOCK  CODE           ?PR?_FLASHREAD?BOOT
00777FH   00779BH   00001DH   BYTE   INBLOCK  CODE           ?PR?CHECKNULL?BOOT
00779CH   0077C2H   000027H   BYTE   INBLOCK  CODE           ?PR?_CHECKFLPROG?BOOT
0077C3H   0077CAH   000008H   BYTE   UNIT     CODE           ?L?COM000E
0077CBH   0077DCH   000012H   BYTE   UNIT     CODE           ?L?COM0007
0077DDH   0077E7H   00000BH   BYTE   UNIT     CODE           ?L?COM0012
0077E8H   0077EEH   000007H   BYTE   UNIT     CODE           ?L?COM0018
0077EFH   0077F7H   000009H   BYTE   UNIT     CODE           ?L?COM0013
0077F8H   0077FFH   000008H   ---    ---      **GAP**
007800H   007A4EH   00024FH   BYTE   INBLOCK  CODE           ?PR?BOOTMAIN?BOOT
007A4FH   007B1BH   0000CDH   BYTE   INBLOCK  CODE           ?PR?_CAN_READFIFO?BOOT
007B1CH   007B54H   000039H   BYTE   UNIT     CODE           ?L?COM0001
007B55H   007B69H   000015H   BYTE   UNIT     CODE           ?L?COM000A
007B6AH   007B78H   00000FH   BYTE   UNIT     CODE           ?L?COM000F
007B79H   007B81H   000009H   BYTE   UNIT     CODE           ?L?COM001A
007B82H   007B8AH   000009H   BYTE   UNIT     CODE           ?L?COM001B
007B8BH   007B97H   00000DH   BYTE   UNIT     CODE           ?L?COM0017
007B98H   007BAAH   000013H   BYTE   UNIT     CODE           ?L?COM0014
007BABH   007BDCH   000032H   BYTE   UNIT     CODE           ?L?COM000D
007BDDH   007BE3H   000007H   BYTE   UNIT     CODE           ?L?COM001E
007BE4H   007BFBH   000018H   BYTE   UNIT     CODE           ?L?COM0011
007BFCH   007C0AH   00000FH   BYTE   UNIT     CODE           ?L?COM0015
007C0BH   007C13H   000009H   BYTE   UNIT     CODE           ?L?COM001C

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
00F000H   00F01CH   00001DH   BYTE   UNIT     XDATA          _XDATA_GROUP_
00F01DH   00F025H   000009H   BYTE   UNIT     XDATA          ?XD?BOOT

LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 3




OVERLAY MAP OF MODULE:   bootload (?C_STARTUP)


FUNCTION/MODULE                XDATA_GROUP
--> CALLED FUNCTION/MODULE     START  STOP
==========================================
?C_C51STARTUP                  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                      ----- -----
  +--> MAIN_VINIT/MAIN
  +--> _CAN_SENDACK/BOOT
  +--> BOOTMAIN/BOOT

MAIN_VINIT/MAIN                ----- -----
  +--> CAN_VINIT/CAN

CAN_VINIT/CAN                  ----- -----
  +--> _CAN_VSETLISTCOMMAND/CAN
  +--> _CAN_VWRITEAMDATA/CAN

_CAN_VSETLISTCOMMAND/CAN       ----- -----
  +--> _CAN_VWRITEAMDATA/CAN

_CAN_VWRITEAMDATA/CAN          F000H F003H

_CAN_SENDACK/BOOT              F00AH F013H
  +--> ?CO?BOOT
  +--> _CAN_VLOADDATA/CAN
  +--> _CAN_VTRANSMIT/CAN
  +--> _CAN_WAITTRANSMIT/BOOT

?CO?BOOT                       ----- -----

_CAN_VLOADDATA/CAN             ----- -----

_CAN_VTRANSMIT/CAN             ----- -----

_CAN_WAITTRANSMIT/BOOT         F018H F01CH
  +--> _CAN_UBREQUESTMSGOBJ/CAN

_CAN_UBREQUESTMSGOBJ/CAN       ----- -----

BOOTMAIN/BOOT                  F000H F009H
  +--> ?CO?BOOT
  +--> _CAN_READFIFO/BOOT
  +--> _PFLERASE/XC88X_FLHANDLER
  +--> FLASH_WAIT/BOOT
  +--> _CAN_SENDACK/BOOT
  +--> CHECKNULL/BOOT
  +--> _FLASHREAD/BOOT
  +--> CAN_SETWDTRESET/BOOT
  +--> DFLASHREAD/BOOT
  +--> _DFLERASE/XC88X_FLHANDLER
  +--> _FLPROG/XC88X_FLHANDLER
  +--> _CHECKFLPROG/BOOT

_CAN_READFIFO/BOOT             F00AH F00BH

_PFLERASE/XC88X_FLHANDLER      ----- -----

FLASH_WAIT/BOOT                ----- -----
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 4


  +--> CAN_SETWDTRESET/BOOT

CAN_SETWDTRESET/BOOT           ----- -----

CHECKNULL/BOOT                 ----- -----

_FLASHREAD/BOOT                F00AH F017H
  +--> _CAN_VLOADDATA/CAN
  +--> _CAN_VTRANSMIT/CAN
  +--> _CAN_WAITTRANSMIT/BOOT

DFLASHREAD/BOOT                F00AH F014H
  +--> _CAN_VLOADDATA/CAN
  +--> _CAN_VTRANSMIT/CAN
  +--> _CAN_WAITTRANSMIT/BOOT

_DFLERASE/XC88X_FLHANDLER      ----- -----

_FLPROG/XC88X_FLHANDLER        ----- -----

_CHECKFLPROG/BOOT              ----- -----

?C_INITSEG                     ----- -----



PUBLIC SYMBOLS OF MODULE:  bootload (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      00000000H   NUMBER   ---       ?C?CODESEG
      01007287H   CODE     ---       ?C?COPY517
      01007102H   CODE     ---       ?C?CSTOPTR
      010070F0H   CODE     ---       ?C?CSTPTR
      000000A2H   DATA     BYTE      ?C?DPSEL
      01007124H   CODE     ---       ?C?ILDIX
      010072D1H   CODE     ---       ?C?LLDCODE
      010072ADH   CODE     ---       ?C?LLDIDATA
      010072C5H   CODE     ---       ?C?LLDPDATA
      0100715EH   CODE     ---       ?C?LLDPTR
      010072B9H   CODE     ---       ?C?LLDXDATA
      0100718AH   CODE     ---       ?C?LSTKXDATA
      0100717EH   CODE     ---       ?C?LSTXDATA
      0100713AH   CODE     ---       ?C?ULCMP
      0100714BH   CODE     ---       ?C?ULSHR
      00000000H   NUMBER   ---       ?C?XDATASEG
      01007099H   CODE     ---       ?C_START
      01007000H   CODE     NEAR LAB  ?C_STARTUP
      01007A4FH   CODE     ---       _CAN_ReadFIFO
      0100758AH   CODE     ---       _CAN_sendAck
      010074F3H   CODE     ---       _CAN_ubRequestMsgObj
      01007680H   CODE     ---       _CAN_vLoadData
      010074E3H   CODE     ---       _CAN_vSetListCommand
      0100764CH   CODE     ---       _CAN_vTransmit
      010074BDH   CODE     ---       _CAN_vWriteAMData
      01007535H   CODE     ---       _CAN_waitTransmit
      0100779CH   CODE     ---       _CheckFlProg
      010072E1H   CODE     NEAR LAB  _DFlErase
      010076F0H   CODE     ---       _FlashRead
      0100733BH   CODE     NEAR LAB  _FlProg
      0100730EH   CODE     NEAR LAB  _PFlErase
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
*SFR* 000000CAH   DATA     BYTE      ADC_CHCTR0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 5


*SFR* 000000CBH   DATA     BYTE      ADC_CHCTR1
*SFR* 000000CCH   DATA     BYTE      ADC_CHCTR2
*SFR* 000000CDH   DATA     BYTE      ADC_CHCTR3
*SFR* 000000CEH   DATA     BYTE      ADC_CHCTR4
*SFR* 000000CFH   DATA     BYTE      ADC_CHCTR5
*SFR* 000000D2H   DATA     BYTE      ADC_CHCTR6
*SFR* 000000D3H   DATA     BYTE      ADC_CHCTR7
*SFR* 000000CBH   DATA     BYTE      ADC_CHINCR
*SFR* 000000CAH   DATA     BYTE      ADC_CHINFR
*SFR* 000000CDH   DATA     BYTE      ADC_CHINPR
*SFR* 000000CCH   DATA     BYTE      ADC_CHINSR
*SFR* 000000CAH   DATA     BYTE      ADC_CRCR1
*SFR* 000000CCH   DATA     BYTE      ADC_CRMR1
*SFR* 000000CBH   DATA     BYTE      ADC_CRPR1
*SFR* 000000CFH   DATA     BYTE      ADC_ETRCR
*SFR* 000000CFH   DATA     BYTE      ADC_EVINCR
*SFR* 000000CEH   DATA     BYTE      ADC_EVINFR
*SFR* 000000D3H   DATA     BYTE      ADC_EVINPR
*SFR* 000000D2H   DATA     BYTE      ADC_EVINSR
*SFR* 000000CAH   DATA     BYTE      ADC_GLOBCTR
*SFR* 000000CBH   DATA     BYTE      ADC_GLOBSTR
*SFR* 000000CEH   DATA     BYTE      ADC_INPCR0
*SFR* 000000CDH   DATA     BYTE      ADC_LCBR
*SFR* 000000D1H   DATA     BYTE      ADC_PAGE
*SFR* 000000CCH   DATA     BYTE      ADC_PRAR
*SFR* 000000CFH   DATA     BYTE      ADC_Q0R0
*SFR* 000000D2H   DATA     BYTE      ADC_QBUR0
*SFR* 000000D2H   DATA     BYTE      ADC_QINR0
*SFR* 000000CDH   DATA     BYTE      ADC_QMR0
*SFR* 000000CEH   DATA     BYTE      ADC_QSR0
*SFR* 000000CAH   DATA     BYTE      ADC_RCR0
*SFR* 000000CBH   DATA     BYTE      ADC_RCR1
*SFR* 000000CCH   DATA     BYTE      ADC_RCR2
*SFR* 000000CDH   DATA     BYTE      ADC_RCR3
*SFR* 000000CBH   DATA     BYTE      ADC_RESR0H
*SFR* 000000CAH   DATA     BYTE      ADC_RESR0L
*SFR* 000000CAH   DATA     WORD      ADC_RESR0LH
*SFR* 000000CDH   DATA     BYTE      ADC_RESR1H
*SFR* 000000CCH   DATA     BYTE      ADC_RESR1L
*SFR* 000000CCH   DATA     WORD      ADC_RESR1LH
*SFR* 000000CFH   DATA     BYTE      ADC_RESR2H
*SFR* 000000CEH   DATA     BYTE      ADC_RESR2L
*SFR* 000000CEH   DATA     WORD      ADC_RESR2LH
*SFR* 000000D3H   DATA     BYTE      ADC_RESR3H
*SFR* 000000D2H   DATA     BYTE      ADC_RESR3L
*SFR* 000000D2H   DATA     WORD      ADC_RESR3LH
*SFR* 000000CBH   DATA     BYTE      ADC_RESRA0H
*SFR* 000000CAH   DATA     BYTE      ADC_RESRA0L
*SFR* 000000CAH   DATA     WORD      ADC_RESRA0LH
*SFR* 000000CDH   DATA     BYTE      ADC_RESRA1H
*SFR* 000000CCH   DATA     BYTE      ADC_RESRA1L
*SFR* 000000CCH   DATA     WORD      ADC_RESRA1LH
*SFR* 000000CFH   DATA     BYTE      ADC_RESRA2H
*SFR* 000000CEH   DATA     BYTE      ADC_RESRA2L
*SFR* 000000CEH   DATA     WORD      ADC_RESRA2LH
*SFR* 000000D3H   DATA     BYTE      ADC_RESRA3H
*SFR* 000000D2H   DATA     BYTE      ADC_RESRA3L
*SFR* 000000D2H   DATA     WORD      ADC_RESRA3LH
*SFR* 000000CEH   DATA     BYTE      ADC_VFCR
*SFR* 000000F0H   DATA     BYTE      B
*SFR* 000000BDH   DATA     BYTE      BCON
*SFR* 000000BEH   DATA     BYTE      BG
      01007800H   CODE     ---       BootMain
*SFR* 000000C0H.1 DATA     BIT       C_T2
*SFR* 000000D8H   DATA     BYTE      CAN_ADCON
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 6


*SFR* 000000DAH   DATA     BYTE      CAN_ADH
*SFR* 000000D9H   DATA     BYTE      CAN_ADL
*SFR* 000000D9H   DATA     WORD      CAN_ADLH
*SFR* 000000DBH   DATA     BYTE      CAN_DATA0
*SFR* 000000DBH   DATA     WORD      CAN_DATA01
*SFR* 000000DCH   DATA     BYTE      CAN_DATA1
*SFR* 000000DDH   DATA     BYTE      CAN_DATA2
*SFR* 000000DDH   DATA     WORD      CAN_DATA23
*SFR* 000000DEH   DATA     BYTE      CAN_DATA3
      0100762FH   CODE     ---       CAN_setWDTReset
      0100738CH   CODE     ---       CAN_vInit
*SFR* 000000FBH   DATA     BYTE      CCU6_CC60RH
*SFR* 000000FAH   DATA     BYTE      CCU6_CC60RL
*SFR* 000000FAH   DATA     WORD      CCU6_CC60RLH
*SFR* 000000FBH   DATA     BYTE      CCU6_CC60SRH
*SFR* 000000FAH   DATA     BYTE      CCU6_CC60SRL
*SFR* 000000FAH   DATA     WORD      CCU6_CC60SRLH
*SFR* 000000FDH   DATA     BYTE      CCU6_CC61RH
*SFR* 000000FCH   DATA     BYTE      CCU6_CC61RL
*SFR* 000000FCH   DATA     WORD      CCU6_CC61RLH
*SFR* 000000FDH   DATA     BYTE      CCU6_CC61SRH
*SFR* 000000FCH   DATA     BYTE      CCU6_CC61SRL
*SFR* 000000FCH   DATA     WORD      CCU6_CC61SRLH
*SFR* 000000FFH   DATA     BYTE      CCU6_CC62RH
*SFR* 000000FEH   DATA     BYTE      CCU6_CC62RL
*SFR* 000000FEH   DATA     WORD      CCU6_CC62RLH
*SFR* 000000FFH   DATA     BYTE      CCU6_CC62SRH
*SFR* 000000FEH   DATA     BYTE      CCU6_CC62SRL
*SFR* 000000FEH   DATA     WORD      CCU6_CC62SRLH
*SFR* 0000009BH   DATA     BYTE      CCU6_CC63RH
*SFR* 0000009AH   DATA     BYTE      CCU6_CC63RL
*SFR* 0000009AH   DATA     WORD      CCU6_CC63RLH
*SFR* 0000009BH   DATA     BYTE      CCU6_CC63SRH
*SFR* 0000009AH   DATA     BYTE      CCU6_CC63SRL
*SFR* 0000009AH   DATA     WORD      CCU6_CC63SRLH
*SFR* 000000A7H   DATA     BYTE      CCU6_CMPMODIFH
*SFR* 000000A6H   DATA     BYTE      CCU6_CMPMODIFL
*SFR* 000000FFH   DATA     BYTE      CCU6_CMPSTATH
*SFR* 000000FEH   DATA     BYTE      CCU6_CMPSTATL
*SFR* 0000009DH   DATA     BYTE      CCU6_IENH
*SFR* 0000009CH   DATA     BYTE      CCU6_IENL
*SFR* 0000009FH   DATA     BYTE      CCU6_INPH
*SFR* 0000009EH   DATA     BYTE      CCU6_INPL
*SFR* 0000009DH   DATA     BYTE      CCU6_ISH
*SFR* 0000009CH   DATA     BYTE      CCU6_ISL
*SFR* 000000A5H   DATA     BYTE      CCU6_ISRH
*SFR* 000000A4H   DATA     BYTE      CCU6_ISRL
*SFR* 000000A5H   DATA     BYTE      CCU6_ISSH
*SFR* 000000A4H   DATA     BYTE      CCU6_ISSL
*SFR* 000000A7H   DATA     BYTE      CCU6_MCMCTR
*SFR* 0000009BH   DATA     BYTE      CCU6_MCMOUTH
*SFR* 0000009AH   DATA     BYTE      CCU6_MCMOUTL
*SFR* 0000009FH   DATA     BYTE      CCU6_MCMOUTSH
*SFR* 0000009EH   DATA     BYTE      CCU6_MCMOUTSL
*SFR* 000000FDH   DATA     BYTE      CCU6_MODCTRH
*SFR* 000000FCH   DATA     BYTE      CCU6_MODCTRL
*SFR* 000000A3H   DATA     BYTE      CCU6_PAGE
*SFR* 0000009FH   DATA     BYTE      CCU6_PISEL0H
*SFR* 0000009EH   DATA     BYTE      CCU6_PISEL0L
*SFR* 000000A4H   DATA     BYTE      CCU6_PISEL2
*SFR* 000000A6H   DATA     BYTE      CCU6_PSLR
*SFR* 000000A5H   DATA     BYTE      CCU6_T12DTCH
*SFR* 000000A4H   DATA     BYTE      CCU6_T12DTCL
*SFR* 000000FBH   DATA     BYTE      CCU6_T12H
*SFR* 000000FAH   DATA     BYTE      CCU6_T12L
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 7


*SFR* 000000FAH   DATA     WORD      CCU6_T12LH
*SFR* 0000009BH   DATA     BYTE      CCU6_T12MSELH
*SFR* 0000009AH   DATA     BYTE      CCU6_T12MSELL
*SFR* 0000009DH   DATA     BYTE      CCU6_T12PRH
*SFR* 0000009CH   DATA     BYTE      CCU6_T12PRL
*SFR* 0000009CH   DATA     WORD      CCU6_T12PRLH
*SFR* 000000FDH   DATA     BYTE      CCU6_T13H
*SFR* 000000FCH   DATA     BYTE      CCU6_T13L
*SFR* 000000FCH   DATA     WORD      CCU6_T13LH
*SFR* 0000009FH   DATA     BYTE      CCU6_T13PRH
*SFR* 0000009EH   DATA     BYTE      CCU6_T13PRL
*SFR* 0000009EH   DATA     WORD      CCU6_T13PRLH
*SFR* 000000A7H   DATA     BYTE      CCU6_TCTR0H
*SFR* 000000A6H   DATA     BYTE      CCU6_TCTR0L
*SFR* 000000FBH   DATA     BYTE      CCU6_TCTR2H
*SFR* 000000FAH   DATA     BYTE      CCU6_TCTR2L
*SFR* 0000009DH   DATA     BYTE      CCU6_TCTR4H
*SFR* 0000009CH   DATA     BYTE      CCU6_TCTR4L
*SFR* 000000FFH   DATA     BYTE      CCU6_TRPCTRH
*SFR* 000000FEH   DATA     BYTE      CCU6_TRPCTRL
*SFR* 000000A0H   DATA     BIT       CD_BSY
*SFR* 000000A1H   DATA     BYTE      CD_CON
*SFR* 0000009BH   DATA     BYTE      CD_CORDXH
*SFR* 0000009AH   DATA     BYTE      CD_CORDXL
*SFR* 0000009DH   DATA     BYTE      CD_CORDYH
*SFR* 0000009CH   DATA     BYTE      CD_CORDYL
*SFR* 0000009FH   DATA     BYTE      CD_CORDZH
*SFR* 0000009EH   DATA     BYTE      CD_CORDZL
*SFR* 000000A0H   DATA     BYTE      CD_STATC
      0100777FH   CODE     ---       CheckNull
*SFR* 000000BAH   DATA     BYTE      CMCON
*SFR* 000000BEH   DATA     BYTE      COCON
*SFR* 000000C0H   DATA     BIT       CP_RL2
*SFR* 000000D0H.7 DATA     BIT       CY
      010075BBH   CODE     ---       DFlashRead
*SFR* 000000A0H.4 DATA     BIT       DMAP
*SFR* 00000083H   DATA     BYTE      DPH
*SFR* 00000082H   DATA     BYTE      DPL
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000E8H   DATA     BIT       EADC
*SFR* 000000E8H.4 DATA     BIT       ECCIP0
*SFR* 000000E8H.5 DATA     BIT       ECCIP1
*SFR* 000000E8H.6 DATA     BIT       ECCIP2
*SFR* 000000E8H.7 DATA     BIT       ECCIP3
*SFR* 000000A2H   DATA     BYTE      EO
*SFR* 000000A0H.2 DATA     BIT       EOC
*SFR* 000000A0H.1 DATA     BIT       ERROR
*SFR* 000000A8H.4 DATA     BIT       ES
*SFR* 000000E8H.1 DATA     BIT       ESSC
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H   DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000E8H.2 DATA     BIT       EX2
*SFR* 000000C0H.3 DATA     BIT       EXEN2
*SFR* 000000C0H.6 DATA     BIT       EXF2
*SFR* 000000B7H   DATA     BYTE      EXICON0
*SFR* 000000BAH   DATA     BYTE      EXICON1
*SFR* 000000E8H.3 DATA     BIT       EXM
*SFR* 000000D0H.5 DATA     BIT       F0
*SFR* 000000D0H.1 DATA     BIT       F1
*SFR* 000000E9H   DATA     BYTE      FDCON
*SFR* 000000EBH   DATA     BYTE      FDRES
*SFR* 000000EAH   DATA     BYTE      FDSTEP
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 8


*SFR* 000000BDH   DATA     BYTE      FEAH
*SFR* 000000BCH   DATA     BYTE      FEAL
      0100760CH   CODE     ---       Flash_Wait
*SFR* 000000F7H   DATA     BYTE      HWBPDR
*SFR* 000000F6H   DATA     BYTE      HWBPSR
*SFR* 000000B3H   DATA     BYTE      ID
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*SFR* 000000A8H   DATA     BYTE      IEN0
*SFR* 000000E8H   DATA     BYTE      IEN1
*SFR* 000000B0H.1 DATA     BIT       IERR
*SFR* 000000A0H.3 DATA     BIT       INT_EN
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 000000F8H   DATA     BYTE      IP1
*SFR* 000000B9H   DATA     BYTE      IPH
*SFR* 000000F9H   DATA     BYTE      IPH1
*SFR* 000000B4H   DATA     BYTE      IRCON0
*SFR* 000000B5H   DATA     BYTE      IRCON1
*SFR* 000000B6H   DATA     BYTE      IRCON2
*SFR* 000000B4H   DATA     BYTE      IRCON3
*SFR* 000000B5H   DATA     BYTE      IRCON4
*SFR* 000000B0H   DATA     BIT       IRDY
*SFR* 00000088H   DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
*SFR* 000000A0H.5 DATA     BIT       KEEPX
*SFR* 000000A0H.6 DATA     BIT       KEEPY
*SFR* 000000A0H.7 DATA     BIT       KEEPZ
      010070DEH   CODE     ---       main
      01007377H   CODE     ---       MAIN_vInit
*SFR* 000000B0H.2 DATA     BIT       MDU_BSY
*SFR* 000000B2H   DATA     BYTE      MDU_MD0
*SFR* 000000B3H   DATA     BYTE      MDU_MD1
*SFR* 000000B4H   DATA     BYTE      MDU_MD2
*SFR* 000000B5H   DATA     BYTE      MDU_MD3
*SFR* 000000B6H   DATA     BYTE      MDU_MD4
*SFR* 000000B7H   DATA     BYTE      MDU_MD5
*SFR* 000000B1H   DATA     BYTE      MDU_MDUCON
*SFR* 000000B0H   DATA     BYTE      MDU_MDUSTAT
*SFR* 000000B2H   DATA     BYTE      MDU_MR0
*SFR* 000000B3H   DATA     BYTE      MDU_MR1
*SFR* 000000B4H   DATA     BYTE      MDU_MR2
*SFR* 000000B5H   DATA     BYTE      MDU_MR3
*SFR* 000000B6H   DATA     BYTE      MDU_MR4
*SFR* 000000B7H   DATA     BYTE      MDU_MR5
*SFR* 00000083H   DATA     BYTE      MEM_DPH
*SFR* 00000082H   DATA     BYTE      MEM_DPL
*SFR* 000000BBH   DATA     BYTE      MEM_NMICON
*SFR* 000000BCH   DATA     BYTE      MEM_NMISR
*SFR* 00000096H   DATA     BYTE      MEX3
*SFR* 000000E9H   DATA     BYTE      MISC_CON
*SFR* 000000F3H   DATA     BYTE      MMBPCR
*SFR* 000000F1H   DATA     BYTE      MMCR
*SFR* 000000E9H   DATA     BYTE      MMCR2
*SFR* 000000F5H   DATA     BYTE      MMDR
*SFR* 000000F4H   DATA     BYTE      MMICR
*SFR* 000000F2H   DATA     BYTE      MMSR
*SFR* 000000EBH   DATA     BYTE      MMWR1
*SFR* 000000ECH   DATA     BYTE      MMWR2
*SFR* 000000B3H   DATA     BYTE      MODPISEL
*SFR* 000000B7H   DATA     BYTE      MODPISEL1
*SFR* 000000BAH   DATA     BYTE      MODPISEL2
*SFR* 000000BDH   DATA     BYTE      MODSUSP
*SFR* 000000BBH   DATA     BYTE      NMICON
*SFR* 000000BCH   DATA     BYTE      NMISR
*SFR* 000000B6H   DATA     BYTE      OSC_CON
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 9


*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H   DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0_ALTSEL0
*SFR* 00000086H   DATA     BYTE      P0_ALTSEL1
*SFR* 00000080H   DATA     BYTE      P0_DATA
*SFR* 00000086H   DATA     BYTE      P0_DIR
*SFR* 00000080H   DATA     BYTE      P0_OD
*SFR* 00000086H   DATA     BYTE      P0_PUDEN
*SFR* 00000080H   DATA     BYTE      P0_PUDSEL
*SFR* 00000090H   DATA     BYTE      P1_ALTSEL0
*SFR* 00000091H   DATA     BYTE      P1_ALTSEL1
*SFR* 00000090H   DATA     BYTE      P1_DATA
*SFR* 00000091H   DATA     BYTE      P1_DIR
*SFR* 00000090H   DATA     BYTE      P1_OD
*SFR* 00000091H   DATA     BYTE      P1_PUDEN
*SFR* 00000090H   DATA     BYTE      P1_PUDSEL
*SFR* 000000A0H   DATA     BYTE      P2_DATA
*SFR* 000000A1H   DATA     BYTE      P2_DIR
*SFR* 000000A1H   DATA     BYTE      P2_PUDEN
*SFR* 000000A0H   DATA     BYTE      P2_PUDSEL
*SFR* 000000B0H   DATA     BYTE      P3_ALTSEL0
*SFR* 000000B1H   DATA     BYTE      P3_ALTSEL1
*SFR* 000000B0H   DATA     BYTE      P3_DATA
*SFR* 000000B1H   DATA     BYTE      P3_DIR
*SFR* 000000B0H   DATA     BYTE      P3_OD
*SFR* 000000B1H   DATA     BYTE      P3_PUDEN
*SFR* 000000B0H   DATA     BYTE      P3_PUDSEL
*SFR* 000000C8H   DATA     BYTE      P4_ALTSEL0
*SFR* 000000C9H   DATA     BYTE      P4_ALTSEL1
*SFR* 000000C8H   DATA     BYTE      P4_DATA
*SFR* 000000C9H   DATA     BYTE      P4_DIR
*SFR* 000000C8H   DATA     BYTE      P4_OD
*SFR* 000000C9H   DATA     BYTE      P4_PUDEN
*SFR* 000000C8H   DATA     BYTE      P4_PUDSEL
*SFR* 00000092H   DATA     BYTE      P5_ALTSEL0
*SFR* 00000093H   DATA     BYTE      P5_ALTSEL1
*SFR* 00000092H   DATA     BYTE      P5_DATA
*SFR* 00000093H   DATA     BYTE      P5_DIR
*SFR* 00000092H   DATA     BYTE      P5_OD
*SFR* 00000093H   DATA     BYTE      P5_PUDEN
*SFR* 00000092H   DATA     BYTE      P5_PUDSEL
*SFR* 000000F8H   DATA     BIT       PADC
*SFR* 000000BBH   DATA     BYTE      PASSWD
*SFR* 000000F8H.4 DATA     BIT       PCCIP0
*SFR* 000000F8H.5 DATA     BIT       PCCIP1
*SFR* 000000F8H.6 DATA     BIT       PCCIP2
*SFR* 000000F8H.7 DATA     BIT       PCCIP3
*SFR* 00000087H   DATA     BYTE      PCON
*SFR* 000000B7H   DATA     BYTE      PLL_CON
*SFR* 000000EAH   DATA     BYTE      PLL_CON1
*SFR* 000000B4H   DATA     BYTE      PMCON0
*SFR* 000000B5H   DATA     BYTE      PMCON1
*SFR* 000000BBH   DATA     BYTE      PMCON2
*SFR* 000000B2H   DATA     BYTE      PORT_PAGE
*SFR* 000000B8H.4 DATA     BIT       PS
*SFR* 000000F8H.1 DATA     BIT       PSSC
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H   DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
*SFR* 000000F8H.2 DATA     BIT       PX2
*SFR* 000000F8H.3 DATA     BIT       PXM
*SFR* 00000098H.2 DATA     BIT       RB8
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 10


*SFR* 000000C8H.2 DATA     BIT       RB8_1
*SFR* 00000098H.4 DATA     BIT       REN
*SFR* 000000C8H.4 DATA     BIT       REN_1
*SFR* 00000098H   DATA     BIT       RI
*SFR* 000000C8H   DATA     BIT       RI_1
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000098H   DATA     BYTE      SCON
*SFR* 000000BFH   DATA     BYTE      SCU_PAGE
*SFR* 00000098H.7 DATA     BIT       SM0
*SFR* 000000C8H.7 DATA     BIT       SM0_1
*SFR* 00000098H.6 DATA     BIT       SM1
*SFR* 000000C8H.6 DATA     BIT       SM1_1
*SFR* 00000098H.5 DATA     BIT       SM2
*SFR* 000000C8H.5 DATA     BIT       SM2_1
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000AFH   DATA     BYTE      SSC_BRH
*SFR* 000000AEH   DATA     BYTE      SSC_BRL
*SFR* 000000ABH   DATA     BYTE      SSC_CONH_O
*SFR* 000000ABH   DATA     BYTE      SSC_CONH_P
*SFR* 000000AAH   DATA     BYTE      SSC_CONL_O
*SFR* 000000AAH   DATA     BYTE      SSC_CONL_P
*SFR* 000000A9H   DATA     BYTE      SSC_PISEL
*SFR* 000000ADH   DATA     BYTE      SSC_RBL
*SFR* 000000ACH   DATA     BYTE      SSC_TBL
*SFR* 0000008FH   DATA     BYTE      SYSCON0
*SFR* 000000C3H   DATA     BYTE      T21_RC2H
*SFR* 000000C2H   DATA     BYTE      T21_RC2L
*SFR* 000000C2H   DATA     WORD      T21_RC2LH
*SFR* 000000C0H   DATA     BYTE      T21_T2CON
*SFR* 000000C5H   DATA     BYTE      T21_T2H
*SFR* 000000C4H   DATA     BYTE      T21_T2L
*SFR* 000000C4H   DATA     WORD      T21_T2LH
*SFR* 000000C1H   DATA     BYTE      T21_T2MOD
*SFR* 000000C3H   DATA     BYTE      T2_RC2H
*SFR* 000000C2H   DATA     BYTE      T2_RC2L
*SFR* 000000C2H   DATA     WORD      T2_RC2LH
*SFR* 000000C0H   DATA     BYTE      T2_T2CON
*SFR* 000000C5H   DATA     BYTE      T2_T2H
*SFR* 000000C4H   DATA     BYTE      T2_T2L
*SFR* 000000C4H   DATA     WORD      T2_T2LH
*SFR* 000000C1H   DATA     BYTE      T2_T2MOD
*SFR* 00000098H.3 DATA     BIT       TB8
*SFR* 000000C8H.3 DATA     BIT       TB8_1
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 000000C0H.7 DATA     BIT       TF2
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 00000098H.1 DATA     BIT       TI
*SFR* 000000C8H.1 DATA     BIT       TI_1
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 000000C0H.2 DATA     BIT       TR2
*SFR* 000000CAH   DATA     BYTE      UART1_BCON
*SFR* 000000CBH   DATA     BYTE      UART1_BG
*SFR* 000000CCH   DATA     BYTE      UART1_FDCON
*SFR* 000000CEH   DATA     BYTE      UART1_FDRES
*SFR* 000000CDH   DATA     BYTE      UART1_FDSTEP
*SFR* 000000C9H   DATA     BYTE      UART1_SBUF
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 11


*SFR* 000000C8H   DATA     BYTE      UART1_SCON
*SFR* 000000BBH   DATA     BYTE      WDTCON
*SFR* 000000BFH   DATA     BYTE      WDTH
*SFR* 000000BEH   DATA     BYTE      WDTL
*SFR* 000000BCH   DATA     BYTE      WDTREL
*SFR* 000000BDH   DATA     BYTE      WDTWINB
      00000080H   IDATA    ---       WLBuf
*SFR* 000000B3H   DATA     BYTE      XADDRH



SYMBOL TABLE OF MODULE:  bootload (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      000000A2H   PUBLIC    DATA     BYTE      ?C?DPSEL
      01007000H   PUBLIC    CODE     NEAR LAB  ?C_STARTUP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000F0H   SFRSYM    DATA     BYTE      B
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      00000096H   SFRSYM    DATA     BYTE      MEX3
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000EAH   SFRSYM    DATA     BYTE      PLL_CON1
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      01007016H   SYMBOL    CODE     NEAR LAB  DELAYXTAL
      01007014H   SYMBOL    CODE     NEAR LAB  DELAYXTAL0
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01007047H   SYMBOL    CODE     NEAR LAB  IDATALOOP
      00000001H   SYMBOL    NUMBER   ---       LIN_BSL
      00000001H   SYMBOL    NUMBER   ---       LIN_NAC
      00000001H   SYMBOL    NUMBER   ---       LIN_NAD
      0000000AH   SYMBOL    NUMBER   ---       NDIV
      00000002H   SYMBOL    NUMBER   ---       NDIV_XC86X
      00000018H   SYMBOL    NUMBER   ---       NDIV_XC87X_PLL_CON
      00000020H   SYMBOL    NUMBER   ---       NDIV_XC87X_PLL_CON1
      0000000AH   SYMBOL    NUMBER   ---       NDIV_XC88X
      00000000H   SYMBOL    NUMBER   ---       NR_XC87X
      00000000H   SYMBOL    NUMBER   ---       OD_XC87X
      0100701AH   SYMBOL    CODE     NEAR LAB  OSCR_NOTSET
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      0000F000H   SYMBOL    NUMBER   ---       PDATASTART
      000000F0H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      01007003H   SYMBOL    CODE     NEAR LAB  STARTUP1
      01007037H   SYMBOL    CODE     NEAR LAB  WAIT_LOCK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00010000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       XC82X_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC864_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC866_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC874_CHIP_13FF
      00000000H   SYMBOL    NUMBER   ---       XC874_CHIP_16FF
      00000000H   SYMBOL    NUMBER   ---       XC878_CHIP_13FF
      00000000H   SYMBOL    NUMBER   ---       XC878_CHIP_16FF
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 12


      00000001H   SYMBOL    NUMBER   ---       XC88X_CHIP
      00000600H   SYMBOL    NUMBER   ---       XDATALEN
      01007052H   SYMBOL    CODE     NEAR LAB  XDATALOOP
      0000F000H   SYMBOL    NUMBER   ---       XDATASTART
      00000001H   SYMBOL    NUMBER   ---       XTAL

      01007000H   BLOCK     CODE     NEAR LAB  LVL=0
      01007000H   LINE      CODE     ---       #312
      ---         BLOCKEND  ---      ---       LVL=0

      01007003H   BLOCK     CODE     NEAR LAB  LVL=0
      01007003H   LINE      CODE     ---       #367
      01007006H   LINE      CODE     ---       #368
      01007009H   LINE      CODE     ---       #369
      0100700CH   LINE      CODE     ---       #370
      0100700FH   LINE      CODE     ---       #371
      01007012H   LINE      CODE     ---       #373
      01007014H   LINE      CODE     ---       #375
      01007016H   LINE      CODE     ---       #377
      01007018H   LINE      CODE     ---       #378
      0100701AH   LINE      CODE     ---       #382
      0100701CH   LINE      CODE     ---       #383
      0100701FH   LINE      CODE     ---       #388
      01007021H   LINE      CODE     ---       #389
      01007023H   LINE      CODE     ---       #390
      01007026H   LINE      CODE     ---       #394
      01007029H   LINE      CODE     ---       #395
      0100702CH   LINE      CODE     ---       #396
      0100702FH   LINE      CODE     ---       #397
      01007032H   LINE      CODE     ---       #400
      01007035H   LINE      CODE     ---       #401
      01007037H   LINE      CODE     ---       #409
      01007039H   LINE      CODE     ---       #410
      0100703BH   LINE      CODE     ---       #411
      0100703EH   LINE      CODE     ---       #413
      01007041H   LINE      CODE     ---       #414
      01007044H   LINE      CODE     ---       #419
      01007046H   LINE      CODE     ---       #420
      01007047H   LINE      CODE     ---       #421
      01007048H   LINE      CODE     ---       #422
      0100704AH   LINE      CODE     ---       #439
      0100704DH   LINE      CODE     ---       #440
      0100704FH   LINE      CODE     ---       #444
      01007051H   LINE      CODE     ---       #446
      01007052H   LINE      CODE     ---       #447
      01007053H   LINE      CODE     ---       #448
      01007054H   LINE      CODE     ---       #449
      01007056H   LINE      CODE     ---       #450
      01007058H   LINE      CODE     ---       #486
      0100705BH   LINE      CODE     ---       #488
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      010070DEH   PUBLIC    CODE     ---       main
      01007377H   PUBLIC    CODE     ---       MAIN_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 13


      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 14


      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 15


      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 16


      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 17


      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 18


      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      01007377H   BLOCK     CODE     ---       LVL=0
      01007377H   LINE      ---      ---       #122
      01007377H   LINE      ---      ---       #123
      01007377H   LINE      ---      ---       #138
      0100737AH   LINE      ---      ---       #140
      0100737DH   LINE      ---      ---       #142
      01007380H   LINE      ---      ---       #151
      01007382H   LINE      ---      ---       #156
      01007385H   LINE      ---      ---       #157
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 19


      01007387H   LINE      ---      ---       #158
      01007389H   LINE      ---      ---       #159
      0100738BH   LINE      ---      ---       #172
      ---         BLOCKEND  ---      ---       LVL=0

      010070DEH   BLOCK     CODE     ---       LVL=0
      010070DEH   LINE      ---      ---       #196
      010070DEH   LINE      ---      ---       #197
      010070DEH   LINE      ---      ---       #202
      010070E0H   LINE      ---      ---       #205
      010070E3H   LINE      ---      ---       #206
      010070E6H   LINE      ---      ---       #207
      010070EBH   LINE      ---      ---       #211
      010070EBH   LINE      ---      ---       #212
      010070EBH   LINE      ---      ---       #215
      010070EEH   LINE      ---      ---       #219
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       CAN
      01007680H   PUBLIC    CODE     ---       _CAN_vLoadData
      0100764CH   PUBLIC    CODE     ---       _CAN_vTransmit
      010074F3H   PUBLIC    CODE     ---       _CAN_ubRequestMsgObj
      010074E3H   PUBLIC    CODE     ---       _CAN_vSetListCommand
      010074BDH   PUBLIC    CODE     ---       _CAN_vWriteAMData
      0100738CH   PUBLIC    CODE     ---       CAN_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 20


      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 21


      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 22


      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 23


      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 24


      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 25


      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      0100738CH   BLOCK     CODE     ---       LVL=0
      0100738CH   LINE      ---      ---       #124
      0100738CH   LINE      ---      ---       #125
      0100738CH   LINE      ---      ---       #140
      01007392H   LINE      ---      ---       #141
      01007395H   LINE      ---      ---       #142
      0100739AH   LINE      ---      ---       #144
      0100739DH   LINE      ---      ---       #145
      0100739FH   LINE      ---      ---       #156
      010073A5H   LINE      ---      ---       #157
      010073A8H   LINE      ---      ---       #158
      010073B0H   LINE      ---      ---       #163
      010073B3H   LINE      ---      ---       #168
      010073BBH   LINE      ---      ---       #184
      010073C3H   LINE      ---      ---       #204
      010073C6H   LINE      ---      ---       #205
      010073C9H   LINE      ---      ---       #206
      010073D1H   LINE      ---      ---       #217
      010073D4H   LINE      ---      ---       #218
      010073DCH   LINE      ---      ---       #231
      010073DFH   LINE      ---      ---       #232
      010073E1H   LINE      ---      ---       #233
      010073E3H   LINE      ---      ---       #234
      010073EBH   LINE      ---      ---       #247
      010073F1H   LINE      ---      ---       #248
      010073F4H   LINE      ---      ---       #249
      010073FCH   LINE      ---      ---       #259
      010073FFH   LINE      ---      ---       #260
      01007402H   LINE      ---      ---       #261
      01007405H   LINE      ---      ---       #263
      01007408H   LINE      ---      ---       #264
      0100740BH   LINE      ---      ---       #271
      01007410H   LINE      ---      ---       #274
      0100742AH   LINE      ---      ---       #291
      01007430H   LINE      ---      ---       #293
      01007433H   LINE      ---      ---       #330
      01007438H   LINE      ---      ---       #351
      0100743FH   LINE      ---      ---       #363
      0100744DH   LINE      ---      ---       #376
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 26


      01007450H   LINE      ---      ---       #413
      01007455H   LINE      ---      ---       #422
      0100745CH   LINE      ---      ---       #441
      01007462H   LINE      ---      ---       #454
      01007465H   LINE      ---      ---       #491
      0100746AH   LINE      ---      ---       #500
      01007471H   LINE      ---      ---       #519
      01007477H   LINE      ---      ---       #532
      01007482H   LINE      ---      ---       #544
      0100748DH   LINE      ---      ---       #569
      01007492H   LINE      ---      ---       #578
      01007499H   LINE      ---      ---       #596
      010074A3H   LINE      ---      ---       #730
      010074A9H   LINE      ---      ---       #731
      010074B1H   LINE      ---      ---       #732
      010074B4H   LINE      ---      ---       #733
      010074BCH   LINE      ---      ---       #742
      ---         BLOCKEND  ---      ---       LVL=0

      010074BDH   BLOCK     CODE     ---       LVL=0
      010074BDH   LINE      ---      ---       #781
      010074BDH   LINE      ---      ---       #782
      00000004H   SYMBOL    DATA     DWORD     ulValue
      010074BDH   BLOCK     CODE     NEAR LAB  LVL=1
      010074BDH   LINE      ---      ---       #784
      010074C2H   LINE      ---      ---       #786
      010074C8H   LINE      ---      ---       #787
      010074CEH   LINE      ---      ---       #788
      010074D4H   LINE      ---      ---       #789
      010074DAH   LINE      ---      ---       #790
      010074E2H   LINE      ---      ---       #791
      0200F000H   SYMBOL    XDATA    ---       ulData
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010074E3H   BLOCK     CODE     ---       LVL=0
      010074E3H   LINE      ---      ---       #816
      010074E3H   LINE      ---      ---       #817
      010074E3H   LINE      ---      ---       #818
      010074E5H   LINE      ---      ---       #819
      010074E8H   LINE      ---      ---       #820
      010074EDH   LINE      ---      ---       #822
      010074F0H   LINE      ---      ---       #823
      010074F2H   LINE      ---      ---       #824
      00000004H   SYMBOL    DATA     DWORD     ulVal
      ---         BLOCKEND  ---      ---       LVL=0

      010074F3H   BLOCK     CODE     ---       LVL=0
      010074F3H   LINE      ---      ---       #859
      010074F3H   LINE      ---      ---       #860
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      010074F3H   BLOCK     CODE     NEAR LAB  LVL=1
      010074F3H   LINE      ---      ---       #861
      010074F5H   LINE      ---      ---       #863
      01007506H   LINE      ---      ---       #865
      01007511H   LINE      ---      ---       #868
      01007516H   LINE      ---      ---       #869
      01007516H   LINE      ---      ---       #870
      01007524H   LINE      ---      ---       #871
      01007526H   LINE      ---      ---       #872
      01007526H   LINE      ---      ---       #873
      01007532H   LINE      ---      ---       #875
      01007534H   LINE      ---      ---       #876
      00000005H   SYMBOL    DATA     BYTE      ubReturn
      ---         BLOCKEND  CODE     ---       LVL=1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 27


      ---         BLOCKEND  CODE     ---       LVL=0

      0100764CH   BLOCK     CODE     ---       LVL=0
      0100764CH   LINE      ---      ---       #905
      0100764CH   LINE      ---      ---       #906
      0100764CH   LINE      ---      ---       #907
      0100765DH   LINE      ---      ---       #909
      01007673H   LINE      ---      ---       #912
      0100767FH   LINE      ---      ---       #914
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      ---         BLOCKEND  ---      ---       LVL=0

      01007680H   BLOCK     CODE     ---       LVL=0
      01007680H   LINE      ---      ---       #945
      01007680H   LINE      ---      ---       #946
      01007680H   LINE      ---      ---       #947
      01007691H   LINE      ---      ---       #949
      010076A6H   LINE      ---      ---       #953
      010076A9H   LINE      ---      ---       #956
      010076ACH   LINE      ---      ---       #959
      010076AFH   LINE      ---      ---       #961
      010076B7H   LINE      ---      ---       #964
      010076BAH   LINE      ---      ---       #966
      010076C1H   LINE      ---      ---       #968
      010076C9H   LINE      ---      ---       #971
      010076CCH   LINE      ---      ---       #974
      010076CFH   LINE      ---      ---       #976
      010076E3H   LINE      ---      ---       #978
      010076EFH   LINE      ---      ---       #980
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      00000001H   SYMBOL    DATA     ---       ulpubData
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       BOOT
      00000080H   PUBLIC    IDATA    ---       WLBuf
      01007800H   PUBLIC    CODE     ---       BootMain
      010075BBH   PUBLIC    CODE     ---       DFlashRead
      0100779CH   PUBLIC    CODE     ---       _CheckFlProg
      0100777FH   PUBLIC    CODE     ---       CheckNull
      01007A4FH   PUBLIC    CODE     ---       _CAN_ReadFIFO
      010076F0H   PUBLIC    CODE     ---       _FlashRead
      0100760CH   PUBLIC    CODE     ---       Flash_Wait
      0100758AH   PUBLIC    CODE     ---       _CAN_sendAck
      01007535H   PUBLIC    CODE     ---       _CAN_waitTransmit
      0100762FH   PUBLIC    CODE     ---       CAN_setWDTReset
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 28


      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 29


      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 30


      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 31


      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 32


      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 33


      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH
      01000000H   SYMBOL    CONST    ---       _?ix1000
      01000008H   SYMBOL    CONST    ---       _?ix1001

      0100762FH   BLOCK     CODE     ---       LVL=0
      0100762FH   LINE      ---      ---       #8
      0100762FH   LINE      ---      ---       #9
      0100762FH   LINE      ---      ---       #10
      01007632H   LINE      ---      ---       #11
      01007635H   LINE      ---      ---       #13
      01007638H   LINE      ---      ---       #14
      0100763BH   LINE      ---      ---       #15
      0100763EH   LINE      ---      ---       #16
      01007641H   LINE      ---      ---       #17
      01007644H   LINE      ---      ---       #19
      01007647H   LINE      ---      ---       #20
      0100764AH   LINE      ---      ---       #22
      ---         BLOCKEND  ---      ---       LVL=0

      01007535H   BLOCK     CODE     ---       LVL=0
      01007535H   LINE      ---      ---       #26
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 34


      0100753AH   LINE      ---      ---       #27
      0200F018H   SYMBOL    XDATA    BYTE      RgMsgobj
      0100753AH   BLOCK     CODE     NEAR LAB  LVL=1
      0100753AH   LINE      ---      ---       #29
      01007585H   LINE      ---      ---       #30
      0200F019H   SYMBOL    XDATA    DWORD     i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007586H   BLOCK     CODE     ---       LVL=0
      0100758AH   LINE      ---      ---       #37
      01007592H   LINE      ---      ---       #38
      0200F00AH   SYMBOL    XDATA    BYTE      Ack0
      0200F00BH   SYMBOL    XDATA    BYTE      Ack1
      01007592H   BLOCK     CODE     NEAR LAB  LVL=1
      01007592H   LINE      ---      ---       #39
      010075A1H   LINE      ---      ---       #41
      010075A9H   LINE      ---      ---       #42
      010075B1H   LINE      ---      ---       #44
      0200F00CH   SYMBOL    XDATA    ---       Arrtmp
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007608H   BLOCK     CODE     ---       LVL=0
      0100760CH   LINE      ---      ---       #55
      0100760CH   LINE      ---      ---       #56
      0100760CH   BLOCK     CODE     NEAR LAB  LVL=1
      0100760CH   LINE      ---      ---       #59
      0100760FH   LINE      ---      ---       #60
      01007612H   LINE      ---      ---       #61
      01007613H   LINE      ---      ---       #62
      01007613H   LINE      ---      ---       #63
      01007615H   LINE      ---      ---       #64
      01007615H   LINE      ---      ---       #65
      01007617H   LINE      ---      ---       #66
      01007617H   LINE      ---      ---       #67
      0100761CH   LINE      ---      ---       #68
      0100761CH   LINE      ---      ---       #69
      01007620H   LINE      ---      ---       #71
      01007620H   LINE      ---      ---       #72
      01007625H   LINE      ---      ---       #73
      0100762AH   LINE      ---      ---       #74
      00000005H   SYMBOL    DATA     BYTE      ubCount0
      00000006H   SYMBOL    DATA     BYTE      ubCount1
      00000007H   SYMBOL    DATA     BYTE      ubCount2
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010076F0H   BLOCK     CODE     ---       LVL=0
      010076F0H   LINE      ---      ---       #79
      010076F8H   LINE      ---      ---       #80
      0200F00AH   SYMBOL    XDATA    WORD      Lenth
      010076F8H   BLOCK     CODE     NEAR LAB  LVL=1
      010076F8H   LINE      ---      ---       #85
      01007701H   LINE      ---      ---       #87
      0100771CH   LINE      ---      ---       #88
      0100771CH   LINE      ---      ---       #89
      01007725H   LINE      ---      ---       #90
      0100772EH   LINE      ---      ---       #91
      01007737H   LINE      ---      ---       #92
      01007740H   LINE      ---      ---       #93
      01007749H   LINE      ---      ---       #94
      01007752H   LINE      ---      ---       #95
      0100775BH   LINE      ---      ---       #96
      01007764H   LINE      ---      ---       #98
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 35


      0100776EH   LINE      ---      ---       #101
      0100777EH   LINE      ---      ---       #103
      0200F00CH   SYMBOL    XDATA    WORD      i
      0200F00EH   SYMBOL    XDATA    ---       pBootCode
      0200F010H   SYMBOL    XDATA    ---       tmp
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007A4FH   BLOCK     CODE     ---       LVL=0
      01007A4FH   LINE      ---      ---       #131
      01007A4FH   LINE      ---      ---       #132
      00000001H   SYMBOL    DATA     ---       ulCANData
      01007A4FH   BLOCK     CODE     NEAR LAB  LVL=1
      01007A4FH   LINE      ---      ---       #135
      01007A55H   LINE      ---      ---       #138
      01007A5DH   LINE      ---      ---       #139
      01007A60H   LINE      ---      ---       #141
      01007A69H   LINE      ---      ---       #143
      01007A6BH   LINE      ---      ---       #144
      01007A71H   LINE      ---      ---       #145
      01007A71H   LINE      ---      ---       #146
      01007A78H   LINE      ---      ---       #147
      01007A80H   LINE      ---      ---       #148
      01007A80H   LINE      ---      ---       #149
      01007A81H   LINE      ---      ---       #150
      01007A83H   LINE      ---      ---       #152
      01007A83H   LINE      ---      ---       #153
      01007A89H   LINE      ---      ---       #154
      01007A89H   LINE      ---      ---       #157
      01007A8DH   LINE      ---      ---       #158
      01007A8DH   LINE      ---      ---       #159
      01007A9AH   LINE      ---      ---       #160
      01007A9DH   LINE      ---      ---       #161
      01007A9DH   LINE      ---      ---       #163
      01007AB0H   LINE      ---      ---       #165
      01007AB5H   LINE      ---      ---       #166
      01007ABDH   LINE      ---      ---       #167
      01007AC5H   LINE      ---      ---       #168
      01007ACDH   LINE      ---      ---       #170
      01007AD0H   LINE      ---      ---       #172
      01007AD8H   LINE      ---      ---       #173
      01007AE0H   LINE      ---      ---       #174
      01007AE8H   LINE      ---      ---       #175
      01007AF0H   LINE      ---      ---       #176
      01007AF8H   LINE      ---      ---       #178
      01007B01H   LINE      ---      ---       #180
      01007B06H   LINE      ---      ---       #181
      01007B06H   LINE      ---      ---       #182
      01007B0DH   LINE      ---      ---       #183
      01007B10H   LINE      ---      ---       #184
      01007B10H   LINE      ---      ---       #185
      01007B16H   LINE      ---      ---       #186
      01007B16H   LINE      ---      ---       #187
      01007B1BH   LINE      ---      ---       #188
      0200F00AH   SYMBOL    XDATA    BYTE      j
      00000007H   SYMBOL    DATA     BYTE      ubStatus
      0200F00BH   SYMBOL    XDATA    BYTE      ubReturn
      0200F01DH   SYMBOL    XDATA    BYTE      aubFIFOReadPtr0
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      0100777FH   BLOCK     CODE     ---       LVL=0
      0100777FH   LINE      ---      ---       #191
      0100777FH   LINE      ---      ---       #192
      0100777FH   BLOCK     CODE     NEAR LAB  LVL=1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 36


      0100777FH   LINE      ---      ---       #195
      01007782H   LINE      ---      ---       #197
      0100778FH   LINE      ---      ---       #198
      0100778FH   LINE      ---      ---       #199
      01007796H   LINE      ---      ---       #200
      01007797H   LINE      ---      ---       #201
      01007799H   LINE      ---      ---       #202
      0100779BH   LINE      ---      ---       #203
      00000082H   SYMBOL    DATA     ---       pBootCode
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      0100779CH   BLOCK     CODE     ---       LVL=0
      0100779CH   LINE      ---      ---       #206
      0100779CH   LINE      ---      ---       #207
      00000006H   SYMBOL    DATA     WORD      Add
      00000005H   SYMBOL    DATA     BYTE      Leth
      0100779CH   BLOCK     CODE     NEAR LAB  LVL=1
      0100779CH   LINE      ---      ---       #211
      0100779CH   LINE      ---      ---       #213
      010077A3H   LINE      ---      ---       #214
      010077A3H   LINE      ---      ---       #215
      010077BDH   LINE      ---      ---       #216
      010077C0H   LINE      ---      ---       #217
      010077C2H   LINE      ---      ---       #218
      00000006H   SYMBOL    DATA     ---       pBootCode
      00000004H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010075BBH   BLOCK     CODE     ---       LVL=0
      010075BBH   LINE      ---      ---       #221
      010075BBH   LINE      ---      ---       #222
      010075BBH   BLOCK     CODE     NEAR LAB  LVL=1
      010075BBH   LINE      ---      ---       #227
      010075C4H   LINE      ---      ---       #229
      010075C8H   LINE      ---      ---       #230
      010075C8H   LINE      ---      ---       #231
      010075F1H   LINE      ---      ---       #240
      010075FBH   LINE      ---      ---       #243
      01007607H   LINE      ---      ---       #245
      0200F00AH   SYMBOL    XDATA    BYTE      i
      0200F00BH   SYMBOL    XDATA    ---       pBootCode
      0200F00DH   SYMBOL    XDATA    ---       tmp
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01007800H   BLOCK     CODE     ---       LVL=0
      01007800H   LINE      ---      ---       #250
      01007800H   LINE      ---      ---       #251
      01007800H   BLOCK     CODE     NEAR LAB  LVL=1
      01007800H   LINE      ---      ---       #252
      0100780EH   LINE      ---      ---       #253
      01007813H   LINE      ---      ---       #259
      01007820H   LINE      ---      ---       #261
      01007826H   LINE      ---      ---       #262
      01007826H   LINE      ---      ---       #263
      01007834H   LINE      ---      ---       #264
      01007834H   LINE      ---      ---       #265
      01007834H   LINE      ---      ---       #266
      01007842H   LINE      ---      ---       #267
      01007842H   LINE      ---      ---       #268
      01007848H   LINE      ---      ---       #269
      0100784CH   LINE      ---      ---       #270
      0100785EH   LINE      ---      ---       #272
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 37


      01007865H   LINE      ---      ---       #277
      0100786BH   LINE      ---      ---       #278
      0100786BH   LINE      ---      ---       #279
      0100786EH   LINE      ---      ---       #280
      01007877H   LINE      ---      ---       #281
      0100787CH   LINE      ---      ---       #282
      0100787DH   LINE      ---      ---       #284
      0100787DH   LINE      ---      ---       #285
      0100787FH   LINE      ---      ---       #286
      0100787FH   LINE      ---      ---       #287
      0100787FH   LINE      ---      ---       #288
      01007881H   LINE      ---      ---       #290
      0100788CH   LINE      ---      ---       #291
      0100788CH   LINE      ---      ---       #292
      0100788EH   LINE      ---      ---       #298
      0100788EH   LINE      ---      ---       #299
      01007890H   LINE      ---      ---       #301
      0100789CH   LINE      ---      ---       #302
      0100789CH   LINE      ---      ---       #303
      0100789EH   LINE      ---      ---       #304
      010078A0H   LINE      ---      ---       #305
      010078AAH   LINE      ---      ---       #306
      010078B1H   LINE      ---      ---       #308
      010078B1H   LINE      ---      ---       #309
      010078B3H   LINE      ---      ---       #311
      010078BEH   LINE      ---      ---       #312
      010078BEH   LINE      ---      ---       #313
      010078C1H   LINE      ---      ---       #314
      010078C3H   LINE      ---      ---       #315
      010078CAH   LINE      ---      ---       #316
      010078D1H   LINE      ---      ---       #317
      010078D8H   LINE      ---      ---       #319
      010078D8H   LINE      ---      ---       #320
      010078DAH   LINE      ---      ---       #322
      010078E6H   LINE      ---      ---       #323
      010078E6H   LINE      ---      ---       #324
      010078E9H   LINE      ---      ---       #325
      010078ECH   LINE      ---      ---       #326
      010078ECH   LINE      ---      ---       #328
      010078FAH   LINE      ---      ---       #329
      010078FAH   LINE      ---      ---       #330
      01007900H   LINE      ---      ---       #340
      01007900H   LINE      ---      ---       #341
      01007905H   LINE      ---      ---       #353
      01007913H   LINE      ---      ---       #354
      01007913H   LINE      ---      ---       #355
      01007919H   LINE      ---      ---       #365
      01007919H   LINE      ---      ---       #366
      0100791EH   LINE      ---      ---       #368
      01007922H   LINE      ---      ---       #370
      01007925H   LINE      ---      ---       #371
      01007928H   LINE      ---      ---       #372
      0100792BH   LINE      ---      ---       #374
      0100792EH   LINE      ---      ---       #375
      01007932H   LINE      ---      ---       #376
      01007934H   LINE      ---      ---       #378
      0100793FH   LINE      ---      ---       #379
      0100793FH   LINE      ---      ---       #380
      01007947H   LINE      ---      ---       #381
      0100794AH   LINE      ---      ---       #382
      0100794AH   LINE      ---      ---       #384
      0100794AH   LINE      ---      ---       #385
      0100794CH   LINE      ---      ---       #388
      0100794CH   LINE      ---      ---       #389
      0100795AH   LINE      ---      ---       #390
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 38


      01007968H   LINE      ---      ---       #391
      01007968H   LINE      ---      ---       #392
      0100796EH   LINE      ---      ---       #394
      01007972H   LINE      ---      ---       #395
      01007985H   LINE      ---      ---       #396
      01007990H   LINE      ---      ---       #397
      0100799BH   LINE      ---      ---       #398
      010079A6H   LINE      ---      ---       #399
      010079B1H   LINE      ---      ---       #400
      010079BCH   LINE      ---      ---       #401
      010079C7H   LINE      ---      ---       #402
      010079D2H   LINE      ---      ---       #404
      010079D7H   LINE      ---      ---       #405
      010079D7H   LINE      ---      ---       #406
      010079DBH   LINE      ---      ---       #408
      010079DDH   LINE      ---      ---       #409
      010079DFH   LINE      ---      ---       #410
      010079EAH   LINE      ---      ---       #411
      010079ECH   LINE      ---      ---       #412
      010079EFH   LINE      ---      ---       #415
      010079FFH   LINE      ---      ---       #416
      010079FFH   LINE      ---      ---       #417
      01007A06H   LINE      ---      ---       #418
      01007A10H   LINE      ---      ---       #419
      01007A12H   LINE      ---      ---       #421
      01007A12H   LINE      ---      ---       #422
      01007A19H   LINE      ---      ---       #423
      01007A1EH   LINE      ---      ---       #424
      01007A1EH   LINE      ---      ---       #425
      01007A2CH   LINE      ---      ---       #426
      01007A2CH   LINE      ---      ---       #427
      01007A2DH   LINE      ---      ---       #429
      01007A2DH   LINE      ---      ---       #430
      01007A32H   LINE      ---      ---       #431
      01007A32H   LINE      ---      ---       #432
      01007A33H   LINE      ---      ---       #435
      01007A33H   LINE      ---      ---       #438
      01007A33H   LINE      ---      ---       #441
      01007A33H   LINE      ---      ---       #442
      01007A33H   LINE      ---      ---       #444
      01007A33H   LINE      ---      ---       #445
      01007A3AH   LINE      ---      ---       #446
      01007A3AH   LINE      ---      ---       #447
      01007A3EH   LINE      ---      ---       #448
      01007A40H   LINE      ---      ---       #449
      01007A47H   LINE      ---      ---       #450
      01007A47H   LINE      ---      ---       #451
      01007A4EH   LINE      ---      ---       #452
      01007A4EH   LINE      ---      ---       #454
      0200F000H   SYMBOL    XDATA    ---       ulCANData
      0200F008H   SYMBOL    XDATA    BYTE      RgRtnReadFIFO
      0200F009H   SYMBOL    XDATA    BYTE      i
      0200F01EH   SYMBOL    XDATA    BYTE      RgState
      0200F01FH   SYMBOL    XDATA    BYTE      CntCanRx
      0200F020H   SYMBOL    XDATA    WORD      CntLenth
      0200F022H   SYMBOL    XDATA    WORD      RgCanRxLenth
      0200F024H   SYMBOL    XDATA    WORD      AddProg
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      ---         MODULE    ---      ---       XC88X_FLHANDLER
      010072E1H   PUBLIC    CODE     NEAR LAB  _DFLERASE
      0100733BH   PUBLIC    CODE     NEAR LAB  _FLPROG
      0100730EH   PUBLIC    CODE     NEAR LAB  _PFLERASE
      000000E0H   SFRSYM    DATA     BYTE      ACC
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 39


      000000F0H   SFRSYM    DATA     BYTE      B
      000000D0H   SFRSYM    DATA     BYTE      PSW
      01007306H   SYMBOL    CODE     NEAR LAB  _DERASE_FAIL
      01007333H   SYMBOL    CODE     NEAR LAB  _PERASE_FAIL
      01007353H   SYMBOL    CODE     NEAR LAB  _PROG_FAIL
      0100DFF9H   SYMBOL    CODE     BYTE      FLASH_ERASE
      0100DFF3H   SYMBOL    CODE     BYTE      FLASH_ERASE_ABORT
      0100DFF6H   SYMBOL    CODE     BYTE      FLASH_PROG
      0100DB06H   SYMBOL    CODE     BYTE      FLASH_PROTECT
      0100DFF0H   SYMBOL    CODE     BYTE      FLASH_READ_STATUS

      010072E1H   BLOCK     CODE     NEAR LAB  LVL=0
      010072E1H   LINE      CODE     ---       #61
      010072E3H   LINE      CODE     ---       #62
      010072E5H   LINE      CODE     ---       #64
      010072E7H   LINE      CODE     ---       #65
      010072E9H   LINE      CODE     ---       #67
      010072EAH   LINE      CODE     ---       #68
      010072ECH   LINE      CODE     ---       #69
      010072EEH   LINE      CODE     ---       #70
      010072F0H   LINE      CODE     ---       #72
      010072F2H   LINE      CODE     ---       #73
      010072F4H   LINE      CODE     ---       #74
      010072F6H   LINE      CODE     ---       #84
      010072F9H   LINE      CODE     ---       #87
      010072FCH   LINE      CODE     ---       #88
      010072FEH   LINE      CODE     ---       #89
      01007300H   LINE      CODE     ---       #90
      01007302H   LINE      CODE     ---       #91
      01007304H   LINE      CODE     ---       #92
      01007305H   LINE      CODE     ---       #93
      01007306H   LINE      CODE     ---       #95
      01007308H   LINE      CODE     ---       #96
      0100730AH   LINE      CODE     ---       #97
      0100730CH   LINE      CODE     ---       #98
      0100730DH   LINE      CODE     ---       #99
      ---         BLOCKEND  ---      ---       LVL=0

      0100730EH   BLOCK     CODE     NEAR LAB  LVL=0
      0100730EH   LINE      CODE     ---       #134
      01007310H   LINE      CODE     ---       #135
      01007312H   LINE      CODE     ---       #136
      01007314H   LINE      CODE     ---       #138
      01007315H   LINE      CODE     ---       #139
      01007317H   LINE      CODE     ---       #140
      01007319H   LINE      CODE     ---       #141
      0100731BH   LINE      CODE     ---       #142
      0100731DH   LINE      CODE     ---       #143
      0100731FH   LINE      CODE     ---       #144
      01007321H   LINE      CODE     ---       #145
      01007323H   LINE      CODE     ---       #155
      01007326H   LINE      CODE     ---       #159
      01007329H   LINE      CODE     ---       #160
      0100732BH   LINE      CODE     ---       #161
      0100732DH   LINE      CODE     ---       #162
      0100732FH   LINE      CODE     ---       #163
      01007331H   LINE      CODE     ---       #164
      01007332H   LINE      CODE     ---       #165
      01007333H   LINE      CODE     ---       #167
      01007335H   LINE      CODE     ---       #168
      01007337H   LINE      CODE     ---       #169
      01007339H   LINE      CODE     ---       #170
      0100733AH   LINE      CODE     ---       #171
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.58                                                             07/13/2013  20:09:09  PAGE 40


      0100733BH   BLOCK     CODE     NEAR LAB  LVL=0
      0100733BH   LINE      CODE     ---       #192
      0100733DH   LINE      CODE     ---       #200
      0100733FH   LINE      CODE     ---       #201
      01007341H   LINE      CODE     ---       #202
      01007343H   LINE      CODE     ---       #212
      01007346H   LINE      CODE     ---       #215
      01007349H   LINE      CODE     ---       #216
      0100734BH   LINE      CODE     ---       #217
      0100734DH   LINE      CODE     ---       #218
      0100734FH   LINE      CODE     ---       #219
      01007351H   LINE      CODE     ---       #220
      01007352H   LINE      CODE     ---       #221
      01007353H   LINE      CODE     ---       #223
      01007355H   LINE      CODE     ---       #224
      01007357H   LINE      CODE     ---       #225
      01007359H   LINE      CODE     ---       #226
      0100735AH   LINE      CODE     ---       #227
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C_INIT
      01007099H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?CSTPTR
      010070F0H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      01007102H   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?ILDIX
      01007124H   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULCMP
      0100713AH   PUBLIC    CODE     ---       ?C?ULCMP

      ---         MODULE    ---      ---       ?C?ULSHR
      0100714BH   PUBLIC    CODE     ---       ?C?ULSHR

      ---         MODULE    ---      ---       ?C?LLDPTR
      0100715EH   PUBLIC    CODE     ---       ?C?LLDPTR

      ---         MODULE    ---      ---       ?C?LSTXDATA
      0100717EH   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      0100718AH   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?COPY517
      01007287H   PUBLIC    CODE     ---       ?C?COPY517

      ---         MODULE    ---      ---       ?C?LLDIDATA
      010072ADH   PUBLIC    CODE     ---       ?C?LLDIDATA

      ---         MODULE    ---      ---       ?C?LLDXDATA
      010072B9H   PUBLIC    CODE     ---       ?C?LLDXDATA

      ---         MODULE    ---      ---       ?C?LLDPDATA
      010072C5H   PUBLIC    CODE     ---       ?C?LLDPDATA

      ---         MODULE    ---      ---       ?C?LLDCODE
      010072D1H   PUBLIC    CODE     ---       ?C?LLDCODE

Program Size: data=89.0 xdata=38 const=16 code=3084
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>LX51 Static Call Analysis</title>
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<style type="text/css">
<!--
p            { font-family: Verdana; font-size: 8pt; margin-top: 2; margin-bottom: 2 }
.LI2         { margin-top: 2; margin-bottom: 2 }
.Release     { background-color: #CCCCCC; font-weight: bold; padding: 6 }
.ToolT       { font-size: 8pt; color: #808080 }
.TinyT       { font-size: 8pt; text-align: Center }
ul           { font-family: Verdana; font-size: 8pt; list-style-type: square;  margin-top: 2; margin-bottom: 2 }
ol           { font-family: Verdana; font-size: 8pt }
h1           { font-family: Verdana; font-size: 14pt; color: #000080; font-weight: bold; text-align: Center; margin-right: 3 }
h2           { font-family: Verdana; font-size: 8pt; color: #000080; font-weight: bold; margin-top: 12; margin-bottom: 3; background-color: #CCCCCC; padding: 6 }
h3           { font-family: Verdana; font-size: 8pt; font-weight: bold; margin-top: 12; margin-bottom: 3; background-color: #CCCCCC; padding: 6 }
pre          { font-family: Courier New; font-size: 8pt; background-color: #CCFFCC; margin-left: 24; margin-right: 24 }
li           { margin-top: 3; margin-bottom: 0 }
-->
</style>
</head>
<body>
<h1>Static Call Analysis for<br>bootload (?C_STARTUP)</h1>

<p class="TinyT">
Created from: <b>LX51 LINKER/LOCATER V4.58</b><br>
Creation time: <b>07/13/2013  20:12:29</b><p>

<hr>


<h2><a name="S2"></a>?PR?MAIN?MAIN (C:0021DBH - C:0021ECH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=18 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S5"></a>?PR?MAIN_VINIT?MAIN (C:002447H - C:00245BH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S6"></a>?PR?CAN_VINIT?CAN (C:00245CH - C:002593H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S50">?L?COM0018</a>) Top=4 Bytes (<a href="#S5">?PR?MAIN_VINIT?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S40">?L?COM0001</a></li>
<li><a href="#S47">?L?COM0013</a></li>
<li><a href="#S48">?L?COM0014</a></li>
<li><a href="#S50">?L?COM0018</a></li>
<li><a href="#S52">?L?COM001A</a></li>
<li><a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a></li>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S7"></a>?PR?_CAN_VWRITEAMDATA?CAN (C:002594H - C:0025B9H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S36">?C?LIB_CODE</a>) Top=10 Bytes (<a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S36">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S40">?L?COM0001</a></li>
<li><a href="#S48">?L?COM0014</a></li>
<li><a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a></li>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S9"></a>?PR?_CAN_VSETLISTCOMMAND?CAN (C:0025BAH - C:0025C9H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a>) Top=8 Bytes (<a href="#S50">?L?COM0018</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S50">?L?COM0018</a></li>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S10"></a>?PR?_CAN_UBREQUESTMSGOBJ?CAN (C:0025CAH - C:00260BH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S42">?L?COM000A</a>) Top=8 Bytes (<a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S42">?L?COM000A</a></li>
<li><a href="#S44">?L?COM000E</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<h2><a name="S11"></a>?PR?_CAN_VTRANSMIT?CAN (C:002693H - C:0026C6H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S42">?L?COM000A</a>) Top=8 Bytes (<a href="#S46">?L?COM0011</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S42">?L?COM000A</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S46">?L?COM0011</a></li>
</ul>

<h2><a name="S12"></a>?PR?_CAN_VLOADDATA?CAN (C:00271DH - C:00278CH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S45">?L?COM0010</a>) Top=8 Bytes (<a href="#S46">?L?COM0011</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S42">?L?COM000A</a></li>
<li><a href="#S45">?L?COM0010</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S46">?L?COM0011</a></li>
</ul>

<h2><a name="S13"></a>?PR?CAN_SETWDTRESET?BOOT (C:00278DH - C:0027A9H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S18">?PR?FLASH_WAIT?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S18">?PR?FLASH_WAIT?BOOT</a></li>
</ul>

<h2><a name="S14"></a>?PR?_CAN_WAITTRANSMIT?BOOT (C:00260CH - C:00265CH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a>) Top=6 Bytes (<a href="#S19">?PR?_FLASHREAD?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S36">?C?LIB_CODE</a></li>
<li><a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S19">?PR?_FLASHREAD?BOOT</a></li>
<li><a href="#S26">?PR?DFLASHREAD?BOOT</a></li>
</ul>

<h2><a name="S16"></a>?PR?_CAN_SENDACK?BOOT (C:00265DH - C:002692H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=14 Bytes (<a href="#S46">?L?COM0011</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S46">?L?COM0011</a></li>
<li><a href="#S54">?L?COM001C</a></li>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S53">?L?COM001B</a></li>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S18"></a>?PR?FLASH_WAIT?BOOT (C:0027AAH - C:0027DBH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S34">?PR?_DFLERASE?XC88X_FLHANDLER</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S34">?PR?_DFLERASE?XC88X_FLHANDLER</a></li>
<li><a href="#S13">?PR?CAN_SETWDTRESET?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S51">?L?COM0019</a></li>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S19"></a>?PR?_FLASHREAD?BOOT (C:002800H - C:00288FH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=14 Bytes (<a href="#S46">?L?COM0011</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S41">?L?COM0007</a></li>
<li><a href="#S46">?L?COM0011</a></li>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S21"></a>?PR?_CAN_READFIFO?BOOT (C:002ADFH - C:002BABH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S42">?L?COM000A</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S36">?C?LIB_CODE</a></li>
<li><a href="#S42">?L?COM000A</a></li>
<li><a href="#S44">?L?COM000E</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S24"></a>?PR?CHECKNULL?BOOT (C:0027DCH - C:0027F8H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S25"></a>?PR?_CHECKFLPROG?BOOT (C:002BACH - C:002BD2H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S26"></a>?PR?DFLASHREAD?BOOT (C:0026C7H - C:00271CH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=14 Bytes (<a href="#S46">?L?COM0011</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S41">?L?COM0007</a></li>
<li><a href="#S46">?L?COM0011</a></li>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S28"></a>?PR?BOOTMAIN?BOOT (C:002890H - C:002ADEH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=16 Bytes (<a href="#S16">?PR?_CAN_SENDACK?BOOT</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S43">?L?COM000D</a></li>
<li><a href="#S49">?L?COM0016</a></li>
<li><a href="#S51">?L?COM0019</a></li>
<li><a href="#S53">?L?COM001B</a></li>
<li><a href="#S54">?L?COM001C</a></li>
<li><a href="#S21">?PR?_CAN_READFIFO?BOOT</a></li>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S25">?PR?_CHECKFLPROG?BOOT</a></li>
<li><a href="#S34">?PR?_DFLERASE?XC88X_FLHANDLER</a></li>
<li><a href="#S19">?PR?_FLASHREAD?BOOT</a></li>
<li><a href="#S13">?PR?CAN_SETWDTRESET?BOOT</a></li>
<li><a href="#S24">?PR?CHECKNULL?BOOT</a></li>
<li><a href="#S26">?PR?DFLASHREAD?BOOT</a></li>
<li><a href="#S18">?PR?FLASH_WAIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S34"></a>?PR?_DFLERASE?XC88X_FLHANDLER (C:0023DEH - C:00240AH)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S18">?PR?FLASH_WAIT?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S18">?PR?FLASH_WAIT?BOOT</a></li>
</ul>

<h2><a name="S35"></a>?PR?_FLPROG?XC88X_FLHANDLER (C:00240BH - C:00242AH)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S51">?L?COM0019</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S51">?L?COM0019</a></li>
</ul>

<h2><a name="S36"></a>?C?LIB_CODE (C:0021EDH - C:0023DDH)</h2>
<p><br><b>Maximum Stack:</b> Top=18 Bytes (<a href="#S45">?L?COM0010</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?L?COM0007</a></li>
<li><a href="#S45">?L?COM0010</a></li>
<li><a href="#S54">?L?COM001C</a></li>
<li><a href="#S21">?PR?_CAN_READFIFO?BOOT</a></li>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<h2><a name="S40"></a>?L?COM0001 (C:002BD3H - C:002C0BH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a>) Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S41"></a>?L?COM0007 (C:002C0CH - C:002C1EH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S36">?C?LIB_CODE</a>) Top=6 Bytes (<a href="#S19">?PR?_FLASHREAD?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S36">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S19">?PR?_FLASHREAD?BOOT</a></li>
<li><a href="#S26">?PR?DFLASHREAD?BOOT</a></li>
</ul>

<h2><a name="S42"></a>?L?COM000A (C:002C36H - C:002C4AH)</h2>
<p><br><b>Maximum Stack:</b> Top=16 Bytes (<a href="#S12">?PR?_CAN_VLOADDATA?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S21">?PR?_CAN_READFIFO?BOOT</a></li>
<li><a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a></li>
<li><a href="#S12">?PR?_CAN_VLOADDATA?CAN</a></li>
<li><a href="#S11">?PR?_CAN_VTRANSMIT?CAN</a></li>
</ul>

<h2><a name="S43"></a>?L?COM000D (C:002C1FH - C:002C26H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S44"></a>?L?COM000E (C:002C27H - C:002C35H)</h2>
<p><br><b>Maximum Stack:</b> Top=16 Bytes (<a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S21">?PR?_CAN_READFIFO?BOOT</a></li>
<li><a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a></li>
</ul>

<h2><a name="S45"></a>?L?COM0010 (C:002C74H - C:002C8BH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S36">?C?LIB_CODE</a>) Top=16 Bytes (<a href="#S12">?PR?_CAN_VLOADDATA?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S36">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S12">?PR?_CAN_VLOADDATA?CAN</a></li>
</ul>

<h2><a name="S46"></a>?L?COM0011 (C:002C4BH - C:002C57H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=12 Bytes (<a href="#S12">?PR?_CAN_VLOADDATA?CAN</a>) Top=6 Bytes (<a href="#S16">?PR?_CAN_SENDACK?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S12">?PR?_CAN_VLOADDATA?CAN</a></li>
<li><a href="#S11">?PR?_CAN_VTRANSMIT?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S19">?PR?_FLASHREAD?BOOT</a></li>
<li><a href="#S26">?PR?DFLASHREAD?BOOT</a></li>
</ul>

<h2><a name="S47"></a>?L?COM0013 (C:002C8CH - C:002C94H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S48"></a>?L?COM0014 (C:002C95H - C:002CA3H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a>) Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S49"></a>?L?COM0016 (C:002C60H - C:002C6CH)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S50"></a>?L?COM0018 (C:002CA4H - C:002CABH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=6 Bytes (<a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a>) Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S51"></a>?L?COM0019 (C:002C58H - C:002C5FH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S35">?PR?_FLPROG?XC88X_FLHANDLER</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S35">?PR?_FLPROG?XC88X_FLHANDLER</a></li>
<li><a href="#S18">?PR?FLASH_WAIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S52"></a>?L?COM001A (C:002CACH - C:002CB4H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S53"></a>?L?COM001B (C:0027F9H - C:0027FEH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=14 Bytes (<a href="#S16">?PR?_CAN_SENDACK?BOOT</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S54"></a>?L?COM001C (C:002C6DH - C:002C73H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S16">?PR?_CAN_SENDACK?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S36">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

</body>
</html>

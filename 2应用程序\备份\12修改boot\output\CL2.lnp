".\output\START_XC.obj",
".\output\MAIN.obj",
".\output\SSC.obj",
".\output\IO.obj",
".\output\SHARED_INT.obj",
".\output\CAN.obj",
".\output\T2.obj",
".\output\WDT.obj",
".\output\SwDriver.obj",
".\output\User.obj",
".\output\boot.obj",
".\output\XC88x_FLHANDLER.obj" 
TO ".\output\CL2.0" 

CODE(".\output\CL2.cod") 
PRINT(".\output\CL2.map") IXREF 
RESERVE (I:0xC0-I:0xFF) 
CLASSES (XDATA (X:0xF000-X:0xF5FF), HDATA (X:0xF000-X:0xF5FF) , 
CODE(C:0x2100-C:0x5FFF)) 
SEGMENTS ( ?C_C51STARTUP(C:0x2100), ?PR?main?MAIN ) 

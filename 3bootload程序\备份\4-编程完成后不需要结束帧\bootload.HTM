<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<title>LX51 Static Call Analysis</title>
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<style type="text/css">
<!--
p            { font-family: Verdana; font-size: 8pt; margin-top: 2; margin-bottom: 2 }
.LI2         { margin-top: 2; margin-bottom: 2 }
.Release     { background-color: #CCCCCC; font-weight: bold; padding: 6 }
.ToolT       { font-size: 8pt; color: #808080 }
.TinyT       { font-size: 8pt; text-align: Center }
ul           { font-family: Verdana; font-size: 8pt; list-style-type: square;  margin-top: 2; margin-bottom: 2 }
ol           { font-family: Verdana; font-size: 8pt }
h1           { font-family: Verdana; font-size: 14pt; color: #000080; font-weight: bold; text-align: Center; margin-right: 3 }
h2           { font-family: Verdana; font-size: 8pt; color: #000080; font-weight: bold; margin-top: 12; margin-bottom: 3; background-color: #CCCCCC; padding: 6 }
h3           { font-family: Verdana; font-size: 8pt; font-weight: bold; margin-top: 12; margin-bottom: 3; background-color: #CCCCCC; padding: 6 }
pre          { font-family: Courier New; font-size: 8pt; background-color: #CCFFCC; margin-left: 24; margin-right: 24 }
li           { margin-top: 3; margin-bottom: 0 }
-->
</style>
</head>
<body>
<h1>Static Call Analysis for<br>bootload (?C_STARTUP)</h1>

<p class="TinyT">
Created from: <b>LX51 LINKER/LOCATER V4.58</b><br>
Creation time: <b>07/07/2013  18:32:47</b><p>

<hr>


<h2><a name="S2"></a>?PR?MAIN?MAIN (C:0070DBH - C:0070ECH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=20 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S5"></a>?PR?MAIN_VINIT?MAIN (C:007374H - C:007388H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S6"></a>?PR?CAN_VINIT?CAN (C:007389H - C:0074B9H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S53">?L?COM0018</a>) Top=4 Bytes (<a href="#S5">?PR?MAIN_VINIT?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S41">?L?COM0001</a></li>
<li><a href="#S49">?L?COM0013</a></li>
<li><a href="#S51">?L?COM0015</a></li>
<li><a href="#S53">?L?COM0018</a></li>
<li><a href="#S56">?L?COM001C</a></li>
<li><a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a></li>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S5">?PR?MAIN_VINIT?MAIN</a></li>
</ul>

<h2><a name="S7"></a>?PR?_CAN_VWRITEAMDATA?CAN (C:0074BAH - C:0074DFH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S37">?C?LIB_CODE</a>) Top=10 Bytes (<a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S37">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S41">?L?COM0001</a></li>
<li><a href="#S51">?L?COM0015</a></li>
<li><a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a></li>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S9"></a>?PR?_CAN_VSETLISTCOMMAND?CAN (C:0074E0H - C:0074EFH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a>) Top=8 Bytes (<a href="#S53">?L?COM0018</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S53">?L?COM0018</a></li>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S10"></a>?PR?_CAN_UBREQUESTMSGOBJ?CAN (C:0074F0H - C:007531H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S43">?L?COM000A</a>) Top=10 Bytes (<a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S43">?L?COM000A</a></li>
<li><a href="#S46">?L?COM000F</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<h2><a name="S11"></a>?PR?_CAN_VTRANSMIT?CAN (C:0076D7H - C:00770AH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=8 Bytes (<a href="#S43">?L?COM000A</a>) Top=10 Bytes (<a href="#S48">?L?COM0012</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S43">?L?COM000A</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S48">?L?COM0012</a></li>
</ul>

<h2><a name="S12"></a>?PR?_CAN_VLOADDATA?CAN (C:00770BH - C:00777AH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S47">?L?COM0011</a>) Top=10 Bytes (<a href="#S48">?L?COM0012</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S43">?L?COM000A</a></li>
<li><a href="#S47">?L?COM0011</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S48">?L?COM0012</a></li>
</ul>

<h2><a name="S13"></a>?PR?CAN_SETWDTRESET?BOOT (C:0076BAH - C:0076D6H)</h2>
<p><br><b>Maximum Stack:</b> Top=2 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S14"></a>?PR?_CAN_WAITTRANSMIT?BOOT (C:007532H - C:007582H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=10 Bytes (<a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a>) Top=8 Bytes (<a href="#S26">?PR?DFLASHREAD?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S37">?C?LIB_CODE</a></li>
<li><a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S19">?PR?_FLASHREAD?BOOT</a></li>
<li><a href="#S26">?PR?DFLASHREAD?BOOT</a></li>
</ul>

<h2><a name="S16"></a>?PR?_CAN_SENDACK?BOOT (C:007583H - C:0075B6H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=14 Bytes (<a href="#S48">?L?COM0012</a>) Top=6 Bytes (<a href="#S44">?L?COM000D</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S48">?L?COM0012</a></li>
<li><a href="#S57">?L?COM001E</a></li>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S44">?L?COM000D</a></li>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S18"></a>?PR?FLASH_WAIT?BOOT (C:007693H - C:0076B9H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S36">?PR?_FLPROG?XC88X_FLHANDLER</a>) Top=6 Bytes (<a href="#S50">?L?COM0014</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S36">?PR?_FLPROG?XC88X_FLHANDLER</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S44">?L?COM000D</a></li>
<li><a href="#S50">?L?COM0014</a></li>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S19"></a>?PR?_FLASHREAD?BOOT (C:007604H - C:007692H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=14 Bytes (<a href="#S48">?L?COM0012</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S42">?L?COM0007</a></li>
<li><a href="#S48">?L?COM0012</a></li>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S21"></a>?PR?_CAN_READFIFO?BOOT (C:007A48H - C:007B14H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S43">?L?COM000A</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S37">?C?LIB_CODE</a></li>
<li><a href="#S43">?L?COM000A</a></li>
<li><a href="#S46">?L?COM000F</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S24"></a>?PR?CHECKNULL?BOOT (C:00777BH - C:007797H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S25"></a>?PR?_CHECKFLPROG?BOOT (C:007798H - C:0077BEH)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S26"></a>?PR?DFLASHREAD?BOOT (C:0075B7H - C:007603H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=14 Bytes (<a href="#S48">?L?COM0012</a>) Top=6 Bytes (<a href="#S44">?L?COM000D</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S42">?L?COM0007</a></li>
<li><a href="#S48">?L?COM0012</a></li>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S44">?L?COM000D</a></li>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S28"></a>?PR?BOOTMAIN?BOOT (C:007800H - C:007A47H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=18 Bytes (<a href="#S44">?L?COM000D</a>) Top=2 Bytes (<a href="#S2">?PR?MAIN?MAIN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S44">?L?COM000D</a></li>
<li><a href="#S45">?L?COM000E</a></li>
<li><a href="#S50">?L?COM0014</a></li>
<li><a href="#S52">?L?COM0017</a></li>
<li><a href="#S54">?L?COM001A</a></li>
<li><a href="#S55">?L?COM001B</a></li>
<li><a href="#S57">?L?COM001E</a></li>
<li><a href="#S21">?PR?_CAN_READFIFO?BOOT</a></li>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S25">?PR?_CHECKFLPROG?BOOT</a></li>
<li><a href="#S19">?PR?_FLASHREAD?BOOT</a></li>
<li><a href="#S13">?PR?CAN_SETWDTRESET?BOOT</a></li>
<li><a href="#S24">?PR?CHECKNULL?BOOT</a></li>
<li><a href="#S26">?PR?DFLASHREAD?BOOT</a></li>
<li><a href="#S18">?PR?FLASH_WAIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S2">?PR?MAIN?MAIN</a></li>
</ul>

<h2><a name="S34"></a>?PR?_DFLERASE?XC88X_FLHANDLER (C:0072DEH - C:00730AH)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S44">?L?COM000D</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S44">?L?COM000D</a></li>
</ul>

<h2><a name="S35"></a>?PR?_PFLERASE?XC88X_FLHANDLER (C:00730BH - C:007337H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S50">?L?COM0014</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S50">?L?COM0014</a></li>
</ul>

<h2><a name="S36"></a>?PR?_FLPROG?XC88X_FLHANDLER (C:007338H - C:007357H)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S18">?PR?FLASH_WAIT?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S18">?PR?FLASH_WAIT?BOOT</a></li>
</ul>

<h2><a name="S37"></a>?C?LIB_CODE (C:0070EDH - C:0072DDH)</h2>
<p><br><b>Maximum Stack:</b> Top=20 Bytes (<a href="#S47">?L?COM0011</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S42">?L?COM0007</a></li>
<li><a href="#S47">?L?COM0011</a></li>
<li><a href="#S57">?L?COM001E</a></li>
<li><a href="#S21">?PR?_CAN_READFIFO?BOOT</a></li>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
<li><a href="#S14">?PR?_CAN_WAITTRANSMIT?BOOT</a></li>
</ul>

<h2><a name="S41"></a>?L?COM0001 (C:007B15H - C:007B4DH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a>) Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S42"></a>?L?COM0007 (C:0077C7H - C:0077D8H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S37">?C?LIB_CODE</a>) Top=8 Bytes (<a href="#S26">?PR?DFLASHREAD?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S37">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S19">?PR?_FLASHREAD?BOOT</a></li>
<li><a href="#S26">?PR?DFLASHREAD?BOOT</a></li>
</ul>

<h2><a name="S43"></a>?L?COM000A (C:007B4EH - C:007B62H)</h2>
<p><br><b>Maximum Stack:</b> Top=18 Bytes (<a href="#S12">?PR?_CAN_VLOADDATA?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S21">?PR?_CAN_READFIFO?BOOT</a></li>
<li><a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a></li>
<li><a href="#S12">?PR?_CAN_VLOADDATA?CAN</a></li>
<li><a href="#S11">?PR?_CAN_VTRANSMIT?CAN</a></li>
</ul>

<h2><a name="S44"></a>?L?COM000D (C:007BA4H - C:007BD5H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=16 Bytes (<a href="#S16">?PR?_CAN_SENDACK?BOOT</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S34">?PR?_DFLERASE?XC88X_FLHANDLER</a></li>
<li><a href="#S26">?PR?DFLASHREAD?BOOT</a></li>
<li><a href="#S18">?PR?FLASH_WAIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S45"></a>?L?COM000E (C:0077BFH - C:0077C6H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S46"></a>?L?COM000F (C:007B63H - C:007B71H)</h2>
<p><br><b>Maximum Stack:</b> Top=18 Bytes (<a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S21">?PR?_CAN_READFIFO?BOOT</a></li>
<li><a href="#S10">?PR?_CAN_UBREQUESTMSGOBJ?CAN</a></li>
</ul>

<h2><a name="S47"></a>?L?COM0011 (C:007BD6H - C:007BEDH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=2 Bytes (<a href="#S37">?C?LIB_CODE</a>) Top=18 Bytes (<a href="#S12">?PR?_CAN_VLOADDATA?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S37">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S12">?PR?_CAN_VLOADDATA?CAN</a></li>
</ul>

<h2><a name="S48"></a>?L?COM0012 (C:0077D9H - C:0077E3H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=12 Bytes (<a href="#S12">?PR?_CAN_VLOADDATA?CAN</a>) Top=8 Bytes (<a href="#S16">?PR?_CAN_SENDACK?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S12">?PR?_CAN_VLOADDATA?CAN</a></li>
<li><a href="#S11">?PR?_CAN_VTRANSMIT?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S19">?PR?_FLASHREAD?BOOT</a></li>
<li><a href="#S26">?PR?DFLASHREAD?BOOT</a></li>
</ul>

<h2><a name="S49"></a>?L?COM0013 (C:0077EBH - C:0077F3H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S50"></a>?L?COM0014 (C:007B91H - C:007BA3H)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S18">?PR?FLASH_WAIT?BOOT</a>) Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S35">?PR?_PFLERASE?XC88X_FLHANDLER</a></li>
<li><a href="#S18">?PR?FLASH_WAIT?BOOT</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S51"></a>?L?COM0015 (C:007BEEH - C:007BFCH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=4 Bytes (<a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a>) Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S7">?PR?_CAN_VWRITEAMDATA?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S52"></a>?L?COM0017 (C:007B84H - C:007B90H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S53"></a>?L?COM0018 (C:0077E4H - C:0077EAH)</h2>
<p><br><b>Maximum Stack:</b> Bottom=6 Bytes (<a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a>) Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S9">?PR?_CAN_VSETLISTCOMMAND?CAN</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S54"></a>?L?COM001A (C:007B72H - C:007B7AH)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S55"></a>?L?COM001B (C:007B7BH - C:007B83H)</h2>
<p><br><b>Maximum Stack:</b> Top=4 Bytes (<a href="#S28">?PR?BOOTMAIN?BOOT</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

<h2><a name="S56"></a>?L?COM001C (C:007BFDH - C:007C05H)</h2>
<p><br><b>Maximum Stack:</b> Top=6 Bytes (<a href="#S6">?PR?CAN_VINIT?CAN</a>)<p>
<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S6">?PR?CAN_VINIT?CAN</a></li>
</ul>

<h2><a name="S57"></a>?L?COM001E (C:0077F4H - C:0077F9H)</h2>
<p><br><b>Maximum Stack:</b> Top=8 Bytes (<a href="#S16">?PR?_CAN_SENDACK?BOOT</a>)<p>
<p><br><b>Calls to:</b></p>
<ul>
<li><a href="#S37">?C?LIB_CODE</a></li>
</ul>

<p><br><b>Called from:</b></p>
<ul>
<li><a href="#S16">?PR?_CAN_SENDACK?BOOT</a></li>
<li><a href="#S28">?PR?BOOTMAIN?BOOT</a></li>
</ul>

</body>
</html>

BL51 BANKED LINKER/LOCATER V6.22                                                        07/04/2013  17:36:08  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
C:\KEIL\C51\BIN\BL51.EXE .\output\START_XC.obj, .\output\MAIN.obj, .\output\SSC.obj, .\output\IO.obj, .\output\SHARED_IN
>> T.obj, .\output\CAN.obj, .\output\T2.obj, .\output\WDT.obj, .\output\SwDriver.obj, .\output\User.obj, .\output\boot.o
>> bj TO .\output\CL2.0 PRINT (.\output\CL2.m51) RAMSIZE (256) XDATA (0XF000-0XF5FF)


MEMORY MODEL: LARGE


INPUT MODULES INCLUDED:
  .\output\START_XC.obj (?C_STARTUP)
  .\output\MAIN.obj (MAIN)
  .\output\SSC.obj (SSC)
  .\output\IO.obj (IO)
  .\output\SHARED_INT.obj (SHARED_INT)
  .\output\CAN.obj (CAN)
  .\output\T2.obj (T2)
  .\output\WDT.obj (WDT)
  .\output\SwDriver.obj (SWDRIVER)
  .\output\User.obj (USER)
  .\output\boot.obj (BOOT)
  C:\KEIL\C51\LIB\C51L.LIB (?C_INIT)
  C:\KEIL\C51\LIB\C51L.LIB (?C?CLDPTR)
  C:\KEIL\C51\LIB\C51L.LIB (?C?CLDOPTR)
  C:\KEIL\C51\LIB\C51L.LIB (?C?CILDOPTR)
  C:\KEIL\C51\LIB\C51L.LIB (?C?CSTPTR)
  C:\KEIL\C51\LIB\C51L.LIB (?C?CSTOPTR)
  C:\KEIL\C51\LIB\C51L.LIB (?C?ULCMP)
  C:\KEIL\C51\LIB\C51L.LIB (?C?ULSHR)
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDPTR)
  C:\KEIL\C51\LIB\C51L.LIB (?C?LSTXDATA)
  C:\KEIL\C51\LIB\C51L.LIB (?C?LSTKXDATA)
  C:\KEIL\C51\LIB\C51L.LIB (?C?OFFXADD)
  C:\KEIL\C51\LIB\C51L.LIB (?C?ICALL)
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDIDATA)
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDXDATA)
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDPDATA)
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDCODE)


LINK MAP OF MODULE:  .\output\CL2.0 (?C_STARTUP)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
                    0008H     0018H                  *** GAP ***
            BIT     0020H.0   0000H.4   UNIT         ?BI?USER
            BIT     0020H.4   0000H.2   UNIT         _BIT_GROUP_
                    0020H.6   0000H.2                *** GAP ***
            IDATA   0021H     0001H     UNIT         ?STACK

            * * * * * * *  X D A T A   M E M O R Y  * * * * * * *
                    0000H     F000H                  *** GAP ***
            XDATA   F000H     002AH     UNIT         ?XD?USER
            XDATA   F02AH     0028H     UNIT         _XDATA_GROUP_
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 2


            XDATA   F052H     001CH     UNIT         ?XD?SWDRIVER

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0027H     UNIT         ?PR?MAIN_VINIT?MAIN
                    002AH     0001H                  *** GAP ***
            CODE    002BH     0003H     ABSOLUTE     
            CODE    002EH     0574H     UNIT         ?PR?_LEDLIGHT?SWDRIVER
            CODE    05A2H     0540H     UNIT         ?PR?SWSAMPLE?USER
            CODE    0AE2H     0383H     UNIT         ?PR?_MC33993CMDSENDONEBIT?SWDRIVER
            CODE    0E65H     030FH     UNIT         ?PR?CANTX?USER
            CODE    1174H     02AAH     UNIT         ?PR?_CAN_VGETMSGOBJ?CAN
            CODE    141EH     01ACH     UNIT         ?PR?CAN_VINIT?CAN
            CODE    15CAH     01A3H     UNIT         ?PR?_MC33993CMDSEND?SWDRIVER
            CODE    176DH     0199H     UNIT         ?PR?LEDDR?USER
            CODE    1906H     0174H     UNIT         ?C?LIB_CODE
            CODE    1A7AH     015DH     UNIT         ?PR?INITMC33993SEC?SWDRIVER
            CODE    1BD7H     0123H     UNIT         ?PR?_SWSTASAMPLE?SWDRIVER
            CODE    1CFAH     00E1H     UNIT         ?PR?PASSWORD?USER
            CODE    1DDBH     00DBH     UNIT         ?C_C51STARTUP
            CODE    1EB6H     00BDH     UNIT         ?PR?INITSW?SWDRIVER
            CODE    1F73H     00A5H     UNIT         ?PR?_CAN_VLOADDATA?CAN
            CODE    2018H     007FH     UNIT         ?PR?COPY?BOOT
            CODE    2097H     007EH     UNIT         ?PR?BOOTMAIN?BOOT
            CODE    2115H     007CH     UNIT         ?PR?CANERRORPROCESS?USER
            CODE    2191H     007AH     UNIT         ?PR?CANRXTX?USER
            CODE    220BH     006DH     UNIT         ?C_INITSEG
            CODE    2278H     005AH     UNIT         ?PR?SHINT_VIXINTR5ISR?SHARED_INT
            CODE    22D2H     0044H     UNIT         ?PR?CANTRANSMIT?USER
            CODE    2316H     0041H     UNIT         ?PR?_CAN_UBNEWDATA?CAN
            CODE    2357H     0041H     UNIT         ?PR?_CAN_VRELEASEOBJ?CAN
            CODE    2398H     0033H     UNIT         ?PR?_MC33993DATASENDRCV?SWDRIVER
            CODE    23CBH     0033H     UNIT         ?PR?RTCPROCESS?USER
            CODE    23FEH     0032H     UNIT         ?PR?_CAN_VWRITEAMDATA?CAN
            CODE    2430H     0024H     UNIT         ?PR?SSC_VINIT?SSC
            CODE    2454H     0024H     UNIT         ?PR?_CAN_VTRANSMIT?CAN
            CODE    2478H     0021H     UNIT         ?PR?MAIN?MAIN
            CODE    2499H     001CH     UNIT         ?PR?WDT_VINIT?WDT
            CODE    24B5H     001BH     UNIT         ?PR?WDT_VDISABLE?WDT
            CODE    24D0H     0017H     UNIT         ?PR?_CAN_VSETLISTCOMMAND?CAN
            CODE    24E7H     0012H     UNIT         ?PR?T2_VINIT?T2
            CODE    24F9H     0011H     UNIT         ?PR?_CHANGECSMC33993?SWDRIVER
            CODE    250AH     0007H     UNIT         ?PR?IO_VINIT?IO
            CODE    2511H     0004H     UNIT         ?PR?SHINT_VINIT?SHARED_INT
            CODE    2515H     0003H     UNIT         ?PR?_SSC_VSENDDATA?SSC
            CODE    2518H     0003H     UNIT         ?PR?SSC_VGETDATA?SSC



OVERLAY MAP OF MODULE:   .\output\CL2.0 (?C_STARTUP)


SEGMENT                                       BIT_GROUP          XDATA_GROUP
  +--> CALLED SEGMENT                      START    LENGTH     START    LENGTH
------------------------------------------------------------------------------
?C_C51STARTUP                              -----    -----      -----    -----
  +--> ?PR?MAIN?MAIN
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 3


  +--> ?C_INITSEG

?PR?MAIN?MAIN                              -----    -----      -----    -----
  +--> ?PR?MAIN_VINIT?MAIN
  +--> ?PR?INITSW?SWDRIVER
  +--> ?PR?SWSAMPLE?USER
  +--> ?PR?CANRXTX?USER
  +--> ?PR?CANERRORPROCESS?USER
  +--> ?PR?BOOTMAIN?BOOT

?PR?MAIN_VINIT?MAIN                        -----    -----      -----    -----
  +--> ?PR?IO_VINIT?IO
  +--> ?PR?T2_VINIT?T2
  +--> ?PR?WDT_VINIT?WDT
  +--> ?PR?SSC_VINIT?SSC
  +--> ?PR?CAN_VINIT?CAN
  +--> ?PR?SHINT_VINIT?SHARED_INT

?PR?CAN_VINIT?CAN                          -----    -----      -----    -----
  +--> ?PR?_CAN_VSETLISTCOMMAND?CAN
  +--> ?PR?_CAN_VWRITEAMDATA?CAN

?PR?_CAN_VSETLISTCOMMAND?CAN               -----    -----      -----    -----
  +--> ?PR?_CAN_VWRITEAMDATA?CAN

?PR?_CAN_VWRITEAMDATA?CAN                  -----    -----      F02DH    0004H

?PR?INITSW?SWDRIVER                        -----    -----      F02AH    0001H
  +--> ?PR?_MC33993CMDSEND?SWDRIVER

?PR?_MC33993CMDSEND?SWDRIVER               -----    -----      F048H    000AH
  +--> ?PR?_CHANGECSMC33993?SWDRIVER
  +--> ?PR?_MC33993DATASENDRCV?SWDRIVER

?PR?_CHANGECSMC33993?SWDRIVER              0020H.5  0000H.1    -----    -----

?PR?_MC33993DATASENDRCV?SWDRIVER           -----    -----      -----    -----
  +--> ?PR?_SSC_VSENDDATA?SSC
  +--> ?PR?SSC_VGETDATA?SSC

?PR?SWSAMPLE?USER                          -----    -----      F02AH    0008H
  +--> ?PR?_SWSTASAMPLE?SWDRIVER
  +--> ?PR?CANTX?USER

?PR?_SWSTASAMPLE?SWDRIVER                  -----    -----      F032H    0008H
  +--> ?PR?INITMC33993SEC?SWDRIVER
  +--> ?PR?_MC33993CMDSEND?SWDRIVER

?PR?INITMC33993SEC?SWDRIVER                -----    -----      F03AH    0001H
  +--> ?PR?_MC33993CMDSEND?SWDRIVER

?PR?CANTX?USER                             -----    -----      -----    -----
  +--> ?PR?CANTRANSMIT?USER

?PR?CANTRANSMIT?USER                       -----    -----      -----    -----
  +--> ?PR?PASSWORD?USER
  +--> ?PR?_CAN_VLOADDATA?CAN
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 4


  +--> ?PR?_CAN_VTRANSMIT?CAN

?PR?CANRXTX?USER                           -----    -----      F02AH    0014H
  +--> ?PR?_CAN_UBNEWDATA?CAN
  +--> ?PR?_CAN_VGETMSGOBJ?CAN
  +--> ?PR?_CAN_VRELEASEOBJ?CAN
  +--> ?PR?LEDDR?USER
  +--> ?PR?CANTX?USER

?PR?_CAN_VGETMSGOBJ?CAN                    -----    -----      F046H    0004H

?PR?LEDDR?USER                             -----    -----      -----    -----
  +--> ?PR?_LEDLIGHT?SWDRIVER

?PR?_LEDLIGHT?SWDRIVER                     -----    -----      F03EH    0005H
  +--> ?PR?_MC33993CMDSENDONEBIT?SWDRIVER

?PR?_MC33993CMDSENDONEBIT?SWDRIVER         0020H.4  0000H.1    F043H    0005H
  +--> ?PR?_MC33993CMDSEND?SWDRIVER

?PR?CANERRORPROCESS?USER                   -----    -----      F02AH    0003H
  +--> ?PR?CAN_VINIT?CAN

?PR?BOOTMAIN?BOOT                          -----    -----      F02AH    001CH
  +--> ?PR?_CAN_UBNEWDATA?CAN
  +--> ?PR?_CAN_VGETMSGOBJ?CAN
  +--> ?PR?_CAN_VRELEASEOBJ?CAN
  +--> ?PR?WDT_VDISABLE?WDT
  +--> ?PR?COPY?BOOT

?PR?COPY?BOOT                              -----    -----      F046H    0008H

*** NEW ROOT ***************************************************

?PR?SHINT_VIXINTR5ISR?SHARED_INT           -----    -----      -----    -----
  +--> ?PR?RTCPROCESS?USER



SYMBOL TABLE OF MODULE:  .\output\CL2.0 (?C_STARTUP)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ?C_STARTUP
  C:1DDBH         SEGMENT       ?C_C51STARTUP
  I:0021H         SEGMENT       ?STACK
  D:00A2H         PUBLIC        ?C?DPSEL
  C:0000H         PUBLIC        ?C_STARTUP
  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  D:00BAH         SYMBOL        CMCON
  C:1DEEH         SYMBOL        DELAYXTAL
  C:1DECH         SYMBOL        DELAYXTAL0
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0000H         SYMBOL        IBPSTACK
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 5


  N:0100H         SYMBOL        IBPSTACKTOP
  N:0100H         SYMBOL        IDATALEN
  C:1E1FH         SYMBOL        IDATALOOP
  N:0001H         SYMBOL        LIN_BSL
  N:0001H         SYMBOL        LIN_NAC
  N:0001H         SYMBOL        LIN_NAD
  D:0096H         SYMBOL        MEX3
  N:000AH         SYMBOL        NDIV
  N:0002H         SYMBOL        NDIV_XC86X
  N:0018H         SYMBOL        NDIV_XC87X_PLL_CON
  N:0020H         SYMBOL        NDIV_XC87X_PLL_CON1
  N:000AH         SYMBOL        NDIV_XC88X
  N:0000H         SYMBOL        NR_XC87X
  N:0000H         SYMBOL        OD_XC87X
  C:1DF2H         SYMBOL        OSCR_NOTSET
  D:00B6H         SYMBOL        OSC_CON
  D:00BBH         SYMBOL        PASSWD
  N:0000H         SYMBOL        PBPSTACK
  N:0100H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:F000H         SYMBOL        PDATASTART
  D:00B7H         SYMBOL        PLL_CON
  D:00EAH         SYMBOL        PLL_CON1
  N:00F0H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00BFH         SYMBOL        SCU_PAGE
  D:0081H         SYMBOL        SP
  C:1DDBH         SYMBOL        STARTUP1
  C:1E0FH         SYMBOL        WAIT_LOCK
  D:00B3H         SYMBOL        XADDRH
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XC82X_CHIP
  N:0000H         SYMBOL        XC864_CHIP
  N:0000H         SYMBOL        XC866_CHIP
  N:0000H         SYMBOL        XC874_CHIP_13FF
  N:0000H         SYMBOL        XC874_CHIP_16FF
  N:0000H         SYMBOL        XC878_CHIP_13FF
  N:0000H         SYMBOL        XC878_CHIP_16FF
  N:0001H         SYMBOL        XC88X_CHIP
  N:0600H         SYMBOL        XDATALEN
  C:1E2AH         SYMBOL        XDATALOOP
  N:F000H         SYMBOL        XDATASTART
  N:0001H         SYMBOL        XTAL
  C:0000H         LINE#         312
  C:1DDBH         LINE#         367
  C:1DDEH         LINE#         368
  C:1DE1H         LINE#         369
  C:1DE4H         LINE#         370
  C:1DE7H         LINE#         371
  C:1DEAH         LINE#         373
  C:1DECH         LINE#         375
  C:1DEEH         LINE#         377
  C:1DF0H         LINE#         378
  C:1DF2H         LINE#         382
  C:1DF4H         LINE#         383
  C:1DF7H         LINE#         388
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 6


  C:1DF9H         LINE#         389
  C:1DFBH         LINE#         390
  C:1DFEH         LINE#         394
  C:1E01H         LINE#         395
  C:1E04H         LINE#         396
  C:1E07H         LINE#         397
  C:1E0AH         LINE#         400
  C:1E0DH         LINE#         401
  C:1E0FH         LINE#         409
  C:1E11H         LINE#         410
  C:1E13H         LINE#         411
  C:1E16H         LINE#         413
  C:1E19H         LINE#         414
  C:1E1CH         LINE#         419
  C:1E1EH         LINE#         420
  C:1E1FH         LINE#         421
  C:1E20H         LINE#         422
  C:1E22H         LINE#         439
  C:1E25H         LINE#         440
  C:1E27H         LINE#         444
  C:1E29H         LINE#         446
  C:1E2AH         LINE#         447
  C:1E2BH         LINE#         448
  C:1E2CH         LINE#         449
  C:1E2EH         LINE#         450
  C:1E30H         LINE#         486
  C:1E33H         LINE#         488
  -------         ENDMOD        ?C_STARTUP

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  B:00A8H.7       PUBLIC        EA
  D:008FH         PUBLIC        SYSCON0
  D:00F9H         PUBLIC        IPH1
  C:0003H         PUBLIC        MAIN_vInit
  D:00B8H         PUBLIC        IP
  C:2478H         PUBLIC        main
  D:00F8H         PUBLIC        IP1
  D:00BBH         PUBLIC        WDTCON
  D:00B9H         PUBLIC        IPH
  D:00BFH         PUBLIC        SCU_PAGE
  D:00BAH         PUBLIC        CMCON
  -------         PROC          MAIN_VINIT
  C:0003H         LINE#         122
  C:0003H         LINE#         123
  C:0003H         LINE#         138
  C:0006H         LINE#         140
  C:0009H         LINE#         142
  C:000CH         LINE#         151
  C:000FH         LINE#         154
  C:0012H         LINE#         157
  C:0015H         LINE#         160
  C:0018H         LINE#         163
  C:001BH         LINE#         166
  C:001EH         LINE#         170
  C:0021H         LINE#         171
  C:0023H         LINE#         172
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 7


  C:0025H         LINE#         173
  C:0027H         LINE#         185
  C:0029H         LINE#         187
  -------         ENDPROC       MAIN_VINIT
  -------         PROC          MAIN
  C:2478H         LINE#         211
  C:2478H         LINE#         212
  C:2478H         LINE#         219
  C:247BH         LINE#         222
  C:247EH         LINE#         226
  C:247EH         LINE#         227
  C:247EH         LINE#         234
  C:2481H         LINE#         235
  C:2484H         LINE#         236
  C:2487H         LINE#         237
  C:2494H         LINE#         238
  C:2497H         LINE#         246
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        SSC
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ABH         PUBLIC        SSC_CONH_P
  D:00AFH         PUBLIC        SSC_BRH
  D:00AAH         PUBLIC        SSC_CONL_P
  D:00ADH         PUBLIC        SSC_RBL
  D:00AEH         PUBLIC        SSC_BRL
  D:00ACH         PUBLIC        SSC_TBL
  D:0086H         PUBLIC        P0_DIR
  D:00B2H         PUBLIC        PORT_PAGE
  D:00A9H         PUBLIC        SSC_PISEL
  C:2515H         PUBLIC        _SSC_vSendData
  C:2430H         PUBLIC        SSC_vInit
  D:0080H         PUBLIC        P0_ALTSEL0
  D:0086H         PUBLIC        P0_ALTSEL1
  C:2518H         PUBLIC        SSC_vGetData
  -------         PROC          SSC_VINIT
  C:2430H         LINE#         122
  C:2430H         LINE#         123
  C:2430H         LINE#         129
  C:2433H         LINE#         139
  C:2436H         LINE#         140
  C:2439H         LINE#         141
  C:243CH         LINE#         143
  C:243FH         LINE#         144
  C:2442H         LINE#         151
  C:2445H         LINE#         160
  C:2447H         LINE#         162
  C:244AH         LINE#         177
  C:244DH         LINE#         179
  C:2450H         LINE#         181
  C:2453H         LINE#         189
  -------         ENDPROC       SSC_VINIT
  -------         PROC          _SSC_VSENDDATA
  D:0007H         SYMBOL        Data
  C:2515H         LINE#         223
  C:2515H         LINE#         224
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 8


  C:2515H         LINE#         225
  C:2517H         LINE#         227
  -------         ENDPROC       _SSC_VSENDDATA
  -------         PROC          SSC_VGETDATA
  C:2518H         LINE#         253
  C:2518H         LINE#         254
  C:2518H         LINE#         255
  C:251AH         LINE#         257
  -------         ENDPROC       SSC_VGETDATA
  -------         ENDMOD        SSC

  -------         MODULE        IO
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:250AH         PUBLIC        IO_vInit
  D:0091H         PUBLIC        P1_DIR
  D:0090H         PUBLIC        P1_DATA
  -------         PROC          IO_VINIT
  C:250AH         LINE#         122
  C:250AH         LINE#         123
  C:250AH         LINE#         175
  C:250DH         LINE#         176
  C:2510H         LINE#         201
  -------         ENDPROC       IO_VINIT
  -------         ENDMOD        IO

  -------         MODULE        SHARED_INT
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00A8H         PUBLIC        IEN0
  D:00E8H         PUBLIC        IEN1
  C:2278H         PUBLIC        SHINT_viXINTR5Isr
  B:00C0H.7       PUBLIC        TF2
  D:00BFH         PUBLIC        SCU_PAGE
  C:2511H         PUBLIC        SHINT_vInit
  -------         PROC          SHINT_VINIT
  C:2511H         LINE#         121
  C:2511H         LINE#         122
  C:2511H         LINE#         133
  C:2514H         LINE#         137
  C:2514H         LINE#         145
  -------         ENDPROC       SHINT_VINIT
  -------         PROC          SHINT_VIXINTR5ISR
  C:2278H         LINE#         189
  C:229FH         LINE#         195
  C:22A2H         LINE#         199
  C:22A5H         LINE#         200
  C:22A5H         LINE#         203
  C:22A7H         LINE#         206
  C:22AAH         LINE#         209
  C:22AAH         LINE#         215
  C:22ADH         LINE#         216
  -------         ENDPROC       SHINT_VIXINTR5ISR
  -------         ENDMOD        SHARED_INT

  -------         MODULE        CAN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00DBH         PUBLIC        CAN_DATA0
  D:00DCH         PUBLIC        CAN_DATA1
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 9


  D:00DDH         PUBLIC        CAN_DATA2
  D:00DAH         PUBLIC        CAN_ADH
  C:2454H         PUBLIC        _CAN_vTransmit
  D:00DEH         PUBLIC        CAN_DATA3
  D:00D9H         PUBLIC        CAN_ADL
  C:2357H         PUBLIC        _CAN_vReleaseObj
  C:1174H         PUBLIC        _CAN_vGetMsgObj
  D:00D8H         PUBLIC        CAN_ADCON
  C:1F73H         PUBLIC        _CAN_vLoadData
  D:00DBH         PUBLIC        CAN_DATA01
  C:2316H         PUBLIC        _CAN_ubNewData
  D:00DDH         PUBLIC        CAN_DATA23
  C:24D6H         PUBLIC        _CAN_vSetListCommand
  C:141EH         PUBLIC        CAN_vInit
  D:0091H         PUBLIC        P1_DIR
  C:2409H         PUBLIC        _CAN_vWriteAMData
  D:00B2H         PUBLIC        PORT_PAGE
  D:00D9H         PUBLIC        CAN_ADLH
  D:0090H         PUBLIC        P1_ALTSEL0
  D:0091H         PUBLIC        P1_ALTSEL1
  -------         PROC          CAN_VINIT
  C:141EH         LINE#         124
  C:141EH         LINE#         125
  C:141EH         LINE#         140
  C:1424H         LINE#         141
  C:1427H         LINE#         142
  C:142CH         LINE#         144
  C:142FH         LINE#         145
  C:1431H         LINE#         156
  C:1437H         LINE#         157
  C:143AH         LINE#         158
  C:1442H         LINE#         163
  C:1445H         LINE#         168
  C:1448H         LINE#         169
  C:144AH         LINE#         170
  C:1452H         LINE#         184
  C:1455H         LINE#         185
  C:1457H         LINE#         186
  C:145FH         LINE#         204
  C:1462H         LINE#         205
  C:1465H         LINE#         206
  C:146DH         LINE#         217
  C:1470H         LINE#         218
  C:1478H         LINE#         231
  C:147BH         LINE#         232
  C:147DH         LINE#         233
  C:147FH         LINE#         234
  C:1487H         LINE#         247
  C:148DH         LINE#         248
  C:1490H         LINE#         249
  C:1498H         LINE#         259
  C:149BH         LINE#         260
  C:149EH         LINE#         261
  C:14A1H         LINE#         263
  C:14A4H         LINE#         264
  C:14A7H         LINE#         271
  C:14ACH         LINE#         274
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 10


  C:14B6H         LINE#         275
  C:14BBH         LINE#         276
  C:14C0H         LINE#         310
  C:14C6H         LINE#         312
  C:14CBH         LINE#         315
  C:14CEH         LINE#         324
  C:14D9H         LINE#         327
  C:14D9H         LINE#         332
  C:14DCH         LINE#         335
  C:14DCH         LINE#         339
  C:14DFH         LINE#         342
  C:14E2H         LINE#         349
  C:14EDH         LINE#         352
  C:14F0H         LINE#         358
  C:14FBH         LINE#         361
  C:14FBH         LINE#         366
  C:14FEH         LINE#         369
  C:1501H         LINE#         376
  C:150CH         LINE#         387
  C:1512H         LINE#         389
  C:151DH         LINE#         392
  C:1520H         LINE#         401
  C:152BH         LINE#         404
  C:152BH         LINE#         409
  C:152EH         LINE#         412
  C:152EH         LINE#         416
  C:1531H         LINE#         419
  C:1534H         LINE#         426
  C:153FH         LINE#         429
  C:1542H         LINE#         435
  C:154DH         LINE#         438
  C:154DH         LINE#         443
  C:1550H         LINE#         446
  C:1553H         LINE#         453
  C:155EH         LINE#         464
  C:1564H         LINE#         466
  C:156FH         LINE#         469
  C:1572H         LINE#         478
  C:157DH         LINE#         481
  C:157DH         LINE#         486
  C:1580H         LINE#         489
  C:1580H         LINE#         493
  C:1583H         LINE#         496
  C:1586H         LINE#         503
  C:1591H         LINE#         506
  C:1594H         LINE#         512
  C:159FH         LINE#         515
  C:159FH         LINE#         520
  C:15A2H         LINE#         523
  C:15A5H         LINE#         530
  C:15B0H         LINE#         648
  C:15B6H         LINE#         649
  C:15BEH         LINE#         650
  C:15C1H         LINE#         651
  C:15C9H         LINE#         660
  -------         ENDPROC       CAN_VINIT
  C:23FEH         SYMBOL        L?0076
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 11


  C:2403H         SYMBOL        L?0077
  -------         PROC          L?0075
  -------         ENDPROC       L?0075
  C:23FEH         SYMBOL        L?0076
  C:2403H         SYMBOL        L?0077
  -------         PROC          _CAN_VWRITEAMDATA
  D:0004H         SYMBOL        ulValue
  -------         DO            
  X:F02DH         SYMBOL        ulData
  -------         ENDDO         
  C:2409H         LINE#         699
  C:2409H         LINE#         700
  C:2409H         LINE#         702
  C:240FH         LINE#         704
  C:2415H         LINE#         705
  C:241BH         LINE#         706
  C:2421H         LINE#         707
  C:2427H         LINE#         708
  C:242FH         LINE#         709
  -------         ENDPROC       _CAN_VWRITEAMDATA
  -------         PROC          L?0078
  -------         ENDPROC       L?0078
  -------         PROC          _CAN_VSETLISTCOMMAND
  D:0004H         SYMBOL        ulVal
  C:24D6H         LINE#         734
  C:24D6H         LINE#         735
  C:24D6H         LINE#         736
  C:24D9H         LINE#         737
  C:24DCH         LINE#         738
  C:24E1H         LINE#         740
  C:24E4H         LINE#         741
  C:24E6H         LINE#         742
  -------         ENDPROC       _CAN_VSETLISTCOMMAND
  -------         PROC          _CAN_VGETMSGOBJ
  X:F046H         SYMBOL        ubObjNr
  X:F047H         SYMBOL        pstObj
  -------         DO            
  D:0007H         SYMBOL        ubTemp
  -------         ENDDO         
  C:1174H         LINE#         772
  C:1182H         LINE#         773
  C:1182H         LINE#         776
  C:1193H         LINE#         780
  C:11A6H         LINE#         781
  C:11AEH         LINE#         783
  C:11BEH         LINE#         786
  C:11D1H         LINE#         788
  C:11D9H         LINE#         789
  C:11ECH         LINE#         790
  C:11F4H         LINE#         791
  C:11FCH         LINE#         792
  C:1204H         LINE#         795
  C:1207H         LINE#         799
  C:120FH         LINE#         800
  C:1222H         LINE#         801
  C:122AH         LINE#         802
  C:1232H         LINE#         803
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 12


  C:123AH         LINE#         806
  C:124DH         LINE#         807
  C:1255H         LINE#         809
  C:125AH         LINE#         810
  C:125AH         LINE#         811
  C:1272H         LINE#         812
  C:1272H         LINE#         817
  C:1275H         LINE#         818
  C:127DH         LINE#         820
  C:1285H         LINE#         821
  C:1285H         LINE#         824
  C:1298H         LINE#         825
  C:12A0H         LINE#         826
  C:12A8H         LINE#         827
  C:12B2H         LINE#         830
  C:12B5H         LINE#         833
  C:12B8H         LINE#         836
  C:12BBH         LINE#         838
  C:12C3H         LINE#         841
  C:12D6H         LINE#         842
  C:12DEH         LINE#         843
  C:12E6H         LINE#         844
  C:12F0H         LINE#         846
  C:12FFH         LINE#         847
  C:1302H         LINE#         849
  C:1302H         LINE#         851
  C:131DH         LINE#         852
  C:1327H         LINE#         853
  C:132EH         LINE#         854
  C:1335H         LINE#         856
  C:1335H         LINE#         857
  C:1343H         LINE#         858
  C:1354H         LINE#         859
  C:1366H         LINE#         862
  C:1369H         LINE#         865
  C:136CH         LINE#         868
  C:136FH         LINE#         870
  C:1377H         LINE#         874
  C:1392H         LINE#         875
  C:139CH         LINE#         876
  C:13A3H         LINE#         877
  C:13AAH         LINE#         879
  C:13AAH         LINE#         880
  C:13B8H         LINE#         881
  C:13C9H         LINE#         882
  C:13DBH         LINE#         883
  C:13DBH         LINE#         887
  C:13EEH         LINE#         888
  C:13F6H         LINE#         890
  C:1409H         LINE#         891
  C:1411H         LINE#         893
  C:141DH         LINE#         894
  -------         ENDPROC       _CAN_VGETMSGOBJ
  -------         PROC          _CAN_UBNEWDATA
  D:0007H         SYMBOL        ubObjNr
  -------         DO            
  D:0005H         SYMBOL        ubReturn
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 13


  -------         ENDDO         
  C:2316H         LINE#         920
  C:2316H         LINE#         921
  C:2316H         LINE#         922
  C:2318H         LINE#         924
  C:2329H         LINE#         926
  C:2339H         LINE#         927
  C:2341H         LINE#         928
  C:2346H         LINE#         929
  C:2346H         LINE#         930
  C:2348H         LINE#         931
  C:2348H         LINE#         932
  C:2354H         LINE#         934
  C:2356H         LINE#         935
  -------         ENDPROC       _CAN_UBNEWDATA
  -------         PROC          _CAN_VTRANSMIT
  D:0007H         SYMBOL        ubObjNr
  C:2454H         LINE#         964
  C:2454H         LINE#         965
  C:2454H         LINE#         968
  C:2464H         LINE#         969
  C:2477H         LINE#         973
  -------         ENDPROC       _CAN_VTRANSMIT
  -------         PROC          _CAN_VLOADDATA
  D:0007H         SYMBOL        ubObjNr
  D:0001H         SYMBOL        ulpubData
  C:1F73H         LINE#         1004
  C:1F73H         LINE#         1005
  C:1F73H         LINE#         1006
  C:1F84H         LINE#         1008
  C:1F94H         LINE#         1009
  C:1FA6H         LINE#         1012
  C:1FA9H         LINE#         1015
  C:1FACH         LINE#         1018
  C:1FAFH         LINE#         1020
  C:1FCBH         LINE#         1023
  C:1FCEH         LINE#         1025
  C:1FD5H         LINE#         1027
  C:1FF1H         LINE#         1030
  C:1FF4H         LINE#         1033
  C:1FF7H         LINE#         1035
  C:200BH         LINE#         1037
  C:2017H         LINE#         1039
  -------         ENDPROC       _CAN_VLOADDATA
  -------         PROC          _CAN_VRELEASEOBJ
  D:0007H         SYMBOL        ubObjNr
  C:2357H         LINE#         1068
  C:2357H         LINE#         1069
  C:2357H         LINE#         1071
  C:2368H         LINE#         1072
  C:2378H         LINE#         1073
  C:238BH         LINE#         1075
  C:2397H         LINE#         1076
  -------         ENDPROC       _CAN_VRELEASEOBJ
  -------         ENDMOD        CAN

  -------         MODULE        T2
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 14


  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00C4H         PUBLIC        T2_T2LH
  D:00C2H         PUBLIC        T2_RC2LH
  D:00C1H         PUBLIC        T2_T2MOD
  B:00C0H.2       PUBLIC        TR2
  C:24E7H         PUBLIC        T2_vInit
  -------         PROC          T2_VINIT
  C:24E7H         LINE#         131
  C:24E7H         LINE#         132
  C:24E7H         LINE#         151
  C:24EDH         LINE#         154
  C:24F3H         LINE#         156
  C:24F6H         LINE#         164
  C:24F8H         LINE#         166
  -------         ENDPROC       T2_VINIT
  -------         ENDMOD        T2

  -------         MODULE        WDT
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:24B5H         PUBLIC        WDT_vDisable
  D:008FH         PUBLIC        SYSCON0
  D:00BBH         PUBLIC        WDTCON
  D:00BCH         PUBLIC        WDTREL
  D:00BBH         PUBLIC        PASSWD
  C:2499H         PUBLIC        WDT_vInit
  D:00BFH         PUBLIC        SCU_PAGE
  -------         PROC          WDT_VINIT
  C:2499H         LINE#         131
  C:2499H         LINE#         132
  C:2499H         LINE#         145
  C:249CH         LINE#         146
  C:249FH         LINE#         148
  C:24A2H         LINE#         149
  C:24A5H         LINE#         150
  C:24A8H         LINE#         151
  C:24ABH         LINE#         152
  C:24AEH         LINE#         154
  C:24B1H         LINE#         155
  C:24B4H         LINE#         163
  -------         ENDPROC       WDT_VINIT
  -------         PROC          WDT_VDISABLE
  C:24B5H         LINE#         184
  C:24B5H         LINE#         185
  C:24B5H         LINE#         186
  C:24B8H         LINE#         188
  C:24BBH         LINE#         189
  C:24BEH         LINE#         191
  C:24C1H         LINE#         192
  C:24C4H         LINE#         193
  C:24C7H         LINE#         194
  C:24CAH         LINE#         196
  C:24CDH         LINE#         197
  C:24CFH         LINE#         198
  -------         ENDPROC       WDT_VDISABLE
  -------         ENDMOD        WDT

  -------         MODULE        SWDRIVER
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 15


  C:0000H         SYMBOL        _ICE_DUMMY_
  C:002EH         PUBLIC        _LedLight
  B:0090H.2       PUBLIC        P1_2
  B:0090H.3       PUBLIC        P1_3
  C:0AE2H         PUBLIC        _Mc33993CmdSendOneBit
  D:00B5H         PUBLIC        IRCON1
  C:24F9H         PUBLIC        _ChangeCsMc33993
  C:1EB6H         PUBLIC        InitSW
  C:1BD7H         PUBLIC        _SwStaSample
  X:F052H         SYMBOL        PntSta
  C:15CEH         PUBLIC        _Mc33993CmdSend
  C:1A7AH         PUBLIC        InitMc33993Sec
  C:2398H         PUBLIC        _Mc33993DataSendRcv
  -------         PROC          _SWSTASAMPLE
  X:F032H         SYMBOL        ptmp
  -------         DO            
  X:F035H         SYMBOL        pStaSwIn
  X:F038H         SYMBOL        i
  D:0006H         SYMBOL        k
  -------         ENDDO         
  C:1BD7H         LINE#         240
  C:1BE2H         LINE#         241
  C:1BE2H         LINE#         245
  C:1BE5H         LINE#         247
  C:1BECH         LINE#         248
  C:1BFAH         LINE#         249
  C:1C0BH         LINE#         250
  C:1C1CH         LINE#         252
  C:1C39H         LINE#         254
  C:1C61H         LINE#         255
  C:1C89H         LINE#         256
  C:1CB1H         LINE#         257
  C:1CD9H         LINE#         258
  C:1CF9H         LINE#         259
  -------         ENDPROC       _SWSTASAMPLE
  -------         PROC          _LEDLIGHT
  X:F03EH         SYMBOL        tmpUn
  -------         DO            
  X:F041H         SYMBOL        i
  X:F042H         SYMBOL        j
  -------         ENDDO         
  C:002EH         LINE#         277
  C:0039H         LINE#         278
  C:0039H         LINE#         283
  C:003CH         LINE#         284
  C:003CH         LINE#         285
  C:004EH         LINE#         286
  C:004EH         LINE#         287
  C:00A0H         LINE#         288
  C:00A0H         LINE#         289
  C:00D6H         LINE#         290
  C:010EH         LINE#         291
  C:0148H         LINE#         292
  C:0183H         LINE#         293
  C:01BCH         LINE#         294
  C:01F5H         LINE#         295
  C:022FH         LINE#         296
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 16


  C:026DH         LINE#         297
  C:02A0H         LINE#         298
  C:02D6H         LINE#         299
  C:030EH         LINE#         300
  C:0347H         LINE#         301
  C:037EH         LINE#         302
  C:03B6H         LINE#         303
  C:03EFH         LINE#         304
  C:0429H         LINE#         305
  C:0461H         LINE#         306
  C:049BH         LINE#         307
  C:04D7H         LINE#         308
  C:0513H         LINE#         309
  C:054DH         LINE#         310
  C:0589H         LINE#         311
  C:0589H         LINE#         312
  C:0589H         LINE#         313
  C:0592H         LINE#         314
  C:05A1H         LINE#         315
  -------         ENDPROC       _LEDLIGHT
  -------         PROC          INITMC33993SEC
  -------         DO            
  X:F03AH         SYMBOL        i
  -------         ENDDO         
  C:1A7AH         LINE#         335
  C:1A7AH         LINE#         336
  C:1A7AH         LINE#         338
  C:1A7FH         LINE#         339
  C:1A7FH         LINE#         340
  C:1A9DH         LINE#         341
  C:1ABBH         LINE#         342
  C:1ADEH         LINE#         343
  C:1AFCH         LINE#         344
  C:1B1FH         LINE#         345
  C:1B3DH         LINE#         346
  C:1B5BH         LINE#         347
  C:1B82H         LINE#         348
  C:1BA0H         LINE#         349
  C:1BC7H         LINE#         350
  C:1BD6H         LINE#         351
  -------         ENDPROC       INITMC33993SEC
  -------         PROC          INITSW
  -------         DO            
  X:F02AH         SYMBOL        i
  -------         ENDDO         
  C:1EB6H         LINE#         369
  C:1EB6H         LINE#         370
  C:1EB6H         LINE#         372
  C:1EC8H         LINE#         373
  C:1EC8H         LINE#         374
  C:1ED3H         LINE#         375
  C:1EE5H         LINE#         376
  C:1EF7H         LINE#         377
  C:1F07H         LINE#         378
  C:1F17H         LINE#         379
  C:1F27H         LINE#         380
  C:1F37H         LINE#         381
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 17


  C:1F47H         LINE#         382
  C:1F58H         LINE#         383
  C:1F69H         LINE#         385
  C:1F72H         LINE#         386
  -------         ENDPROC       INITSW
  -------         PROC          _MC33993CMDSENDONEBIT
  X:F043H         SYMBOL        NumMc
  X:F044H         SYMBOL        CmdBit
  X:F045H         SYMBOL        Sp0ToSg13
  B:0020H.4       SYMBOL        Status
  -------         DO            
  X:F046H         SYMBOL        RcvSoMc33993
  -------         ENDDO         
  C:0AE2H         LINE#         405
  C:0AEFH         LINE#         406
  C:0AEFH         LINE#         409
  C:0AF7H         LINE#         410
  C:0B09H         LINE#         411
  C:0B09H         LINE#         413
  C:0B09H         LINE#         414
  C:0B20H         LINE#         415
  C:0B45H         LINE#         416
  C:0B66H         LINE#         417
  C:0B8BH         LINE#         418
  C:0BADH         LINE#         419
  C:0BD1H         LINE#         420
  C:0BF1H         LINE#         421
  C:0C11H         LINE#         422
  C:0C33H         LINE#         424
  C:0C37H         LINE#         426
  C:0C3AH         LINE#         428
  C:0C3AH         LINE#         429
  C:0C58H         LINE#         430
  C:0C71H         LINE#         431
  C:0C87H         LINE#         432
  C:0CA1H         LINE#         433
  C:0CB8H         LINE#         434
  C:0CCFH         LINE#         435
  C:0CEFH         LINE#         436
  C:0D1FH         LINE#         437
  C:0D4BH         LINE#         438
  C:0D7BH         LINE#         439
  C:0DA9H         LINE#         440
  C:0DD8H         LINE#         441
  C:0E03H         LINE#         442
  C:0E2BH         LINE#         443
  C:0E4DH         LINE#         445
  C:0E62H         LINE#         447
  C:0E62H         LINE#         448
  C:0E62H         LINE#         449
  C:0E62H         LINE#         450
  C:0E64H         LINE#         451
  -------         ENDPROC       _MC33993CMDSENDONEBIT
  -------         PROC          _CHANGECSMC33993
  D:0007H         SYMBOL        NumMc
  B:0020H.5       SYMBOL        HorL
  C:24F9H         LINE#         455
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 18


  C:24F9H         LINE#         456
  C:24F9H         LINE#         457
  C:2500H         LINE#         458
  C:2500H         LINE#         459
  C:2505H         LINE#         460
  C:2509H         LINE#         461
  C:2509H         LINE#         462
  C:2509H         LINE#         463
  -------         ENDPROC       _CHANGECSMC33993
  -------         PROC          _MC33993DATASENDRCV
  D:0007H         SYMBOL        Data
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:2398H         LINE#         481
  C:2398H         LINE#         482
  C:2398H         LINE#         484
  C:239BH         LINE#         485
  C:23AEH         LINE#         486
  C:23B1H         LINE#         487
  C:23C4H         LINE#         488
  C:23C7H         LINE#         489
  C:23CAH         LINE#         490
  -------         ENDPROC       _MC33993DATASENDRCV
  -------         PROC          L?0258
  -------         ENDPROC       L?0258
  -------         PROC          _MC33993CMDSEND
  X:F048H         SYMBOL        NumMc
  D:0005H         SYMBOL        CmdBit
  X:F04AH         SYMBOL        SetBitH
  X:F04BH         SYMBOL        SetBitL
  -------         DO            
  X:F04CH         SYMBOL        RcvSoMc33993
  D:0001H         SYMBOL        Pnt
  X:F050H         SYMBOL        RcvSoMc33993W
  -------         ENDDO         
  C:15CEH         LINE#         508
  C:15D8H         LINE#         509
  C:15D8H         LINE#         514
  C:15DCH         LINE#         515
  C:15E4H         LINE#         516
  C:161AH         LINE#         517
  C:161AH         LINE#         518
  C:1633H         LINE#         519
  C:164CH         LINE#         520
  C:1666H         LINE#         521
  C:167EH         LINE#         522
  C:1697H         LINE#         523
  C:16AFH         LINE#         524
  C:16C7H         LINE#         525
  C:16E0H         LINE#         526
  C:16FCH         LINE#         527
  C:171AH         LINE#         528
  C:171AH         LINE#         529
  C:171AH         LINE#         532
  C:1726H         LINE#         533
  C:1730H         LINE#         535
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 19


  C:1731H         LINE#         536
  C:1732H         LINE#         537
  C:1733H         LINE#         538
  C:1734H         LINE#         542
  C:173EH         LINE#         544
  C:174BH         LINE#         546
  C:1758H         LINE#         548
  C:1762H         LINE#         550
  C:1763H         LINE#         551
  C:1764H         LINE#         552
  C:1765H         LINE#         553
  C:1766H         LINE#         555
  C:176CH         LINE#         556
  C:176CH         LINE#         557
  -------         ENDPROC       _MC33993CMDSEND
  -------         ENDMOD        SWDRIVER

  -------         MODULE        USER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00DBH         PUBLIC        CAN_DATA0
  D:00DCH         PUBLIC        CAN_DATA1
  D:00DDH         PUBLIC        CAN_DATA2
  D:00DAH         PUBLIC        CAN_ADH
  D:00DEH         PUBLIC        CAN_DATA3
  D:00D9H         PUBLIC        CAN_ADL
  C:2115H         PUBLIC        CanErrorProcess
  B:00A8H.7       PUBLIC        EA
  X:F00EH         SYMBOL        UnSwSample_1
  X:F012H         PUBLIC        UnInfCan_1
  D:00D8H         PUBLIC        CAN_ADCON
  C:05A2H         PUBLIC        SwSample
  B:0020H.2       PUBLIC        FlgCan_1
  C:0E65H         PUBLIC        CanTX
  X:F022H         PUBLIC        RgCanPerid
  X:F024H         PUBLIC        CntCan_1
  C:176DH         PUBLIC        LedDr
  D:00D9H         PUBLIC        CAN_ADLH
  C:23CBH         PUBLIC        RTCProcess
  B:0020H.3       PUBLIC        FlgOneTime
  C:1CFAH         PUBLIC        Password
  X:F026H         PUBLIC        UnSwOut_1
  D:00BFH         PUBLIC        SCU_PAGE
  C:22D2H         PUBLIC        CanTransmit
  C:2191H         PUBLIC        CanRXTX
  -------         PROC          RTCPROCESS
  C:23CBH         LINE#         25
  C:23CBH         LINE#         26
  C:23CBH         LINE#         29
  C:23FDH         LINE#         32
  -------         ENDPROC       RTCPROCESS
  -------         PROC          CANERRORPROCESS
  -------         DO            
  X:F02AH         SYMBOL        ubNSRL
  X:F02BH         SYMBOL        ubNSRH
  X:F02CH         SYMBOL        ubResetLEC
  -------         ENDDO         
  C:2115H         LINE#         36
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 20


  C:2115H         LINE#         37
  C:2115H         LINE#         40
  C:211BH         LINE#         42
  C:211DH         LINE#         44
  C:212EH         LINE#         45
  C:2131H         LINE#         47
  C:2137H         LINE#         48
  C:213FH         LINE#         49
  C:2145H         LINE#         50
  C:2149H         LINE#         54
  C:2150H         LINE#         55
  C:2150H         LINE#         56
  C:2153H         LINE#         80
  C:2153H         LINE#         84
  C:215CH         LINE#         85
  C:215CH         LINE#         113
  C:2162H         LINE#         114
  C:2162H         LINE#         116
  C:2168H         LINE#         117
  C:2170H         LINE#         118
  C:2177H         LINE#         119
  C:217FH         LINE#         121
  C:2182H         LINE#         122
  C:218EH         LINE#         124
  C:2190H         LINE#         125
  -------         ENDPROC       CANERRORPROCESS
  -------         PROC          PASSWORD
  -------         DO            
  D:0001H         SYMBOL        i
  -------         ENDDO         
  C:1CFAH         LINE#         129
  C:1CFAH         LINE#         130
  C:1CFAH         LINE#         132
  C:1D08H         LINE#         133
  C:1D1DH         LINE#         135
  C:1D1FH         LINE#         136
  C:1D1FH         LINE#         137
  C:1D3AH         LINE#         138
  C:1D55H         LINE#         139
  C:1D70H         LINE#         140
  C:1D8BH         LINE#         141
  C:1DA6H         LINE#         142
  C:1DD1H         LINE#         143
  C:1DDAH         LINE#         144
  -------         ENDPROC       PASSWORD
  -------         PROC          CANTRANSMIT
  C:22D2H         LINE#         147
  C:22D2H         LINE#         148
  C:22D2H         LINE#         149
  C:22D5H         LINE#         150
  C:22E0H         LINE#         151
  C:22F1H         LINE#         152
  C:22F7H         LINE#         153
  C:22FFH         LINE#         154
  C:2304H         LINE#         155
  C:2304H         LINE#         156
  C:2309H         LINE#         157
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 21


  C:2309H         LINE#         158
  C:2315H         LINE#         159
  -------         ENDPROC       CANTRANSMIT
  -------         PROC          LEDDR
  C:176DH         LINE#         166
  C:176DH         LINE#         167
  C:176DH         LINE#         168
  C:1781H         LINE#         169
  C:1795H         LINE#         170
  C:17A9H         LINE#         171
  C:17BDH         LINE#         172
  C:17D1H         LINE#         173
  C:17E5H         LINE#         174
  C:17F9H         LINE#         175
  C:180DH         LINE#         176
  C:1821H         LINE#         177
  C:1835H         LINE#         178
  C:1849H         LINE#         179
  C:185DH         LINE#         180
  C:1871H         LINE#         181
  C:1885H         LINE#         182
  C:1899H         LINE#         183
  C:18ADH         LINE#         184
  C:18C1H         LINE#         185
  C:18D5H         LINE#         186
  C:18E9H         LINE#         187
  C:18FDH         LINE#         189
  -------         ENDPROC       LEDDR
  -------         PROC          CANTX
  C:0E65H         LINE#         195
  C:0E65H         LINE#         196
  C:0E65H         LINE#         197
  C:0E92H         LINE#         198
  C:0EBFH         LINE#         199
  C:0EE5H         LINE#         200
  C:0F09H         LINE#         201
  C:0F33H         LINE#         202
  C:0F5DH         LINE#         203
  C:0F87H         LINE#         204
  C:0FB1H         LINE#         205
  C:0FDCH         LINE#         206
  C:1007H         LINE#         207
  C:1031H         LINE#         208
  C:105BH         LINE#         209
  C:107AH         LINE#         210
  C:109CH         LINE#         211
  C:10C6H         LINE#         212
  C:10F1H         LINE#         213
  C:1111H         LINE#         214
  C:1131H         LINE#         215
  C:1153H         LINE#         216
  C:1171H         LINE#         218
  -------         ENDPROC       CANTX
  -------         PROC          CANRXTX
  -------         DO            
  X:F02AH         SYMBOL        StrCanRx
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 22


  C:2191H         LINE#         222
  C:2191H         LINE#         223
  C:2191H         LINE#         226
  C:2199H         LINE#         227
  C:2199H         LINE#         228
  C:21A4H         LINE#         230
  C:21ACH         LINE#         231
  C:21B4H         LINE#         232
  C:21BCH         LINE#         233
  C:21C4H         LINE#         234
  C:21CCH         LINE#         235
  C:21D4H         LINE#         236
  C:21DCH         LINE#         237
  C:21E4H         LINE#         239
  C:21E9H         LINE#         240
  C:21FCH         LINE#         242
  C:21FFH         LINE#         243
  C:21FFH         LINE#         245
  C:2202H         LINE#         246
  C:2202H         LINE#         247
  C:2205H         LINE#         248
  C:2205H         LINE#         249
  C:2207H         LINE#         250
  C:220AH         LINE#         251
  C:220AH         LINE#         252
  C:220AH         LINE#         256
  -------         ENDPROC       CANRXTX
  -------         PROC          SWSAMPLE
  -------         DO            
  X:F02AH         SYMBOL        i
  X:F02EH         SYMBOL        j
  X:F000H         SYMBOL        tmpSwSample1
  X:F004H         SYMBOL        tmpSwSample2
  X:F008H         SYMBOL        tmpSwSample3
  X:F00CH         SYMBOL        CntTremble
  B:0020H.0       SYMBOL        FlgSample
  B:0020H.1       SYMBOL        FlgSample1
  -------         ENDDO         
  C:05A2H         LINE#         277
  C:05A2H         LINE#         278
  C:05A2H         LINE#         287
  C:05ABH         LINE#         288
  C:05ADH         LINE#         289
  C:05D4H         LINE#         290
  C:05D4H         LINE#         291
  C:05FBH         LINE#         292
  C:05FBH         LINE#         293
  C:0665H         LINE#         294
  C:0665H         LINE#         295
  C:0667H         LINE#         296
  C:0667H         LINE#         297
  C:0689H         LINE#         298
  C:06ABH         LINE#         300
  C:06AEH         LINE#         301
  C:06AEH         LINE#         302
  C:06BCH         LINE#         303
  C:06BFH         LINE#         305
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 23


  C:06BFH         LINE#         306
  C:06E6H         LINE#         307
  C:06E6H         LINE#         308
  C:070DH         LINE#         309
  C:070DH         LINE#         310
  C:0775H         LINE#         311
  C:0797H         LINE#         312
  C:07B9H         LINE#         313
  C:07BBH         LINE#         314
  C:07C2H         LINE#         315
  C:07C2H         LINE#         316
  C:07D6H         LINE#         317
  C:07D6H         LINE#         319
  C:07FDH         LINE#         320
  C:07FDH         LINE#         321
  C:0824H         LINE#         322
  C:0824H         LINE#         323
  C:088CH         LINE#         324
  C:08AEH         LINE#         325
  C:08D0H         LINE#         326
  C:08D7H         LINE#         328
  C:08DCH         LINE#         331
  C:08DEH         LINE#         332
  C:0905H         LINE#         333
  C:0905H         LINE#         334
  C:092CH         LINE#         335
  C:092CH         LINE#         336
  C:0996H         LINE#         337
  C:0996H         LINE#         338
  C:0998H         LINE#         339
  C:0998H         LINE#         340
  C:09BAH         LINE#         341
  C:09DCH         LINE#         343
  C:09E2H         LINE#         344
  C:09E2H         LINE#         346
  C:09E5H         LINE#         348
  C:0A0CH         LINE#         349
  C:0A0CH         LINE#         350
  C:0A33H         LINE#         351
  C:0A33H         LINE#         352
  C:0A9BH         LINE#         353
  C:0ABDH         LINE#         354
  C:0ADFH         LINE#         355
  C:0AE1H         LINE#         356
  C:0AE1H         LINE#         358
  C:0AE1H         LINE#         361
  -------         ENDPROC       SWSAMPLE
  -------         ENDMOD        USER

  -------         MODULE        BOOT
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:2018H         PUBLIC        Copy
  C:2097H         PUBLIC        BootMain
  -------         PROC          COPY
  -------         DO            
  X:F046H         SYMBOL        i
  X:F04AH         SYMBOL        pBootCode
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 24


  X:F04CH         SYMBOL        pXramCode
  -------         ENDDO         
  C:2018H         LINE#         7
  C:2018H         LINE#         8
  C:2018H         LINE#         13
  C:2021H         LINE#         14
  C:2028H         LINE#         15
  C:204CH         LINE#         16
  C:204CH         LINE#         17
  C:2075H         LINE#         19
  C:2096H         LINE#         20
  -------         ENDPROC       COPY
  -------         PROC          BOOTMAIN
  -------         DO            
  X:F02AH         SYMBOL        StrBootRx
  X:F03EH         SYMBOL        ulCANData
  D:0006H         SYMBOL        tmp
  D:0001H         SYMBOL        pJump
  -------         ENDDO         
  C:2097H         LINE#         24
  C:2097H         LINE#         25
  C:2097H         LINE#         32
  C:209FH         LINE#         33
  C:209FH         LINE#         34
  C:20AAH         LINE#         36
  C:20B2H         LINE#         37
  C:20BAH         LINE#         38
  C:20C2H         LINE#         39
  C:20CAH         LINE#         40
  C:20D2H         LINE#         41
  C:20DAH         LINE#         42
  C:20E2H         LINE#         43
  C:20EAH         LINE#         45
  C:20EFH         LINE#         46
  C:20FBH         LINE#         47
  C:20FBH         LINE#         48
  C:20FEH         LINE#         49
  C:2101H         LINE#         50
  C:2105H         LINE#         51
  C:2108H         LINE#         52
  C:210EH         LINE#         53
  C:2111H         LINE#         55
  C:2114H         LINE#         56
  C:2114H         LINE#         57
  C:2114H         LINE#         58
  -------         ENDPROC       BOOTMAIN
  -------         ENDMOD        BOOT

  -------         MODULE        ?C?CLDPTR
  C:1906H         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:191FH         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CILDOPTR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 25


  C:194CH         PUBLIC        ?C?CILDOPTR
  -------         ENDMOD        ?C?CILDOPTR

  -------         MODULE        ?C?CSTPTR
  C:197FH         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:1991H         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR

  -------         MODULE        ?C?ULCMP
  C:19B3H         PUBLIC        ?C?ULCMP
  -------         ENDMOD        ?C?ULCMP

  -------         MODULE        ?C?ULSHR
  C:19C4H         PUBLIC        ?C?ULSHR
  -------         ENDMOD        ?C?ULSHR

  -------         MODULE        ?C?LLDPTR
  C:19D7H         PUBLIC        ?C?LLDPTR
  -------         ENDMOD        ?C?LLDPTR

  -------         MODULE        ?C?LSTXDATA
  C:19F7H         PUBLIC        ?C?LSTXDATA
  -------         ENDMOD        ?C?LSTXDATA

  -------         MODULE        ?C?LSTKXDATA
  C:1A03H         PUBLIC        ?C?LSTKXDATA
  -------         ENDMOD        ?C?LSTKXDATA

  -------         MODULE        ?C?OFFXADD
  C:1A34H         PUBLIC        ?C?OFFXADD
  -------         ENDMOD        ?C?OFFXADD

  -------         MODULE        ?C?ICALL
  C:1A40H         PUBLIC        ?C?ICALL
  C:1A44H         PUBLIC        ?C?ICALL2
  -------         ENDMOD        ?C?ICALL

  -------         MODULE        ?C?LLDIDATA
  C:1A46H         PUBLIC        ?C?LLDIDATA
  -------         ENDMOD        ?C?LLDIDATA

  -------         MODULE        ?C?LLDXDATA
  C:1A52H         PUBLIC        ?C?LLDXDATA
  -------         ENDMOD        ?C?LLDXDATA

  -------         MODULE        ?C?LLDPDATA
  C:1A5EH         PUBLIC        ?C?LLDPDATA
  -------         ENDMOD        ?C?LLDPDATA

  -------         MODULE        ?C?LLDCODE
  C:1A6AH         PUBLIC        ?C?LLDCODE
  -------         ENDMOD        ?C?LLDCODE

Program Size: data=9.6 xdata=110 code=9498
BL51 BANKED LINKER/LOCATER V6.22                                                      07/04/2013  17:36:08  PAGE 26


LINK/LOCATE RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)

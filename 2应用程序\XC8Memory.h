//****************************************************************************
// File Name     XC8Memory.h
//----------------------------------------------------------------------------
// Derivatives   Infineon XC88x AA and AB
//
// Description   Sets of function to handle the memory in the XC88x AA.
//               In particular, the Flash handling is taken care of here.
//
//----------------------------------------------------------------------------
// Date          14.03.2006 10:48:38
// Copyright     (c) 2006 Infineon Technologies
//****************************************************************************

// Header for XC8Memory.c
// Memory handling of XC88x AA derivatives

#ifndef _XC8MEMORY_H_
#define _XC8MEMORY_H_

#include "XC88x_FLADDR.H"


//+------------------------------------------------+
//|   Global variables needed
//+------------------------------------------------+
// Buffer for the 32byte data before being written to eEPROM
extern unsigned char idata WLBuf[BYTES_PER_WORDLINE];

// Function to program the WLBuf to Flash Memory
bit ProgWL(unsigned char code *AdrBnkXSecYWLZ);
// Function to load the data from XDATA memory to WLBuf
void LoadXD2WLBuf(unsigned char xdata *address);
// Function to load the data from CODE memory to WLBuf
// This is used to copy data from one Flash location to another.
void LoadConst2WLBuf(unsigned char code *address);
// Function to load the data from CODE memory to XDATA memory
void LoadConst2XD(unsigned char xdata *dstaddress, unsigned char code *srcaddress, unsigned char length);
// Function to read a byte from CODE memory
unsigned char ReadConst(unsigned char code *address);

// All of the description of the functions below is done in XC88x_FLHANDER.ASM
// Erase Flash Sectors
// Required Stack Size = 12
extern bit PFlErase (unsigned char Bank0Sector, unsigned char Bank1Sector, unsigned char Bank2Sector);
extern bit DFlErase (unsigned int DFlash0Sector, unsigned int DFlash1Sector);

// Program Flash
// Required Stack Size = 12
extern bit FlProg(unsigned char idata *SrcBuf);

// Check if Flash is ready to read
// Bank = Bank Number to be checked
// Required Stack Size = 6
extern bit FlReady(unsigned char Bank);

// Abort the existing erase process
// Required Stack Size = 6
extern bit FlEraseAbort(void);

// Program / Remove Password.
// Detail is available in FLHANDLER.asm
extern bit FlProtect(unsigned char Password);

extern unsigned char _FlReadByte(void);
extern void FlExecute(unsigned char AddrH, unsigned char AddrL);

sfr MEM_DPH    = 0x83;    
sfr MEM_DPL    = 0x82;
sfr MEM_NMICON = 0xBB;
sfr MEM_NMISR  = 0xBC;

#endif  // _XC8MEMORY_H_

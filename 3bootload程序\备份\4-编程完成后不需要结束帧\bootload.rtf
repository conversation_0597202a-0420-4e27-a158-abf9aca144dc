{\rtf1\ansi\deff0\deftab720{\fonttbl{\f0\fnil MS Sans Serif;}{\f1\fnil\fcharset2 Symbol;}{\f2\fswiss\fprq2 System;}{\f3\fnil Times New Roman;}{\f4\fswiss\fprq2 Arial;}}
{\colortbl\red0\green0\blue0;\red0\green0\blue128;\red255\green0\blue0;}
\deflang1031\pard\plain\f4\fs28\cf0 DAvE's Project Documentation
\par \plain\f4\fs22\cf0
\par \plain\f4\fs22\cf0 Project: \tab\tab\b bootload.dav
\par 
\par \plain\f4\fs22\cf0 Controller: \tab\tab\b XC886CLM-8FF
\par \plain\f4\fs22\cf0 Compiler: \tab\tab\b Keil
\par \plain\f4\fs22\cf0 Memory Model: \tab\b SMALL
\par 
\par \plain\f4\fs22\cf0 Date: \tab\tab\tab\b 2013-7-7 10:40:52
\par 
\par 
\par \plain\f4\fs22\cf2\b Please read this document carefully and note
\par \plain\f4\fs22\cf2\b the red-colored hints.
\par 
\par \plain\f4\fs22\cf2\b If you miss a file in the generated files list
\par \plain\f4\fs22\cf2\b maybe you have forgotten to select the
\par \plain\f4\fs22\cf2\b initialisation function of the related module.
\par 
\par \plain\f4\fs22\cf0 Generated Files:
\plain\f4\fs20\cf0\b
\par \tab\tab\tab MAIN.H
\par \tab\tab\tab MAIN.C
\par \tab\tab\tab START_XC.A51
\par \tab\tab\tab CAN.H
\par \tab\tab\tab CAN.C
\par \tab\tab\tab BOOTLOAD.ASM
\par 
\par 
\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul Project Settings
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void MAIN_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function initializes the microcontroller. It is

\par \tab \tab assumed that the SFRs are in their reset state.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void main(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the main function.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab Configuration of the System Clock:\par 
\tab - External Osc is selected (configuration is done in the startup file\par 
\tab 'START_XC.A51')\par 
\tab - PLL Mode, NDIV =  24\par 
\tab - input frequency is 8 MHz\par 
\par 
\tab *********************************************************************************\par 
\tab Note : All peripheral related IO configurations are done in the\par 
\tab respective peripheral modules (alternate functions selection)\par 
\tab *********************************************************************************\par 
\par 
\tab Initialization of module 'MultiCAN Controller '\par 
\par 
\tab \cf2Interrupt structure 2 mode 0 is selected.\cf0\par 
\par 
\tab \cf2Interrupt service routine choice 2 is selected.\cf0\par 
\par 
\tab \cf2globally disable interrupts\cf0\par 
\par 

\par \plain\f4\fs20\cf0
\par \plain\f4\fs28\cf0\ul MultiCAN Controller
\par
\par \plain\f4\fs24\cf0 Macros:\f4\fs20\cf0
\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_vReadEN()\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This macro enables Read mode (CAN Address/Data Control

\par \tab \tab Register).

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_vWriteEN(ubyte ubDCtrl)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This macro enables Write mode (CAN Address/Data Control

\par \tab \tab Register).

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubDCtrl:

\par \tab \tab Data Control Flags

\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_vWriteCANAddress(uword uwAdr)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This macro writes 16-bit CAN address to CAN Address

\par \tab \tab Register Low and High respectively.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab uwAdr:

\par \tab \tab 16-bit Address

\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_vWriteCANData(ulong ulValue)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This macro writes 32-bit Data to CAN Data Register's

\par \tab \tab 0-3respectively.

\par \tab \tab Note:

\par \tab \tab Write Process :

\par \tab \tab ->Write the address of the MultiCAN kernel register to the

\par \tab \tab CAN_ADL and CAN_ADH registers.

\par \tab \tab use macro : CAN_vWriteCANAddress.

\par \tab \tab ->Write the data to the

\par \tab \tab CAN_DATA0/CAN_DATA1/CAN_DATA2/CAN_DATA3 registers.

\par \tab \tab ->Write the register CAN_ADCON, including setting the valid

\par \tab \tab bit of the data registers and setting register bit RWEN to

\par \tab \tab 1.

\par \tab \tab ->The valid data will be written to the MultiCAN kernel

\par \tab \tab only once. Register bit BSY will become 1.

\par \tab \tab ->When Register bit BSY becomes 0, the transmission is

\par \tab \tab finished.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ulValue:

\par \tab \tab 32-bit Data

\par
\par
\par \plain\f4\fs20\cf0 \tab Macro:
\par \plain\f4\fs20\cf0\b \tab \tab CAN_pushAMRegs/_popAMRegs()\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab The macro CAN_pushAMRegs() PUSH the CAN Access Mediator

\par \tab \tab Registers.

\par \tab \tab The macro CAN_popAMRegs() POP the CAN Access Mediator

\par \tab \tab Registers.

\par \tab \tab Note:

\par \tab \tab This macro is used in CAN ISR/Function's to protect Access

\par \tab \tab Mediator Register

\par \tab \tab MultiCAN Access Mediator Registers:

\par \tab \tab ADL, ADH, DATA0, DATA1, DATA2, DATA3.

\par \tab \tab The _push_/_pop_ routine inserts a PUSH/POP instruction

\par \tab \tab into the program saving the contents of the Special

\par \tab \tab Function Register(sfr) on the Stack.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par \plain\f4\fs24\cf0 Functions:\f4\fs20\cf0
\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vInit(void)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This is the initialization function of the CAN function

\par \tab \tab library. It is assumed that the SFRs used by this library

\par \tab \tab are in their reset state.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab None

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vWriteAMData(ulong ulValue)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function writes 32-bit Data to CAN Data Register's

\par \tab \tab 0-3respectively.

\par \tab \tab Note:

\par \tab \tab Write Process :

\par \tab \tab ->Write the address of the MultiCAN kernel register to the

\par \tab \tab CAN_ADL and CAN_ADH registers.

\par \tab \tab use macro : CAN_vWriteCANAddress.

\par \tab \tab ->Write the data to the

\par \tab \tab CAN_DATA0/CAN_DATA1/CAN_DATA2/CAN_DATA3 registers.

\par \tab \tab ->Write the register CAN_ADCON, including setting the valid

\par \tab \tab bit of the data registers and setting register bit RWEN to

\par \tab \tab 1.

\par \tab \tab ->The valid data will be written to the MultiCAN kernel

\par \tab \tab only once. Register bit BSY will become 1.

\par \tab \tab ->When Register bit BSY becomes 0, the transmission is

\par \tab \tab finished.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ulValue:

\par \tab \tab 32-bit Data

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vSetListCommand(ulong ulVal)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function write's 32-bit Data to CAN_PANCTR Register.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ulVal:

\par \tab \tab 32-bit Data

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab ubyte CAN_ubRequestMsgObj(ubyte ubObjNr)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab If a TRANSMIT OBJECT is to be reconfigured it must first be

\par \tab \tab accessed. The access to the transmit object is exclusive.

\par \tab \tab This function checks whether the choosen message object is

\par \tab \tab still executing a transmit request, or if the object can be

\par \tab \tab accessed exclusively.

\par \tab \tab After the message object is reserved, it can be

\par \tab \tab reconfigured by using the function CAN_vConfigMsgObj or

\par \tab \tab CAN_vLoadData.

\par \tab \tab Both functions enable access to the object for the CAN

\par \tab \tab controller.

\par \tab \tab By calling the function CAN_vTransmit transfering of data

\par \tab \tab is started.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b 0 message object is busy (a transfer is active), else 1\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubObjNr:

\par \tab \tab Number of the message object (0-31)

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vTransmit(ubyte ubObjNr)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab This function triggers the CAN controller to send the

\par \tab \tab selected message.

\par \tab \tab If the selected message object is a TRANSMIT OBJECT then

\par \tab \tab this function triggers the sending of a data frame. If

\par \tab \tab however the selected message object is a RECEIVE OBJECT

\par \tab \tab this function triggers the sending of a remote frame.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubObjNr:

\par \tab \tab Number of the message object (0-31)

\par
\par \plain\f4\fs20\cf0 \tab Function:
\par \plain\f4\fs20\cf0\b \tab \tab void CAN_vLoadData(ubyte ubObjNr, ulong *ulpubData)\plain\f4\fs20\cf0
\par \tab Description:
\par \plain\f4\fs20\cf0\i
\tab \tab If a hardware TRANSMIT OBJECT has to be loaded with data

\par \tab \tab but not with a new identifier, this function may be used

\par \tab \tab instead of the function CAN_vConfigMsgObj. The message

\par \tab \tab object should be accessed by calling the function

\par \tab \tab CAN_ubRequestMsgObj before calling this function. This

\par \tab \tab prevents the CAN controller from working with invalid data.

\par \plain\f4\fs20\cf0 \tab Returnvalue:
\par \tab \tab \plain\f4\fs20\cf0\b None\plain\f4\fs20\cf0
\par \plain\f4\fs20\cf0 \tab Parameters:
\par \plain\f4\fs20\cf0\b 
\tab \tab ubObjNr:

\par \tab \tab Number of the message object to be configured (0-31)

\par \tab \tab *ulpubData:

\par \tab \tab Pointer on a data buffer

\par
\par \plain\f4\fs24\cf0 Initialization:
\par \plain\f4\fs20\cf0\b
\par
\tab Configuration of the Module Clock:\par 
\tab - the CAN module clock = 48.00 MHz\par 
\tab - FCLK runs at 2 times the frequency of PCLK.\par 
\par 
\tab - CMCON - Clock Control Register is Configured in MAIN_vInit\par 
\par 
\tab Configuration of CAN Node 0:\par 
\par 
\tab General Configuration of the Node 0:\par 
\tab - set INIT and CCE\par 
\par 
\tab - load NODE 0 interrupt pointer register\par 
\par 
\tab Configuration of the used CAN Input Port Pins:\par 
\tab - Pin P1.0 is used as RXDC0_0 input\par 
\par 
\tab - Loop-back mode is disabled\par 
\par 
\tab Configuration of the Node 0 Baud Rate:\par 
\tab - required baud rate = 100.000 kbaud\par 
\tab - real baud rate     = 100.000 kbaud\par 
\tab - sample point       = 60.00 %\par 
\tab - there are 5 time quanta before sample point\par 
\tab - there are 4 time quanta after sample point\par 
\tab - the (re)synchronization jump width is 2 time quanta\par 
\par 
\tab Configuration of the Node 0 Error Counter:\par 
\tab - the error warning threshold value (warning level) is 96\par 
\par 
\tab Configuration of the Frame Counter:\par 
\tab - Frame Counter Mode: the counter is incremented upon the reception\par 
\tab and transmission of frames\par 
\tab - frame counter: 0x0000\par 
\par 
\tab Configuration of CAN Node 1:\par 
\par 
\tab General Configuration of the Node 1:\par 
\tab - set INIT and CCE\par 
\par 
\tab Configuration of the used CAN Output Port Pins:\par 
\tab Pin P1.1 is used as TXDC0_0 Output\par 
\tab - NODE1 TXD Pin's are not used\par 
\par 
\tab Configuration of the CAN Message Object List Structure:\par 
\par 
\tab Allocate MOs for list 1:\par 
\par 
\tab Configuration of the CAN Message Objects 0 - 31:\par 
\tab Configuration of Message Object 0:\par 
\tab - message object 0 is valid\par 
\tab - message object is used as receive object\par 
\tab - this message object is assigned to list 1 (node 0)\par 
\par 
\tab - priority class 3; transmit acceptance filtering is based on the list\par 
\tab order (like class 1)\par 
\tab - extended 29-bit identifier\par 
\tab - identifier 29-bit:      0x00000003\par 
\par 
\tab - only accept receive frames with matching IDE bit\par 
\tab - acceptance mask 29-bit: 0x1FFFFFFF\par 
\par 
\tab - use message pending register 0 bit position 0\par 
\par 
\tab - button pointer : MO0\par 
\tab - top pointer : MO2\par 
\tab - current select pointer : MO0\par 
\tab - object select pointer : MO0\par 
\par 
\tab - this object is a RECEIVE FIFO BASE OBJECT\par 
\tab - 8 valid data bytes\par 
\par 
\tab Configuration of Message Object 1:\par 
\tab - message object 1 is valid\par 
\tab - message object is used as receive object\par 
\tab - this message object is assigned to list 1 (node 0)\par 
\par 
\tab - priority class 3; transmit acceptance filtering is based on the list\par 
\tab order (like class 1)\par 
\tab - extended 29-bit identifier\par 
\tab - identifier 29-bit:      0x00000003\par 
\par 
\tab - only accept receive frames with matching IDE bit\par 
\tab - acceptance mask 29-bit: 0x1FFFFFFF\par 
\par 
\tab - use message pending register 0 bit position 1\par 
\par 
\tab - this object is a RECEIVE FIFO SLAVE OBJECT connected to the base\par 
\tab object 0\par 
\tab - 0 valid data bytes\par 
\par 
\tab Configuration of Message Object 2:\par 
\tab - message object 2 is valid\par 
\tab - message object is used as receive object\par 
\tab - this message object is assigned to list 1 (node 0)\par 
\par 
\tab - priority class 3; transmit acceptance filtering is based on the list\par 
\tab order (like class 1)\par 
\tab - extended 29-bit identifier\par 
\tab - identifier 29-bit:      0x00000003\par 
\par 
\tab - only accept receive frames with matching IDE bit\par 
\tab - acceptance mask 29-bit: 0x1FFFFFFF\par 
\par 
\tab - use message pending register 0 bit position 2\par 
\par 
\tab - this object is a RECEIVE FIFO SLAVE OBJECT connected to the base\par 
\tab object 0\par 
\tab - 0 valid data bytes\par 
\par 
\tab Configuration of Message Object 3:\par 
\tab - message object 3 is valid\par 
\tab - message object is used as transmit object\par 
\tab - this message object is assigned to list 1 (node 0)\par 
\par 
\tab - priority class 3; transmit acceptance filtering is based on the list\par 
\tab order (like class 1)\par 
\tab - extended 29-bit identifier\par 
\tab - identifier 29-bit:      0x1FFFFFF0\par 
\par 
\tab - only accept receive frames with matching IDE bit\par 
\tab - acceptance mask 29-bit: 0x1FFFFFFF\par 
\par 
\tab - use message pending register 0 bit position 3\par 
\par 
\tab - this object is a STANDARD MESSAGE OBJECT\par 
\tab - 8 valid data bytes\par 
\par 
\tab Configuration of Message Object 4:\par 
\tab - message object 4 is not valid\par 
\tab Configuration of Message Object 5:\par 
\tab - message object 5 is not valid\par 
\tab Configuration of Message Object 6:\par 
\tab - message object 6 is not valid\par 
\tab Configuration of Message Object 7:\par 
\tab - message object 7 is not valid\par 
\tab Configuration of Message Object 8:\par 
\tab - message object 8 is not valid\par 
\tab Configuration of Message Object 9:\par 
\tab - message object 9 is not valid\par 
\tab Configuration of Message Object 10:\par 
\tab - message object 10 is not valid\par 
\tab Configuration of Message Object 11:\par 
\tab - message object 11 is not valid\par 
\tab Configuration of Message Object 12:\par 
\tab - message object 12 is not valid\par 
\tab Configuration of Message Object 13:\par 
\tab - message object 13 is not valid\par 
\tab Configuration of Message Object 14:\par 
\tab - message object 14 is not valid\par 
\tab Configuration of Message Object 15:\par 
\tab - message object 15 is not valid\par 
\tab Configuration of Message Object 16:\par 
\tab - message object 16 is not valid\par 
\tab Configuration of Message Object 17:\par 
\tab - message object 17 is not valid\par 
\tab Configuration of Message Object 18:\par 
\tab - message object 18 is not valid\par 
\tab Configuration of Message Object 19:\par 
\tab - message object 19 is not valid\par 
\tab Configuration of Message Object 20:\par 
\tab - message object 20 is not valid\par 
\tab Configuration of Message Object 21:\par 
\tab - message object 21 is not valid\par 
\tab Configuration of Message Object 22:\par 
\tab - message object 22 is not valid\par 
\tab Configuration of Message Object 23:\par 
\tab - message object 23 is not valid\par 
\tab Configuration of Message Object 24:\par 
\tab - message object 24 is not valid\par 
\tab Configuration of Message Object 25:\par 
\tab - message object 25 is not valid\par 
\tab Configuration of Message Object 26:\par 
\tab - message object 26 is not valid\par 
\tab Configuration of Message Object 27:\par 
\tab - message object 27 is not valid\par 
\tab Configuration of Message Object 28:\par 
\tab - message object 28 is not valid\par 
\tab Configuration of Message Object 29:\par 
\tab - message object 29 is not valid\par 
\tab Configuration of Message Object 30:\par 
\tab - message object 30 is not valid\par 
\tab Configuration of Message Object 31:\par 
\tab - message object 31 is not valid\par 
\par 
\tab Configuration of the Interrupts:\par 
\tab - CAN interrupt node 0 is disabled\par 
\tab - CAN interrupt node 1 is disabled\par 
\tab - CAN interrupt node 2 is disabled\par 
\tab - CAN interrupt node 3 is disabled\par 
\tab - CAN interrupt node 4 is disabled\par 
\tab - CAN interrupt node 5 is disabled\par 
\tab - CAN interrupt node 6 is disabled\par 
\tab - CAN interrupt node 7 is disabled\par 
\par 

}

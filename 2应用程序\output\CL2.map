LX51 LINKER/LOCATER V4.58                                                               07/13/2013  18:09:34  PAGE 1


LX51 LINKER/LOCATER V4.58, INVOKED BY:
C:\KEIL\C51\BIN\LX51.EXE .\output\START_XC.obj, .\output\MAIN.obj, .\output\SSC.obj, .\output\IO.obj, .\output\SHARED_IN
>> T.obj, .\output\CAN.obj, .\output\T2.obj, .\output\WDT.obj, .\output\SwDriver.obj, .\output\User.obj, .\output\boot.o
>> bj, .\output\XC88x_FLHANDLER.obj TO .\output\CL2.0 CODE (.\output\CL2.cod) PRINT (.\output\CL2.map) IXREF RESERVE (I:
>> 0XC0-I:0XFF) CLASSES (XDATA (X:0XF000-X:0XF5FF), HDATA (X:0XF000-X:0XF5FF), CODE (C:0X2100-C:0X5FFF)) SEGMENTS (?C_C5
>> 1STARTUP (C:0X2100), ?PR?MAIN?MAIN)


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE


INPUT MODULES INCLUDED:
  .\output\START_XC.obj (?C_STARTUP)
         COMMENT TYPE 0: AX51 V3.07d
  .\output\MAIN.obj (MAIN)
         COMMENT TYPE 0: C51 V9.50a
  .\output\SSC.obj (SSC)
         COMMENT TYPE 0: C51 V9.50a
  .\output\IO.obj (IO)
         COMMENT TYPE 0: C51 V9.50a
  .\output\SHARED_INT.obj (SHARED_INT)
         COMMENT TYPE 0: C51 V9.50a
  .\output\CAN.obj (CAN)
         COMMENT TYPE 0: C51 V9.50a
  .\output\T2.obj (T2)
         COMMENT TYPE 0: C51 V9.50a
  .\output\WDT.obj (WDT)
         COMMENT TYPE 0: C51 V9.50a
  .\output\SwDriver.obj (SWDRIVER)
         COMMENT TYPE 0: C51 V9.50a
  .\output\User.obj (USER)
         COMMENT TYPE 0: C51 V9.50a
  .\output\boot.obj (BOOT)
         COMMENT TYPE 0: C51 V9.50a
  .\output\XC88x_FLHANDLER.obj (XC88X_FLHANDLER)
         COMMENT TYPE 0: AX51 V3.07d
  C:\KEIL\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?CILDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?ULCMP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?ULSHR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?OFFXADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?COPY517)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDIDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDXDATA)
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 2


         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDPDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  C:\KEIL\C51\LIB\C51L.LIB (?C?LLDCODE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\output\CL2.0 (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
X:000000H   X:00F000H   X:00F5FFH   000071H   XDATA
X:000000H   X:00F000H   X:00F5FFH             HDATA
C:000000H   C:002100H   C:005FFFH   001C90H   CODE
I:000000H   I:000000H   I:0000FFH   000021H   IDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000000H.6 BIT
C:000000H   C:000000H   C:00FFFFH   000008H   CONST
I:000000H   I:000000H   I:00007FH   000018H   DATA


MEMORY MAP OF MODULE:  .\output\CL2.0 (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   000017H   000010H   ---    ---      **GAP**
000018H   00001FH   000008H   ---    AT..     DATA           "REG BANK 3"
000020H.0 000020H.3 000000H.4 BIT    UNIT     BIT            ?BI?USER
000020H.4 000020H.5 000000H.2 BIT    UNIT     BIT            _BIT_GROUP_
000020H.6 000035H   000015H.2 ---    ---      **GAP**
000036H   00003DH   000008H   ---    OFFS..   DATA           ?DT?XC88X_FLHANDLER?1
00003EH   00007FH   000042H   ---    ---      **GAP**
000080H   00009FH   000020H   BYTE   OFFS..   IDATA          ?ID?BOOT?0
0000A0H   0000A0H   000001H   BYTE   UNIT     IDATA          ?STACK
0000A1H   0000BFH   00001FH   ---    ---      **GAP**
0000C0H   0000FFH   000040H   ---    ---      *** RESERVED MEMORY ***

* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   BYTE   UNIT     CONST          ?CO?BOOT
000008H   001FFFH   001FF8H   ---    ---      **GAP**
002000H   002002H   000003H   ---    OFFS..   CODE           ?CO?START_XC?3
002003H   00202AH   000028H   ---    ---      **GAP**
00202BH   00202DH   000003H   BYTE   OFFS..   CODE           ?SHARED_INT?0202B
00202EH   0020FFH   0000D2H   ---    ---      **GAP**
002100H   0021DAH   0000DBH   BYTE   UNIT     CODE           ?C_C51STARTUP
0021DBH   0021FFH   000025H   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
002200H   00245FH   000260H   BYTE   UNIT     CODE           ?C?LIB_CODE
002460H   0024CCH   00006DH   BYTE   UNIT     CODE           ?C_INITSEG
0024CDH   0024F9H   00002DH   BYTE   UNIT     CODE           ?PR?_DFLERASE?XC88X_FLHANDLER
0024FAH   002519H   000020H   BYTE   UNIT     CODE           ?PR?_FLPROG?XC88X_FLHANDLER
00251AH   00253BH   000022H   BYTE   UNIT     CODE           ?PR?MAIN_VINIT?MAIN
00253CH   00266EH   000133H   BYTE   UNIT     CODE           ?PR?CAN_VINIT?CAN
00266FH   00269CH   00002EH   BYTE   UNIT     CODE           ?PR?_CAN_VWRITEAMDATA?CAN
00269DH   0026C2H   000026H   BYTE   UNIT     CODE           ?L?COM0004
0026C3H   0026D2H   000010H   BYTE   UNIT     CODE           ?PR?_CAN_VSETLISTCOMMAND?CAN
0026D3H   0026DBH   000009H   BYTE   UNIT     CODE           ?L?COM003A
0026DCH   0026E2H   000007H   BYTE   UNIT     CODE           ?L?COM004B
0026E3H   0026EBH   000009H   BYTE   UNIT     CODE           ?L?COM0053
0026ECH   0026F4H   000009H   BYTE   UNIT     CODE           ?L?COM0054
0026F5H   0026F9H   000005H   BYTE   UNIT     CODE           ?PR?WDT_VINIT?WDT
0026FAH   002706H   00000DH   BYTE   UNIT     CODE           ?L?COM0043
002707H   00270FH   000009H   BYTE   UNIT     CODE           ?PR?CAN_SETWDTRESET?BOOT
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 3


002710H   00271CH   00000DH   BYTE   UNIT     CODE           ?L?COM0044
00271DH   002741H   000025H   BYTE   UNIT     CODE           ?PR?FLASH_WAIT?BOOT
002742H   0027EFH   0000AEH   BYTE   UNIT     CODE           ?PR?BOOTMAIN?BOOT
0027F0H   0027F3H   000004H   BYTE   UNIT     CODE           ?PR?SHINT_VINIT?SHARED_INT
0027F4H   0027FAH   000007H   BYTE   UNIT     CODE           ?PR?IO_VINIT?IO
0027FBH   00281EH   000024H   BYTE   UNIT     CODE           ?PR?SSC_VINIT?SSC
00281FH   002821H   000003H   BYTE   UNIT     CODE           ?PR?_SSC_VSENDDATA?SSC
002822H   00284FH   00002EH   BYTE   UNIT     CODE           ?PR?_MC33993DATASENDRCV?SWDRIVER
002850H   002852H   000003H   BYTE   UNIT     CODE           ?PR?SSC_VGETDATA?SSC
002853H   00294EH   0000FCH   BYTE   UNIT     CODE           ?PR?_MC33993CMDSEND?SWDRIVER
00294FH   00295EH   000010H   BYTE   UNIT     CODE           ?L?COM0012
00295FH   0029A6H   000048H   BYTE   UNIT     CODE           ?PR?INITSW?SWDRIVER
0029A7H   002A22H   00007CH   BYTE   UNIT     CODE           ?PR?INITMC33993SEC?SWDRIVER
002A23H   002A2EH   00000CH   BYTE   UNIT     CODE           ?L?COM0029
002A2FH   002A3CH   00000EH   BYTE   UNIT     CODE           ?L?COM0021
002A3DH   002A45H   000009H   BYTE   UNIT     CODE           ?L?COM0034
002A46H   002AFAH   0000B5H   BYTE   UNIT     CODE           ?PR?_SWSTASAMPLE?SWDRIVER
002AFBH   002B0EH   000014H   BYTE   UNIT     CODE           ?L?COM0001
002B0FH   002DEDH   0002DFH   BYTE   UNIT     CODE           ?PR?_LEDLIGHT?SWDRIVER
002DEEH   002DF9H   00000CH   BYTE   UNIT     CODE           ?L?COM001C
002DFAH   002E02H   000009H   BYTE   UNIT     CODE           ?L?COM0030
002E03H   002E12H   000010H   BYTE   UNIT     CODE           ?L?COM0014
002E13H   002E1EH   00000CH   BYTE   UNIT     CODE           ?L?COM0024
002E1FH   002E2CH   00000EH   BYTE   UNIT     CODE           ?L?COM002C
002E2DH   002E38H   00000CH   BYTE   UNIT     CODE           ?L?COM0033
002E39H   002E49H   000011H   BYTE   UNIT     CODE           ?L?COM002B
002E4AH   002E50H   000007H   BYTE   UNIT     CODE           ?L?COM0062
002E51H   002E59H   000009H   BYTE   UNIT     CODE           ?L?COM003B
002E5AH   002E61H   000008H   BYTE   UNIT     CODE           ?L?COM0055
002E62H   002E69H   000008H   BYTE   UNIT     CODE           ?L?COM0056
002E6AH   002E72H   000009H   BYTE   UNIT     CODE           ?L?COM0051
002E73H   002E83H   000011H   BYTE   UNIT     CODE           ?PR?_CHANGECSMC33993?SWDRIVER
002E84H   003048H   0001C5H   BYTE   UNIT     CODE           ?PR?_CAN_VGETMSGOBJ?CAN
003049H   003059H   000011H   BYTE   UNIT     CODE           ?L?COM0017
00305AH   003062H   000009H   BYTE   UNIT     CODE           ?L?COM0040
003063H   00306FH   00000DH   BYTE   UNIT     CODE           ?L?COM0045
003070H   003081H   000012H   BYTE   UNIT     CODE           ?L?COM003C
003082H   0030AEH   00002DH   BYTE   UNIT     CODE           ?PR?_CAN_VRELEASEOBJ?CAN
0030AFH   0030EEH   000040H   BYTE   UNIT     CODE           ?PR?_CAN_UBREQUESTMSGOBJ?CAN
0030EFH   0030F7H   000009H   BYTE   UNIT     CODE           ?L?COM0052
0030F8H   00312AH   000033H   BYTE   UNIT     CODE           ?PR?_CAN_WAITTRANSMIT?BOOT
00312BH   003142H   000018H   BYTE   UNIT     CODE           ?L?COM0010
003143H   00332EH   0001ECH   BYTE   UNIT     CODE           ?PR?SWSAMPLE?USER
00332FH   003353H   000025H   BYTE   UNIT     CODE           ?L?COM0009
003354H   00335BH   000008H   BYTE   UNIT     CODE           ?L?COM002E
00335CH   003377H   00001CH   BYTE   UNIT     CODE           ?L?COM000E
003378H   0034ECH   000175H   BYTE   UNIT     CODE           ?PR?CANTX?USER
0034EDH   0034FEH   000012H   BYTE   UNIT     CODE           ?PR?CANTRANSMIT?USER
0034FFH   003531H   000033H   BYTE   UNIT     CODE           ?PR?_CAN_VTRANSMIT?CAN
003532H   003544H   000013H   BYTE   UNIT     CODE           ?L?COM000C
003545H   00355BH   000017H   BYTE   UNIT     CODE           ?L?COM0026
00355CH   003565H   00000AH   BYTE   UNIT     CODE           ?L?COM0036
003566H   003578H   000013H   BYTE   UNIT     CODE           ?L?COM001E
003579H   00357FH   000007H   BYTE   UNIT     CODE           ?L?COM0042
003580H   00358AH   00000BH   BYTE   UNIT     CODE           ?L?COM0038
00358BH   003599H   00000FH   BYTE   UNIT     CODE           ?L?COM0031
00359AH   0035A0H   000007H   BYTE   UNIT     CODE           ?L?COM004F
0035A1H   003600H   000060H   BYTE   UNIT     CODE           ?PR?PASSWORD?USER
003601H   003619H   000019H   BYTE   UNIT     CODE           ?L?COM0015
00361AH   003621H   000008H   BYTE   UNIT     CODE           ?L?COM0060
003622H   003629H   000008H   BYTE   UNIT     CODE           ?L?COM0061
00362AH   003635H   00000CH   BYTE   UNIT     CODE           ?L?COM0049
003636H   003646H   000011H   BYTE   UNIT     CODE           ?L?COM003F
003647H   003683H   00003DH   BYTE   UNIT     CODE           ?PR?_CAN_SENDACK?BOOT
003684H   0036F0H   00006DH   BYTE   UNIT     CODE           ?PR?_CAN_VLOADDATA?CAN
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 4


0036F1H   003708H   000018H   BYTE   UNIT     CODE           ?L?COM0037
003709H   00377AH   000072H   BYTE   UNIT     CODE           ?PR?CANRXTX?USER
00377BH   0037ADH   000033H   BYTE   UNIT     CODE           ?PR?_CAN_UBNEWDATA?CAN
0037AEH   0037B5H   000008H   BYTE   UNIT     CODE           ?L?COM005F
0037B6H   00380FH   00005AH   BYTE   UNIT     CODE           ?PR?SHINT_VIXINTR5ISR?SHARED_INT
003810H   003821H   000012H   BYTE   UNIT     CODE           ?PR?T2_VINIT?T2
003822H   00383CH   00001BH   BYTE   UNIT     CODE           ?PR?WDT_VDISABLE?WDT
00383DH   003A75H   000239H   BYTE   UNIT     CODE           ?PR?_MC33993CMDSENDONEBIT?SWDRIVER
003A76H   003A90H   00001BH   BYTE   UNIT     CODE           ?L?COM001A
003A91H   003A97H   000007H   BYTE   UNIT     CODE           ?L?COM0032
003A98H   003A9FH   000008H   BYTE   UNIT     CODE           ?L?COM002F
003AA0H   003AABH   00000CH   BYTE   UNIT     CODE           ?L?COM001F
003AACH   003AB5H   00000AH   BYTE   UNIT     CODE           ?L?COM0050
003AB6H   003AC3H   00000EH   BYTE   UNIT     CODE           ?L?COM0041
003AC4H   003ACBH   000008H   BYTE   UNIT     CODE           ?L?COM0047
003ACCH   003AD3H   000008H   BYTE   UNIT     CODE           ?L?COM0048
003AD4H   003B06H   000033H   BYTE   UNIT     CODE           ?PR?RTCPROCESS?USER
003B07H   003B82H   00007CH   BYTE   UNIT     CODE           ?PR?CANERRORPROCESS?USER
003B83H   003D0CH   00018AH   BYTE   UNIT     CODE           ?PR?LEDDR?USER
003D0DH   003D14H   000008H   BYTE   UNIT     CODE           ?L?COM004A
003D15H   003D1EH   00000AH   BYTE   UNIT     CODE           ?L?COM003D
003D1FH   003D2BH   00000DH   BYTE   UNIT     CODE           ?L?COM0046
003D2CH   003D35H   00000AH   BYTE   UNIT     CODE           ?L?COM004C
003D36H   003D40H   00000BH   BYTE   UNIT     CODE           ?L?COM004D
003D41H   003D49H   000009H   BYTE   UNIT     CODE           ?L?COM004E
003D4AH   003D51H   000008H   BYTE   UNIT     CODE           ?L?COM0057
003D52H   003D59H   000008H   BYTE   UNIT     CODE           ?L?COM0058
003D5AH   003D61H   000008H   BYTE   UNIT     CODE           ?L?COM0059
003D62H   003D69H   000008H   BYTE   UNIT     CODE           ?L?COM005A
003D6AH   003D71H   000008H   BYTE   UNIT     CODE           ?L?COM005B
003D72H   003D79H   000008H   BYTE   UNIT     CODE           ?L?COM005C
003D7AH   003D81H   000008H   BYTE   UNIT     CODE           ?L?COM005D
003D82H   003D89H   000008H   BYTE   UNIT     CODE           ?L?COM005E

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
00F000H   00F02AH   00002BH   BYTE   UNIT     XDATA          _XDATA_GROUP_
00F02BH   00F054H   00002AH   BYTE   UNIT     XDATA          ?XD?USER
00F055H   00F070H   00001CH   BYTE   UNIT     XDATA          ?XD?SWDRIVER



OVERLAY MAP OF MODULE:   .\output\CL2.0 (?C_STARTUP)


FUNCTION/MODULE                      BIT_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE          START  STOP  START  STOP
============================================================
?C_C51STARTUP                       ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                           ----- -----  ----- -----
  +--> MAIN_VINIT/MAIN
  +--> INITSW/SWDRIVER
  +--> SWSAMPLE/USER
  +--> CANRXTX/USER
  +--> CANERRORPROCESS/USER
  +--> BOOTMAIN/BOOT

MAIN_VINIT/MAIN                     ----- -----  ----- -----
  +--> IO_VINIT/IO
  +--> T2_VINIT/T2
  +--> WDT_VINIT/WDT
  +--> SSC_VINIT/SSC
  +--> CAN_VINIT/CAN
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 5


  +--> SHINT_VINIT/SHARED_INT

IO_VINIT/IO                         ----- -----  ----- -----

T2_VINIT/T2                         ----- -----  ----- -----

WDT_VINIT/WDT                       ----- -----  ----- -----

SSC_VINIT/SSC                       ----- -----  ----- -----

CAN_VINIT/CAN                       ----- -----  ----- -----
  +--> _CAN_VSETLISTCOMMAND/CAN
  +--> _CAN_VWRITEAMDATA/CAN

_CAN_VSETLISTCOMMAND/CAN            ----- -----  ----- -----
  +--> _CAN_VWRITEAMDATA/CAN

_CAN_VWRITEAMDATA/CAN               ----- -----  F003H F006H

SHINT_VINIT/SHARED_INT              ----- -----  ----- -----

INITSW/SWDRIVER                     ----- -----  F000H F000H
  +--> _MC33993CMDSEND/SWDRIVER

_MC33993CMDSEND/SWDRIVER            ----- -----  F01EH F027H
  +--> _CHANGECSMC33993/SWDRIVER
  +--> _MC33993DATASENDRCV/SWDRIVER

_CHANGECSMC33993/SWDRIVER           20H.5 20H.5  ----- -----

_MC33993DATASENDRCV/SWDRIVER        ----- -----  ----- -----
  +--> _SSC_VSENDDATA/SSC
  +--> SSC_VGETDATA/SSC

_SSC_VSENDDATA/SSC                  ----- -----  ----- -----

SSC_VGETDATA/SSC                    ----- -----  ----- -----

SWSAMPLE/USER                       ----- -----  F000H F007H
  +--> _SWSTASAMPLE/SWDRIVER
  +--> CANTX/USER

_SWSTASAMPLE/SWDRIVER               ----- -----  F008H F00FH
  +--> INITMC33993SEC/SWDRIVER
  +--> _MC33993CMDSEND/SWDRIVER

INITMC33993SEC/SWDRIVER             ----- -----  F010H F010H
  +--> _MC33993CMDSEND/SWDRIVER

CANTX/USER                          ----- -----  ----- -----
  +--> CANTRANSMIT/USER

CANTRANSMIT/USER                    ----- -----  ----- -----
  +--> _CAN_WAITTRANSMIT/BOOT
  +--> PASSWORD/USER
  +--> _CAN_VLOADDATA/CAN
  +--> _CAN_VTRANSMIT/CAN

_CAN_WAITTRANSMIT/BOOT              ----- -----  F026H F02AH
  +--> _CAN_UBREQUESTMSGOBJ/CAN

_CAN_UBREQUESTMSGOBJ/CAN            ----- -----  ----- -----

PASSWORD/USER                       ----- -----  ----- -----

LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 6


_CAN_VLOADDATA/CAN                  ----- -----  ----- -----

_CAN_VTRANSMIT/CAN                  ----- -----  ----- -----

CANRXTX/USER                        ----- -----  F000H F013H
  +--> _CAN_UBNEWDATA/CAN
  +--> _CAN_VGETMSGOBJ/CAN
  +--> _CAN_VRELEASEOBJ/CAN
  +--> LEDDR/USER
  +--> CANTX/USER

_CAN_UBNEWDATA/CAN                  ----- -----  ----- -----

_CAN_VGETMSGOBJ/CAN                 ----- -----  F01CH F01FH

_CAN_VRELEASEOBJ/CAN                ----- -----  ----- -----

LEDDR/USER                          ----- -----  ----- -----
  +--> _LEDLIGHT/SWDRIVER

_LEDLIGHT/SWDRIVER                  ----- -----  F014H F018H
  +--> _MC33993CMDSENDONEBIT/SWDRIVER

_MC33993CMDSENDONEBIT/SWDRIVER      20H.4 20H.4  F019H F01DH
  +--> _MC33993CMDSEND/SWDRIVER

CANERRORPROCESS/USER                ----- -----  F000H F002H
  +--> CAN_VINIT/CAN

BOOTMAIN/BOOT                       ----- -----  F000H F01BH
  +--> _CAN_UBNEWDATA/CAN
  +--> _CAN_VGETMSGOBJ/CAN
  +--> _CAN_VRELEASEOBJ/CAN
  +--> WDT_VDISABLE/WDT
  +--> _CAN_SENDACK/BOOT
  +--> CAN_SETWDTRESET/BOOT
  +--> _DFLERASE/XC88X_FLHANDLER
  +--> FLASH_WAIT/BOOT
  +--> _FLPROG/XC88X_FLHANDLER

WDT_VDISABLE/WDT                    ----- -----  ----- -----

_CAN_SENDACK/BOOT                   ----- -----  F01CH F025H
  +--> ?CO?BOOT
  +--> _CAN_VLOADDATA/CAN
  +--> _CAN_VTRANSMIT/CAN
  +--> _CAN_WAITTRANSMIT/BOOT

?CO?BOOT                            ----- -----  ----- -----

CAN_SETWDTRESET/BOOT                ----- -----  ----- -----

_DFLERASE/XC88X_FLHANDLER           ----- -----  ----- -----

FLASH_WAIT/BOOT                     ----- -----  ----- -----
  +--> CAN_SETWDTRESET/BOOT

_FLPROG/XC88X_FLHANDLER             ----- -----  ----- -----

?C_INITSEG                          ----- -----  ----- -----

*** NEW ROOT **********************

SHINT_VIXINTR5ISR/SHARED_INT        ----- -----  ----- -----
  +--> RTCPROCESS/USER
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 7



RTCPROCESS/USER                     ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\output\CL2.0 (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      00000020H.5 BIT      BIT       ?_ChangeCsMc33993?BIT
      0200F01EH   XDATA    BYTE      ?_Mc33993CmdSend?BYTE
      00000020H.4 BIT      BIT       ?_Mc33993CmdSendOneBit?BIT
      01002246H   CODE     ---       ?C?CILDOPTR
      01002219H   CODE     ---       ?C?CLDOPTR
      01002200H   CODE     ---       ?C?CLDPTR
      00000000H   NUMBER   ---       ?C?CODESEG
      01002406H   CODE     ---       ?C?COPY517
      0100228BH   CODE     ---       ?C?CSTOPTR
      01002279H   CODE     ---       ?C?CSTPTR
      000000A2H   DATA     BYTE      ?C?DPSEL
      01002450H   CODE     ---       ?C?LLDCODE
      0100242CH   CODE     ---       ?C?LLDIDATA
      01002444H   CODE     ---       ?C?LLDPDATA
      010022D1H   CODE     ---       ?C?LLDPTR
      01002438H   CODE     ---       ?C?LLDXDATA
      010022FDH   CODE     ---       ?C?LSTKXDATA
      010022F1H   CODE     ---       ?C?LSTXDATA
      0100232EH   CODE     ---       ?C?OFFXADD
      010022ADH   CODE     ---       ?C?ULCMP
      010022BEH   CODE     ---       ?C?ULSHR
      00000000H   NUMBER   ---       ?C?XDATASEG
      01002196H   CODE     ---       ?C_START
      01002000H   CODE     NEAR LAB  ?C_STARTUP
      01003647H   CODE     ---       _CAN_sendAck
      0100377BH   CODE     ---       _CAN_ubNewData
      010030AFH   CODE     ---       _CAN_ubRequestMsgObj
      01002E84H   CODE     ---       _CAN_vGetMsgObj
      01003684H   CODE     ---       _CAN_vLoadData
      01003082H   CODE     ---       _CAN_vReleaseObj
      010026C3H   CODE     ---       _CAN_vSetListCommand
      010034FFH   CODE     ---       _CAN_vTransmit
      01002677H   CODE     ---       _CAN_vWriteAMData
      010030F8H   CODE     ---       _CAN_waitTransmit
      01002E73H   CODE     ---       _ChangeCsMc33993
      010024CDH   CODE     NEAR LAB  _DFlErase
      010024FAH   CODE     NEAR LAB  _FlProg
      01002B0FH   CODE     ---       _LedLight
      01002853H   CODE     ---       _Mc33993CmdSend
      0100383DH   CODE     ---       _Mc33993CmdSendOneBit
      01002822H   CODE     ---       _Mc33993DataSendRcv
      0100281FH   CODE     ---       _SSC_vSendData
      01002A46H   CODE     ---       _SwStaSample
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
*SFR* 000000CAH   DATA     BYTE      ADC_CHCTR0
*SFR* 000000CBH   DATA     BYTE      ADC_CHCTR1
*SFR* 000000CCH   DATA     BYTE      ADC_CHCTR2
*SFR* 000000CDH   DATA     BYTE      ADC_CHCTR3
*SFR* 000000CEH   DATA     BYTE      ADC_CHCTR4
*SFR* 000000CFH   DATA     BYTE      ADC_CHCTR5
*SFR* 000000D2H   DATA     BYTE      ADC_CHCTR6
*SFR* 000000D3H   DATA     BYTE      ADC_CHCTR7
*SFR* 000000CBH   DATA     BYTE      ADC_CHINCR
*SFR* 000000CAH   DATA     BYTE      ADC_CHINFR
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 8


*SFR* 000000CDH   DATA     BYTE      ADC_CHINPR
*SFR* 000000CCH   DATA     BYTE      ADC_CHINSR
*SFR* 000000CAH   DATA     BYTE      ADC_CRCR1
*SFR* 000000CCH   DATA     BYTE      ADC_CRMR1
*SFR* 000000CBH   DATA     BYTE      ADC_CRPR1
*SFR* 000000CFH   DATA     BYTE      ADC_ETRCR
*SFR* 000000CFH   DATA     BYTE      ADC_EVINCR
*SFR* 000000CEH   DATA     BYTE      ADC_EVINFR
*SFR* 000000D3H   DATA     BYTE      ADC_EVINPR
*SFR* 000000D2H   DATA     BYTE      ADC_EVINSR
*SFR* 000000CAH   DATA     BYTE      ADC_GLOBCTR
*SFR* 000000CBH   DATA     BYTE      ADC_GLOBSTR
*SFR* 000000CEH   DATA     BYTE      ADC_INPCR0
*SFR* 000000CDH   DATA     BYTE      ADC_LCBR
*SFR* 000000D1H   DATA     BYTE      ADC_PAGE
*SFR* 000000CCH   DATA     BYTE      ADC_PRAR
*SFR* 000000CFH   DATA     BYTE      ADC_Q0R0
*SFR* 000000D2H   DATA     BYTE      ADC_QBUR0
*SFR* 000000D2H   DATA     BYTE      ADC_QINR0
*SFR* 000000CDH   DATA     BYTE      ADC_QMR0
*SFR* 000000CEH   DATA     BYTE      ADC_QSR0
*SFR* 000000CAH   DATA     BYTE      ADC_RCR0
*SFR* 000000CBH   DATA     BYTE      ADC_RCR1
*SFR* 000000CCH   DATA     BYTE      ADC_RCR2
*SFR* 000000CDH   DATA     BYTE      ADC_RCR3
*SFR* 000000CBH   DATA     BYTE      ADC_RESR0H
*SFR* 000000CAH   DATA     BYTE      ADC_RESR0L
*SFR* 000000CAH   DATA     WORD      ADC_RESR0LH
*SFR* 000000CDH   DATA     BYTE      ADC_RESR1H
*SFR* 000000CCH   DATA     BYTE      ADC_RESR1L
*SFR* 000000CCH   DATA     WORD      ADC_RESR1LH
*SFR* 000000CFH   DATA     BYTE      ADC_RESR2H
*SFR* 000000CEH   DATA     BYTE      ADC_RESR2L
*SFR* 000000CEH   DATA     WORD      ADC_RESR2LH
*SFR* 000000D3H   DATA     BYTE      ADC_RESR3H
*SFR* 000000D2H   DATA     BYTE      ADC_RESR3L
*SFR* 000000D2H   DATA     WORD      ADC_RESR3LH
*SFR* 000000CBH   DATA     BYTE      ADC_RESRA0H
*SFR* 000000CAH   DATA     BYTE      ADC_RESRA0L
*SFR* 000000CAH   DATA     WORD      ADC_RESRA0LH
*SFR* 000000CDH   DATA     BYTE      ADC_RESRA1H
*SFR* 000000CCH   DATA     BYTE      ADC_RESRA1L
*SFR* 000000CCH   DATA     WORD      ADC_RESRA1LH
*SFR* 000000CFH   DATA     BYTE      ADC_RESRA2H
*SFR* 000000CEH   DATA     BYTE      ADC_RESRA2L
*SFR* 000000CEH   DATA     WORD      ADC_RESRA2LH
*SFR* 000000D3H   DATA     BYTE      ADC_RESRA3H
*SFR* 000000D2H   DATA     BYTE      ADC_RESRA3L
*SFR* 000000D2H   DATA     WORD      ADC_RESRA3LH
*SFR* 000000CEH   DATA     BYTE      ADC_VFCR
*SFR* 000000F0H   DATA     BYTE      B
*SFR* 000000BDH   DATA     BYTE      BCON
*SFR* 000000BEH   DATA     BYTE      BG
      01002742H   CODE     ---       BootMain
*SFR* 000000C0H.1 DATA     BIT       C_T2
*SFR* 000000D8H   DATA     BYTE      CAN_ADCON
*SFR* 000000DAH   DATA     BYTE      CAN_ADH
*SFR* 000000D9H   DATA     BYTE      CAN_ADL
*SFR* 000000D9H   DATA     WORD      CAN_ADLH
*SFR* 000000DBH   DATA     BYTE      CAN_DATA0
*SFR* 000000DBH   DATA     WORD      CAN_DATA01
*SFR* 000000DCH   DATA     BYTE      CAN_DATA1
*SFR* 000000DDH   DATA     BYTE      CAN_DATA2
*SFR* 000000DDH   DATA     WORD      CAN_DATA23
*SFR* 000000DEH   DATA     BYTE      CAN_DATA3
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 9


      01002707H   CODE     ---       CAN_setWDTReset
      0100253CH   CODE     ---       CAN_vInit
      01003B07H   CODE     ---       CanErrorProcess
      01003709H   CODE     ---       CanRXTX
      010034EDH   CODE     ---       CanTransmit
      01003378H   CODE     ---       CanTX
*SFR* 000000FBH   DATA     BYTE      CCU6_CC60RH
*SFR* 000000FAH   DATA     BYTE      CCU6_CC60RL
*SFR* 000000FAH   DATA     WORD      CCU6_CC60RLH
*SFR* 000000FBH   DATA     BYTE      CCU6_CC60SRH
*SFR* 000000FAH   DATA     BYTE      CCU6_CC60SRL
*SFR* 000000FAH   DATA     WORD      CCU6_CC60SRLH
*SFR* 000000FDH   DATA     BYTE      CCU6_CC61RH
*SFR* 000000FCH   DATA     BYTE      CCU6_CC61RL
*SFR* 000000FCH   DATA     WORD      CCU6_CC61RLH
*SFR* 000000FDH   DATA     BYTE      CCU6_CC61SRH
*SFR* 000000FCH   DATA     BYTE      CCU6_CC61SRL
*SFR* 000000FCH   DATA     WORD      CCU6_CC61SRLH
*SFR* 000000FFH   DATA     BYTE      CCU6_CC62RH
*SFR* 000000FEH   DATA     BYTE      CCU6_CC62RL
*SFR* 000000FEH   DATA     WORD      CCU6_CC62RLH
*SFR* 000000FFH   DATA     BYTE      CCU6_CC62SRH
*SFR* 000000FEH   DATA     BYTE      CCU6_CC62SRL
*SFR* 000000FEH   DATA     WORD      CCU6_CC62SRLH
*SFR* 0000009BH   DATA     BYTE      CCU6_CC63RH
*SFR* 0000009AH   DATA     BYTE      CCU6_CC63RL
*SFR* 0000009AH   DATA     WORD      CCU6_CC63RLH
*SFR* 0000009BH   DATA     BYTE      CCU6_CC63SRH
*SFR* 0000009AH   DATA     BYTE      CCU6_CC63SRL
*SFR* 0000009AH   DATA     WORD      CCU6_CC63SRLH
*SFR* 000000A7H   DATA     BYTE      CCU6_CMPMODIFH
*SFR* 000000A6H   DATA     BYTE      CCU6_CMPMODIFL
*SFR* 000000FFH   DATA     BYTE      CCU6_CMPSTATH
*SFR* 000000FEH   DATA     BYTE      CCU6_CMPSTATL
*SFR* 0000009DH   DATA     BYTE      CCU6_IENH
*SFR* 0000009CH   DATA     BYTE      CCU6_IENL
*SFR* 0000009FH   DATA     BYTE      CCU6_INPH
*SFR* 0000009EH   DATA     BYTE      CCU6_INPL
*SFR* 0000009DH   DATA     BYTE      CCU6_ISH
*SFR* 0000009CH   DATA     BYTE      CCU6_ISL
*SFR* 000000A5H   DATA     BYTE      CCU6_ISRH
*SFR* 000000A4H   DATA     BYTE      CCU6_ISRL
*SFR* 000000A5H   DATA     BYTE      CCU6_ISSH
*SFR* 000000A4H   DATA     BYTE      CCU6_ISSL
*SFR* 000000A7H   DATA     BYTE      CCU6_MCMCTR
*SFR* 0000009BH   DATA     BYTE      CCU6_MCMOUTH
*SFR* 0000009AH   DATA     BYTE      CCU6_MCMOUTL
*SFR* 0000009FH   DATA     BYTE      CCU6_MCMOUTSH
*SFR* 0000009EH   DATA     BYTE      CCU6_MCMOUTSL
*SFR* 000000FDH   DATA     BYTE      CCU6_MODCTRH
*SFR* 000000FCH   DATA     BYTE      CCU6_MODCTRL
*SFR* 000000A3H   DATA     BYTE      CCU6_PAGE
*SFR* 0000009FH   DATA     BYTE      CCU6_PISEL0H
*SFR* 0000009EH   DATA     BYTE      CCU6_PISEL0L
*SFR* 000000A4H   DATA     BYTE      CCU6_PISEL2
*SFR* 000000A6H   DATA     BYTE      CCU6_PSLR
*SFR* 000000A5H   DATA     BYTE      CCU6_T12DTCH
*SFR* 000000A4H   DATA     BYTE      CCU6_T12DTCL
*SFR* 000000FBH   DATA     BYTE      CCU6_T12H
*SFR* 000000FAH   DATA     BYTE      CCU6_T12L
*SFR* 000000FAH   DATA     WORD      CCU6_T12LH
*SFR* 0000009BH   DATA     BYTE      CCU6_T12MSELH
*SFR* 0000009AH   DATA     BYTE      CCU6_T12MSELL
*SFR* 0000009DH   DATA     BYTE      CCU6_T12PRH
*SFR* 0000009CH   DATA     BYTE      CCU6_T12PRL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 10


*SFR* 0000009CH   DATA     WORD      CCU6_T12PRLH
*SFR* 000000FDH   DATA     BYTE      CCU6_T13H
*SFR* 000000FCH   DATA     BYTE      CCU6_T13L
*SFR* 000000FCH   DATA     WORD      CCU6_T13LH
*SFR* 0000009FH   DATA     BYTE      CCU6_T13PRH
*SFR* 0000009EH   DATA     BYTE      CCU6_T13PRL
*SFR* 0000009EH   DATA     WORD      CCU6_T13PRLH
*SFR* 000000A7H   DATA     BYTE      CCU6_TCTR0H
*SFR* 000000A6H   DATA     BYTE      CCU6_TCTR0L
*SFR* 000000FBH   DATA     BYTE      CCU6_TCTR2H
*SFR* 000000FAH   DATA     BYTE      CCU6_TCTR2L
*SFR* 0000009DH   DATA     BYTE      CCU6_TCTR4H
*SFR* 0000009CH   DATA     BYTE      CCU6_TCTR4L
*SFR* 000000FFH   DATA     BYTE      CCU6_TRPCTRH
*SFR* 000000FEH   DATA     BYTE      CCU6_TRPCTRL
*SFR* 000000A0H   DATA     BIT       CD_BSY
*SFR* 000000A1H   DATA     BYTE      CD_CON
*SFR* 0000009BH   DATA     BYTE      CD_CORDXH
*SFR* 0000009AH   DATA     BYTE      CD_CORDXL
*SFR* 0000009DH   DATA     BYTE      CD_CORDYH
*SFR* 0000009CH   DATA     BYTE      CD_CORDYL
*SFR* 0000009FH   DATA     BYTE      CD_CORDZH
*SFR* 0000009EH   DATA     BYTE      CD_CORDZL
*SFR* 000000A0H   DATA     BYTE      CD_STATC
*SFR* 000000BAH   DATA     BYTE      CMCON
      0200F04FH   XDATA    WORD      CntCan_1
*SFR* 000000BEH   DATA     BYTE      COCON
*SFR* 000000C0H   DATA     BIT       CP_RL2
*SFR* 000000D0H.7 DATA     BIT       CY
*SFR* 000000A0H.4 DATA     BIT       DMAP
*SFR* 00000083H   DATA     BYTE      DPH
*SFR* 00000082H   DATA     BYTE      DPL
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000E8H   DATA     BIT       EADC
*SFR* 000000E8H.4 DATA     BIT       ECCIP0
*SFR* 000000E8H.5 DATA     BIT       ECCIP1
*SFR* 000000E8H.6 DATA     BIT       ECCIP2
*SFR* 000000E8H.7 DATA     BIT       ECCIP3
*SFR* 000000A2H   DATA     BYTE      EO
*SFR* 000000A0H.2 DATA     BIT       EOC
*SFR* 000000A0H.1 DATA     BIT       ERROR
*SFR* 000000A8H.4 DATA     BIT       ES
*SFR* 000000E8H.1 DATA     BIT       ESSC
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H   DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000E8H.2 DATA     BIT       EX2
*SFR* 000000C0H.3 DATA     BIT       EXEN2
*SFR* 000000C0H.6 DATA     BIT       EXF2
*SFR* 000000B7H   DATA     BYTE      EXICON0
*SFR* 000000BAH   DATA     BYTE      EXICON1
*SFR* 000000E8H.3 DATA     BIT       EXM
*SFR* 000000D0H.5 DATA     BIT       F0
*SFR* 000000D0H.1 DATA     BIT       F1
*SFR* 000000E9H   DATA     BYTE      FDCON
*SFR* 000000EBH   DATA     BYTE      FDRES
*SFR* 000000EAH   DATA     BYTE      FDSTEP
*SFR* 000000BDH   DATA     BYTE      FEAH
*SFR* 000000BCH   DATA     BYTE      FEAL
      0100271DH   CODE     ---       Flash_Wait
      00000020H.2 BIT      BIT       FlgCan_1
      00000020H.3 BIT      BIT       FlgOneTime
*SFR* 000000F7H   DATA     BYTE      HWBPDR
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 11


*SFR* 000000F6H   DATA     BYTE      HWBPSR
*SFR* 000000B3H   DATA     BYTE      ID
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*SFR* 000000A8H   DATA     BYTE      IEN0
*SFR* 000000E8H   DATA     BYTE      IEN1
*SFR* 000000B0H.1 DATA     BIT       IERR
      010029A7H   CODE     ---       InitMc33993Sec
      0100295FH   CODE     ---       InitSW
*SFR* 000000A0H.3 DATA     BIT       INT_EN
      010027F4H   CODE     ---       IO_vInit
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 000000F8H   DATA     BYTE      IP1
*SFR* 000000B9H   DATA     BYTE      IPH
*SFR* 000000F9H   DATA     BYTE      IPH1
*SFR* 000000B4H   DATA     BYTE      IRCON0
*SFR* 000000B5H   DATA     BYTE      IRCON1
*SFR* 000000B6H   DATA     BYTE      IRCON2
*SFR* 000000B4H   DATA     BYTE      IRCON3
*SFR* 000000B5H   DATA     BYTE      IRCON4
*SFR* 000000B0H   DATA     BIT       IRDY
*SFR* 00000088H   DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
*SFR* 000000A0H.5 DATA     BIT       KEEPX
*SFR* 000000A0H.6 DATA     BIT       KEEPY
*SFR* 000000A0H.7 DATA     BIT       KEEPZ
      01003B83H   CODE     ---       LedDr
      010021DBH   CODE     ---       main
      0100251AH   CODE     ---       MAIN_vInit
*SFR* 000000B0H.2 DATA     BIT       MDU_BSY
*SFR* 000000B2H   DATA     BYTE      MDU_MD0
*SFR* 000000B3H   DATA     BYTE      MDU_MD1
*SFR* 000000B4H   DATA     BYTE      MDU_MD2
*SFR* 000000B5H   DATA     BYTE      MDU_MD3
*SFR* 000000B6H   DATA     BYTE      MDU_MD4
*SFR* 000000B7H   DATA     BYTE      MDU_MD5
*SFR* 000000B1H   DATA     BYTE      MDU_MDUCON
*SFR* 000000B0H   DATA     BYTE      MDU_MDUSTAT
*SFR* 000000B2H   DATA     BYTE      MDU_MR0
*SFR* 000000B3H   DATA     BYTE      MDU_MR1
*SFR* 000000B4H   DATA     BYTE      MDU_MR2
*SFR* 000000B5H   DATA     BYTE      MDU_MR3
*SFR* 000000B6H   DATA     BYTE      MDU_MR4
*SFR* 000000B7H   DATA     BYTE      MDU_MR5
*SFR* 00000083H   DATA     BYTE      MEM_DPH
*SFR* 00000082H   DATA     BYTE      MEM_DPL
*SFR* 000000BBH   DATA     BYTE      MEM_NMICON
*SFR* 000000BCH   DATA     BYTE      MEM_NMISR
*SFR* 00000096H   DATA     BYTE      MEX3
*SFR* 000000E9H   DATA     BYTE      MISC_CON
*SFR* 000000F3H   DATA     BYTE      MMBPCR
*SFR* 000000F1H   DATA     BYTE      MMCR
*SFR* 000000E9H   DATA     BYTE      MMCR2
*SFR* 000000F5H   DATA     BYTE      MMDR
*SFR* 000000F4H   DATA     BYTE      MMICR
*SFR* 000000F2H   DATA     BYTE      MMSR
*SFR* 000000EBH   DATA     BYTE      MMWR1
*SFR* 000000ECH   DATA     BYTE      MMWR2
*SFR* 000000B3H   DATA     BYTE      MODPISEL
*SFR* 000000B7H   DATA     BYTE      MODPISEL1
*SFR* 000000BAH   DATA     BYTE      MODPISEL2
*SFR* 000000BDH   DATA     BYTE      MODSUSP
*SFR* 000000BBH   DATA     BYTE      NMICON
*SFR* 000000BCH   DATA     BYTE      NMISR
*SFR* 000000B6H   DATA     BYTE      OSC_CON
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 12


*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H   DATA     BIT       P
*SFR* 00000080H   DATA     BIT       P0_0
*SFR* 00000080H.1 DATA     BIT       P0_1
*SFR* 00000080H.2 DATA     BIT       P0_2
*SFR* 00000080H.3 DATA     BIT       P0_3
*SFR* 00000080H.4 DATA     BIT       P0_4
*SFR* 00000080H.5 DATA     BIT       P0_5
*SFR* 00000080H.7 DATA     BIT       P0_7
*SFR* 00000080H   DATA     BYTE      P0_ALTSEL0
*SFR* 00000086H   DATA     BYTE      P0_ALTSEL1
*SFR* 00000080H   DATA     BYTE      P0_DATA
*SFR* 00000086H   DATA     BYTE      P0_DIR
*SFR* 00000080H   DATA     BYTE      P0_OD
*SFR* 00000086H   DATA     BYTE      P0_PUDEN
*SFR* 00000080H   DATA     BYTE      P0_PUDSEL
*SFR* 00000090H   DATA     BIT       P1_0
*SFR* 00000090H.1 DATA     BIT       P1_1
*SFR* 00000090H.2 DATA     BIT       P1_2
*SFR* 00000090H.3 DATA     BIT       P1_3
*SFR* 00000090H.4 DATA     BIT       P1_4
*SFR* 00000090H.5 DATA     BIT       P1_5
*SFR* 00000090H.6 DATA     BIT       P1_6
*SFR* 00000090H.7 DATA     BIT       P1_7
*SFR* 00000090H   DATA     BYTE      P1_ALTSEL0
*SFR* 00000091H   DATA     BYTE      P1_ALTSEL1
*SFR* 00000090H   DATA     BYTE      P1_DATA
*SFR* 00000091H   DATA     BYTE      P1_DIR
*SFR* 00000090H   DATA     BYTE      P1_OD
*SFR* 00000091H   DATA     BYTE      P1_PUDEN
*SFR* 00000090H   DATA     BYTE      P1_PUDSEL
*SFR* 000000A0H   DATA     BIT       P2_0
*SFR* 000000A0H.1 DATA     BIT       P2_1
*SFR* 000000A0H.2 DATA     BIT       P2_2
*SFR* 000000A0H.3 DATA     BIT       P2_3
*SFR* 000000A0H.4 DATA     BIT       P2_4
*SFR* 000000A0H.5 DATA     BIT       P2_5
*SFR* 000000A0H.6 DATA     BIT       P2_6
*SFR* 000000A0H.7 DATA     BIT       P2_7
*SFR* 000000A0H   DATA     BYTE      P2_DATA
*SFR* 000000A1H   DATA     BYTE      P2_DIR
*SFR* 000000A1H   DATA     BYTE      P2_PUDEN
*SFR* 000000A0H   DATA     BYTE      P2_PUDSEL
*SFR* 000000B0H   DATA     BIT       P3_0
*SFR* 000000B0H.1 DATA     BIT       P3_1
*SFR* 000000B0H.2 DATA     BIT       P3_2
*SFR* 000000B0H.3 DATA     BIT       P3_3
*SFR* 000000B0H.4 DATA     BIT       P3_4
*SFR* 000000B0H.5 DATA     BIT       P3_5
*SFR* 000000B0H.6 DATA     BIT       P3_6
*SFR* 000000B0H.7 DATA     BIT       P3_7
*SFR* 000000B0H   DATA     BYTE      P3_ALTSEL0
*SFR* 000000B1H   DATA     BYTE      P3_ALTSEL1
*SFR* 000000B0H   DATA     BYTE      P3_DATA
*SFR* 000000B1H   DATA     BYTE      P3_DIR
*SFR* 000000B0H   DATA     BYTE      P3_OD
*SFR* 000000B1H   DATA     BYTE      P3_PUDEN
*SFR* 000000B0H   DATA     BYTE      P3_PUDSEL
*SFR* 000000C8H   DATA     BIT       P4_0
*SFR* 000000C8H.1 DATA     BIT       P4_1
*SFR* 000000C8H.3 DATA     BIT       P4_3
*SFR* 000000C8H   DATA     BYTE      P4_ALTSEL0
*SFR* 000000C9H   DATA     BYTE      P4_ALTSEL1
*SFR* 000000C8H   DATA     BYTE      P4_DATA
*SFR* 000000C9H   DATA     BYTE      P4_DIR
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 13


*SFR* 000000C8H   DATA     BYTE      P4_OD
*SFR* 000000C9H   DATA     BYTE      P4_PUDEN
*SFR* 000000C8H   DATA     BYTE      P4_PUDSEL
*SFR* 00000092H   DATA     BYTE      P5_ALTSEL0
*SFR* 00000093H   DATA     BYTE      P5_ALTSEL1
*SFR* 00000092H   DATA     BYTE      P5_DATA
*SFR* 00000093H   DATA     BYTE      P5_DIR
*SFR* 00000092H   DATA     BYTE      P5_OD
*SFR* 00000093H   DATA     BYTE      P5_PUDEN
*SFR* 00000092H   DATA     BYTE      P5_PUDSEL
*SFR* 000000F8H   DATA     BIT       PADC
*SFR* 000000BBH   DATA     BYTE      PASSWD
      010035A1H   CODE     ---       Password
*SFR* 000000F8H.4 DATA     BIT       PCCIP0
*SFR* 000000F8H.5 DATA     BIT       PCCIP1
*SFR* 000000F8H.6 DATA     BIT       PCCIP2
*SFR* 000000F8H.7 DATA     BIT       PCCIP3
*SFR* 00000087H   DATA     BYTE      PCON
*SFR* 000000B7H   DATA     BYTE      PLL_CON
*SFR* 000000EAH   DATA     BYTE      PLL_CON1
*SFR* 000000B4H   DATA     BYTE      PMCON0
*SFR* 000000B5H   DATA     BYTE      PMCON1
*SFR* 000000BBH   DATA     BYTE      PMCON2
*SFR* 000000B2H   DATA     BYTE      PORT_PAGE
*SFR* 000000B8H.4 DATA     BIT       PS
*SFR* 000000F8H.1 DATA     BIT       PSSC
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H   DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
*SFR* 000000F8H.2 DATA     BIT       PX2
*SFR* 000000F8H.3 DATA     BIT       PXM
*SFR* 00000098H.2 DATA     BIT       RB8
*SFR* 000000C8H.2 DATA     BIT       RB8_1
*SFR* 00000098H.4 DATA     BIT       REN
*SFR* 000000C8H.4 DATA     BIT       REN_1
      0200F04DH   XDATA    WORD      RgCanPerid
*SFR* 00000098H   DATA     BIT       RI
*SFR* 000000C8H   DATA     BIT       RI_1
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
      01003AD4H   CODE     ---       RTCProcess
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000098H   DATA     BYTE      SCON
*SFR* 000000BFH   DATA     BYTE      SCU_PAGE
      010027F0H   CODE     ---       SHINT_vInit
      010037B6H   CODE     ---       SHINT_viXINTR5Isr
*SFR* 00000098H.7 DATA     BIT       SM0
*SFR* 000000C8H.7 DATA     BIT       SM0_1
*SFR* 00000098H.6 DATA     BIT       SM1
*SFR* 000000C8H.6 DATA     BIT       SM1_1
*SFR* 00000098H.5 DATA     BIT       SM2
*SFR* 000000C8H.5 DATA     BIT       SM2_1
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000AFH   DATA     BYTE      SSC_BRH
*SFR* 000000AEH   DATA     BYTE      SSC_BRL
*SFR* 000000ABH   DATA     BYTE      SSC_CONH_O
*SFR* 000000ABH   DATA     BYTE      SSC_CONH_P
*SFR* 000000AAH   DATA     BYTE      SSC_CONL_O
*SFR* 000000AAH   DATA     BYTE      SSC_CONL_P
*SFR* 000000A9H   DATA     BYTE      SSC_PISEL
*SFR* 000000ADH   DATA     BYTE      SSC_RBL
*SFR* 000000ACH   DATA     BYTE      SSC_TBL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 14


      01002850H   CODE     ---       SSC_vGetData
      010027FBH   CODE     ---       SSC_vInit
      01003143H   CODE     ---       SwSample
*SFR* 0000008FH   DATA     BYTE      SYSCON0
*SFR* 000000C3H   DATA     BYTE      T21_RC2H
*SFR* 000000C2H   DATA     BYTE      T21_RC2L
*SFR* 000000C2H   DATA     WORD      T21_RC2LH
*SFR* 000000C0H   DATA     BYTE      T21_T2CON
*SFR* 000000C5H   DATA     BYTE      T21_T2H
*SFR* 000000C4H   DATA     BYTE      T21_T2L
*SFR* 000000C4H   DATA     WORD      T21_T2LH
*SFR* 000000C1H   DATA     BYTE      T21_T2MOD
*SFR* 000000C3H   DATA     BYTE      T2_RC2H
*SFR* 000000C2H   DATA     BYTE      T2_RC2L
*SFR* 000000C2H   DATA     WORD      T2_RC2LH
*SFR* 000000C0H   DATA     BYTE      T2_T2CON
*SFR* 000000C5H   DATA     BYTE      T2_T2H
*SFR* 000000C4H   DATA     BYTE      T2_T2L
*SFR* 000000C4H   DATA     WORD      T2_T2LH
*SFR* 000000C1H   DATA     BYTE      T2_T2MOD
      01003810H   CODE     ---       T2_vInit
*SFR* 00000098H.3 DATA     BIT       TB8
*SFR* 000000C8H.3 DATA     BIT       TB8_1
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 000000C0H.7 DATA     BIT       TF2
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 00000098H.1 DATA     BIT       TI
*SFR* 000000C8H.1 DATA     BIT       TI_1
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 000000C0H.2 DATA     BIT       TR2
*SFR* 000000CAH   DATA     BYTE      UART1_BCON
*SFR* 000000CBH   DATA     BYTE      UART1_BG
*SFR* 000000CCH   DATA     BYTE      UART1_FDCON
*SFR* 000000CEH   DATA     BYTE      UART1_FDRES
*SFR* 000000CDH   DATA     BYTE      UART1_FDSTEP
*SFR* 000000C9H   DATA     BYTE      UART1_SBUF
*SFR* 000000C8H   DATA     BYTE      UART1_SCON
      0200F03DH   XDATA    ---       UnInfCan_1
      0200F051H   XDATA    ---       UnSwOut_1
      0200F039H   XDATA    ---       UnSwSample_1
      01003822H   CODE     ---       WDT_vDisable
      010026F5H   CODE     ---       WDT_vInit
*SFR* 000000BBH   DATA     BYTE      WDTCON
*SFR* 000000BFH   DATA     BYTE      WDTH
*SFR* 000000BEH   DATA     BYTE      WDTL
*SFR* 000000BCH   DATA     BYTE      WDTREL
*SFR* 000000BDH   DATA     BYTE      WDTWINB
      00000080H   IDATA    ---       WLBuf
*SFR* 000000B3H   DATA     BYTE      XADDRH



SYMBOL TABLE OF MODULE:  .\output\CL2.0 (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      000000A2H   PUBLIC    DATA     BYTE      ?C?DPSEL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 15


      01002000H   PUBLIC    CODE     NEAR LAB  ?C_STARTUP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000F0H   SFRSYM    DATA     BYTE      B
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      00000096H   SFRSYM    DATA     BYTE      MEX3
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000EAH   SFRSYM    DATA     BYTE      PLL_CON1
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      01002113H   SYMBOL    CODE     NEAR LAB  DELAYXTAL
      01002111H   SYMBOL    CODE     NEAR LAB  DELAYXTAL0
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01002144H   SYMBOL    CODE     NEAR LAB  IDATALOOP
      00000001H   SYMBOL    NUMBER   ---       LIN_BSL
      00000001H   SYMBOL    NUMBER   ---       LIN_NAC
      00000001H   SYMBOL    NUMBER   ---       LIN_NAD
      0000000AH   SYMBOL    NUMBER   ---       NDIV
      00000002H   SYMBOL    NUMBER   ---       NDIV_XC86X
      00000018H   SYMBOL    NUMBER   ---       NDIV_XC87X_PLL_CON
      00000020H   SYMBOL    NUMBER   ---       NDIV_XC87X_PLL_CON1
      0000000AH   SYMBOL    NUMBER   ---       NDIV_XC88X
      00000000H   SYMBOL    NUMBER   ---       NR_XC87X
      00000000H   SYMBOL    NUMBER   ---       OD_XC87X
      01002117H   SYMBOL    CODE     NEAR LAB  OSCR_NOTSET
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      0000F000H   SYMBOL    NUMBER   ---       PDATASTART
      000000F0H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      01002100H   SYMBOL    CODE     NEAR LAB  STARTUP1
      01002134H   SYMBOL    CODE     NEAR LAB  WAIT_LOCK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00010000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       XC82X_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC864_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC866_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC874_CHIP_13FF
      00000000H   SYMBOL    NUMBER   ---       XC874_CHIP_16FF
      00000000H   SYMBOL    NUMBER   ---       XC878_CHIP_13FF
      00000000H   SYMBOL    NUMBER   ---       XC878_CHIP_16FF
      00000001H   SYMBOL    NUMBER   ---       XC88X_CHIP
      00000600H   SYMBOL    NUMBER   ---       XDATALEN
      0100214FH   SYMBOL    CODE     NEAR LAB  XDATALOOP
      0000F000H   SYMBOL    NUMBER   ---       XDATASTART
      00000001H   SYMBOL    NUMBER   ---       XTAL

      01002000H   BLOCK     CODE     NEAR LAB  LVL=0
      01002000H   LINE      CODE     ---       #312
      ---         BLOCKEND  ---      ---       LVL=0

      01002100H   BLOCK     CODE     NEAR LAB  LVL=0
      01002100H   LINE      CODE     ---       #367
      01002103H   LINE      CODE     ---       #368
      01002106H   LINE      CODE     ---       #369
      01002109H   LINE      CODE     ---       #370
      0100210CH   LINE      CODE     ---       #371
      0100210FH   LINE      CODE     ---       #373
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 16


      01002111H   LINE      CODE     ---       #375
      01002113H   LINE      CODE     ---       #377
      01002115H   LINE      CODE     ---       #378
      01002117H   LINE      CODE     ---       #382
      01002119H   LINE      CODE     ---       #383
      0100211CH   LINE      CODE     ---       #388
      0100211EH   LINE      CODE     ---       #389
      01002120H   LINE      CODE     ---       #390
      01002123H   LINE      CODE     ---       #394
      01002126H   LINE      CODE     ---       #395
      01002129H   LINE      CODE     ---       #396
      0100212CH   LINE      CODE     ---       #397
      0100212FH   LINE      CODE     ---       #400
      01002132H   LINE      CODE     ---       #401
      01002134H   LINE      CODE     ---       #409
      01002136H   LINE      CODE     ---       #410
      01002138H   LINE      CODE     ---       #411
      0100213BH   LINE      CODE     ---       #413
      0100213EH   LINE      CODE     ---       #414
      01002141H   LINE      CODE     ---       #419
      01002143H   LINE      CODE     ---       #420
      01002144H   LINE      CODE     ---       #421
      01002145H   LINE      CODE     ---       #422
      01002147H   LINE      CODE     ---       #439
      0100214AH   LINE      CODE     ---       #440
      0100214CH   LINE      CODE     ---       #444
      0100214EH   LINE      CODE     ---       #446
      0100214FH   LINE      CODE     ---       #447
      01002150H   LINE      CODE     ---       #448
      01002151H   LINE      CODE     ---       #449
      01002153H   LINE      CODE     ---       #450
      01002155H   LINE      CODE     ---       #486
      01002158H   LINE      CODE     ---       #488
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      010021DBH   PUBLIC    CODE     ---       main
      0100251AH   PUBLIC    CODE     ---       MAIN_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 17


      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 18


      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 19


      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 20


      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 21


      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 22


      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 23


      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      0100251AH   BLOCK     CODE     ---       LVL=0
      0100251AH   LINE      ---      ---       #122
      0100251AH   LINE      ---      ---       #123
      0100251AH   LINE      ---      ---       #138
      0100251DH   LINE      ---      ---       #140
      01002520H   LINE      ---      ---       #142
      01002523H   LINE      ---      ---       #151
      01002525H   LINE      ---      ---       #154
      01002528H   LINE      ---      ---       #157
      0100252AH   LINE      ---      ---       #160
      0100252CH   LINE      ---      ---       #163
      0100252EH   LINE      ---      ---       #166
      01002530H   LINE      ---      ---       #170
      01002533H   LINE      ---      ---       #171
      01002535H   LINE      ---      ---       #172
      01002537H   LINE      ---      ---       #173
      01002539H   LINE      ---      ---       #185
      0100253BH   LINE      ---      ---       #187
      ---         BLOCKEND  ---      ---       LVL=0

      010021DBH   BLOCK     CODE     ---       LVL=0
      010021DBH   LINE      ---      ---       #211
      010021DBH   LINE      ---      ---       #212
      010021DBH   LINE      ---      ---       #220
      010021DDH   LINE      ---      ---       #223
      010021E0H   LINE      ---      ---       #224
      010021E3H   LINE      ---      ---       #225
      010021E6H   LINE      ---      ---       #230
      010021E6H   LINE      ---      ---       #231
      010021E6H   LINE      ---      ---       #238
      010021E9H   LINE      ---      ---       #239
      010021ECH   LINE      ---      ---       #240
      010021EFH   LINE      ---      ---       #241
      010021FCH   LINE      ---      ---       #242
      010021FEH   LINE      ---      ---       #250
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SSC
      01002850H   PUBLIC    CODE     ---       SSC_vGetData
      0100281FH   PUBLIC    CODE     ---       _SSC_vSendData
      010027FBH   PUBLIC    CODE     ---       SSC_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 24


      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 25


      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 26


      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 27


      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 28


      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 29


      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 30


      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      010027FBH   BLOCK     CODE     ---       LVL=0
      010027FBH   LINE      ---      ---       #122
      010027FBH   LINE      ---      ---       #123
      010027FBH   LINE      ---      ---       #129
      010027FEH   LINE      ---      ---       #139
      01002801H   LINE      ---      ---       #140
      01002804H   LINE      ---      ---       #141
      01002807H   LINE      ---      ---       #143
      0100280AH   LINE      ---      ---       #144
      0100280DH   LINE      ---      ---       #151
      01002810H   LINE      ---      ---       #160
      01002812H   LINE      ---      ---       #162
      01002815H   LINE      ---      ---       #177
      01002818H   LINE      ---      ---       #179
      0100281BH   LINE      ---      ---       #181
      0100281EH   LINE      ---      ---       #189
      ---         BLOCKEND  ---      ---       LVL=0

      0100281FH   BLOCK     CODE     ---       LVL=0
      0100281FH   LINE      ---      ---       #223
      0100281FH   LINE      ---      ---       #224
      0100281FH   LINE      ---      ---       #225
      01002821H   LINE      ---      ---       #227
      00000007H   SYMBOL    DATA     BYTE      Data
      ---         BLOCKEND  ---      ---       LVL=0

      01002850H   BLOCK     CODE     ---       LVL=0
      01002850H   LINE      ---      ---       #253
      01002850H   LINE      ---      ---       #254
      01002850H   LINE      ---      ---       #255
      01002852H   LINE      ---      ---       #257
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       IO
      010027F4H   PUBLIC    CODE     ---       IO_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 31


      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 32


      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 33


      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 34


      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 35


      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 36


      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 37


      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      010027F4H   BLOCK     CODE     ---       LVL=0
      010027F4H   LINE      ---      ---       #122
      010027F4H   LINE      ---      ---       #123
      010027F4H   LINE      ---      ---       #175
      010027F7H   LINE      ---      ---       #176
      010027FAH   LINE      ---      ---       #201
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SHARED_INT
      010037B6H   PUBLIC    CODE     ---       SHINT_viXINTR5Isr
      010027F0H   PUBLIC    CODE     ---       SHINT_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 38


      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 39


      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 40


      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 41


      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 42


      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 43


      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 44


      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      010027F0H   BLOCK     CODE     ---       LVL=0
      010027F0H   LINE      ---      ---       #121
      010027F0H   LINE      ---      ---       #122
      010027F0H   LINE      ---      ---       #133
      010027F3H   LINE      ---      ---       #137
      010027F3H   LINE      ---      ---       #145
      ---         BLOCKEND  ---      ---       LVL=0

      010037B6H   BLOCK     CODE     ---       LVL=0
      010037DDH   LINE      ---      ---       #189
      010037DDH   LINE      ---      ---       #195
      010037E0H   LINE      ---      ---       #199
      010037E3H   LINE      ---      ---       #200
      010037E3H   LINE      ---      ---       #203
      010037E5H   LINE      ---      ---       #206
      010037E8H   LINE      ---      ---       #209
      010037E8H   LINE      ---      ---       #215
      010037EBH   LINE      ---      ---       #216
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       CAN
      01003082H   PUBLIC    CODE     ---       _CAN_vReleaseObj
      01003684H   PUBLIC    CODE     ---       _CAN_vLoadData
      010034FFH   PUBLIC    CODE     ---       _CAN_vTransmit
      0100377BH   PUBLIC    CODE     ---       _CAN_ubNewData
      010030AFH   PUBLIC    CODE     ---       _CAN_ubRequestMsgObj
      01002E84H   PUBLIC    CODE     ---       _CAN_vGetMsgObj
      010026C3H   PUBLIC    CODE     ---       _CAN_vSetListCommand
      01002677H   PUBLIC    CODE     ---       _CAN_vWriteAMData
      0100253CH   PUBLIC    CODE     ---       CAN_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 45


      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 46


      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 47


      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 48


      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 49


      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 50


      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 51


      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      0100253CH   BLOCK     CODE     ---       LVL=0
      0100253CH   LINE      ---      ---       #124
      0100253CH   LINE      ---      ---       #125
      0100253CH   LINE      ---      ---       #140
      01002542H   LINE      ---      ---       #141
      01002545H   LINE      ---      ---       #142
      0100254AH   LINE      ---      ---       #144
      0100254DH   LINE      ---      ---       #145
      0100254FH   LINE      ---      ---       #156
      01002555H   LINE      ---      ---       #157
      01002558H   LINE      ---      ---       #158
      01002560H   LINE      ---      ---       #163
      01002563H   LINE      ---      ---       #168
      0100256AH   LINE      ---      ---       #184
      01002571H   LINE      ---      ---       #204
      01002574H   LINE      ---      ---       #205
      01002577H   LINE      ---      ---       #206
      0100257FH   LINE      ---      ---       #217
      01002582H   LINE      ---      ---       #218
      0100258AH   LINE      ---      ---       #231
      0100258DH   LINE      ---      ---       #232
      0100258FH   LINE      ---      ---       #233
      01002591H   LINE      ---      ---       #234
      01002599H   LINE      ---      ---       #247
      0100259FH   LINE      ---      ---       #248
      010025A2H   LINE      ---      ---       #249
      010025AAH   LINE      ---      ---       #259
      010025ADH   LINE      ---      ---       #260
      010025B0H   LINE      ---      ---       #261
      010025B3H   LINE      ---      ---       #263
      010025B6H   LINE      ---      ---       #264
      010025B9H   LINE      ---      ---       #271
      010025BEH   LINE      ---      ---       #274
      010025D9H   LINE      ---      ---       #311
      010025DFH   LINE      ---      ---       #313
      010025E7H   LINE      ---      ---       #325
      010025F1H   LINE      ---      ---       #350
      010025F5H   LINE      ---      ---       #359
      010025FBH   LINE      ---      ---       #377
      010025FDH   LINE      ---      ---       #388
      01002603H   LINE      ---      ---       #390
      01002607H   LINE      ---      ---       #402
      0100260BH   LINE      ---      ---       #427
      0100260FH   LINE      ---      ---       #436
      01002615H   LINE      ---      ---       #454
      01002617H   LINE      ---      ---       #465
      0100261DH   LINE      ---      ---       #467
      01002625H   LINE      ---      ---       #479
      01002629H   LINE      ---      ---       #504
      0100262DH   LINE      ---      ---       #513
      01002633H   LINE      ---      ---       #531
      01002635H   LINE      ---      ---       #546
      0100263BH   LINE      ---      ---       #548
      0100263FH   LINE      ---      ---       #560
      01002649H   LINE      ---      ---       #585
      0100264DH   LINE      ---      ---       #594
      01002653H   LINE      ---      ---       #612
      01002655H   LINE      ---      ---       #722
      0100265BH   LINE      ---      ---       #723
      01002663H   LINE      ---      ---       #724
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 52


      01002666H   LINE      ---      ---       #725
      0100266EH   LINE      ---      ---       #734
      ---         BLOCKEND  ---      ---       LVL=0

      0100266FH   BLOCK     CODE     ---       LVL=0
      01002677H   LINE      ---      ---       #773
      01002677H   LINE      ---      ---       #774
      00000004H   SYMBOL    DATA     DWORD     ulValue
      01002677H   BLOCK     CODE     NEAR LAB  LVL=1
      01002677H   LINE      ---      ---       #776
      0100267CH   LINE      ---      ---       #778
      01002682H   LINE      ---      ---       #779
      01002688H   LINE      ---      ---       #780
      0100268EH   LINE      ---      ---       #781
      01002694H   LINE      ---      ---       #782
      0100269CH   LINE      ---      ---       #783
      0200F003H   SYMBOL    XDATA    ---       ulData
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010026C3H   BLOCK     CODE     ---       LVL=0
      010026C3H   LINE      ---      ---       #808
      010026C3H   LINE      ---      ---       #809
      010026C3H   LINE      ---      ---       #810
      010026C5H   LINE      ---      ---       #811
      010026C8H   LINE      ---      ---       #812
      010026CDH   LINE      ---      ---       #814
      010026D0H   LINE      ---      ---       #815
      010026D2H   LINE      ---      ---       #816
      00000004H   SYMBOL    DATA     DWORD     ulVal
      ---         BLOCKEND  ---      ---       LVL=0

      01002E84H   BLOCK     CODE     ---       LVL=0
      01002E84H   LINE      ---      ---       #846
      01002E8CH   LINE      ---      ---       #847
      0200F01CH   SYMBOL    XDATA    BYTE      ubObjNr
      0200F01DH   SYMBOL    XDATA    ---       pstObj
      01002E8CH   BLOCK     CODE     NEAR LAB  LVL=1
      01002E8CH   LINE      ---      ---       #850
      01002E9DH   LINE      ---      ---       #854
      01002EA5H   LINE      ---      ---       #855
      01002EADH   LINE      ---      ---       #857
      01002EB8H   LINE      ---      ---       #860
      01002EC0H   LINE      ---      ---       #862
      01002EC8H   LINE      ---      ---       #863
      01002ED6H   LINE      ---      ---       #864
      01002EDEH   LINE      ---      ---       #865
      01002EE6H   LINE      ---      ---       #866
      01002EEEH   LINE      ---      ---       #869
      01002EF1H   LINE      ---      ---       #873
      01002EF9H   LINE      ---      ---       #874
      01002F07H   LINE      ---      ---       #875
      01002F0FH   LINE      ---      ---       #876
      01002F17H   LINE      ---      ---       #877
      01002F1FH   LINE      ---      ---       #880
      01002F26H   LINE      ---      ---       #881
      01002F2EH   LINE      ---      ---       #883
      01002F33H   LINE      ---      ---       #884
      01002F33H   LINE      ---      ---       #885
      01002F40H   LINE      ---      ---       #886
      01002F40H   LINE      ---      ---       #891
      01002F43H   LINE      ---      ---       #892
      01002F4BH   LINE      ---      ---       #894
      01002F50H   LINE      ---      ---       #895
      01002F50H   LINE      ---      ---       #898
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 53


      01002F5EH   LINE      ---      ---       #899
      01002F66H   LINE      ---      ---       #900
      01002F72H   LINE      ---      ---       #904
      01002F7CH   LINE      ---      ---       #915
      01002F8AH   LINE      ---      ---       #916
      01002F92H   LINE      ---      ---       #917
      01002F9EH   LINE      ---      ---       #920
      01002FA7H   LINE      ---      ---       #921
      01002FA9H   LINE      ---      ---       #923
      01002FA9H   LINE      ---      ---       #925
      01002FBCH   LINE      ---      ---       #927
      01002FC3H   LINE      ---      ---       #930
      01002FC3H   LINE      ---      ---       #931
      01002FD8H   LINE      ---      ---       #936
      01002FE2H   LINE      ---      ---       #948
      01002FF5H   LINE      ---      ---       #950
      01002FFCH   LINE      ---      ---       #951
      01003003H   LINE      ---      ---       #953
      01003003H   LINE      ---      ---       #954
      01003017H   LINE      ---      ---       #957
      01003017H   LINE      ---      ---       #961
      0100301DH   LINE      ---      ---       #962
      01003025H   LINE      ---      ---       #964
      01003034H   LINE      ---      ---       #965
      0100303CH   LINE      ---      ---       #967
      01003048H   LINE      ---      ---       #968
      00000007H   SYMBOL    DATA     BYTE      ubTemp
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010030AFH   BLOCK     CODE     ---       LVL=0
      010030AFH   LINE      ---      ---       #1003
      010030AFH   LINE      ---      ---       #1004
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      010030AFH   BLOCK     CODE     NEAR LAB  LVL=1
      010030AFH   LINE      ---      ---       #1005
      010030B1H   LINE      ---      ---       #1007
      010030C2H   LINE      ---      ---       #1009
      010030C4H   LINE      ---      ---       #1010
      010030CCH   LINE      ---      ---       #1012
      010030D1H   LINE      ---      ---       #1013
      010030D1H   LINE      ---      ---       #1014
      010030DEH   LINE      ---      ---       #1015
      010030E0H   LINE      ---      ---       #1016
      010030E0H   LINE      ---      ---       #1017
      010030ECH   LINE      ---      ---       #1019
      010030EEH   LINE      ---      ---       #1020
      00000005H   SYMBOL    DATA     BYTE      ubReturn
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      0100377BH   BLOCK     CODE     ---       LVL=0
      0100377BH   LINE      ---      ---       #1046
      0100377BH   LINE      ---      ---       #1047
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      0100377BH   BLOCK     CODE     NEAR LAB  LVL=1
      0100377BH   LINE      ---      ---       #1048
      0100377DH   LINE      ---      ---       #1050
      0100378EH   LINE      ---      ---       #1052
      01003790H   LINE      ---      ---       #1053
      01003798H   LINE      ---      ---       #1054
      0100379DH   LINE      ---      ---       #1055
      0100379DH   LINE      ---      ---       #1056
      0100379FH   LINE      ---      ---       #1057
      0100379FH   LINE      ---      ---       #1058
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 54


      010037ABH   LINE      ---      ---       #1060
      010037ADH   LINE      ---      ---       #1061
      00000005H   SYMBOL    DATA     BYTE      ubReturn
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010034FFH   BLOCK     CODE     ---       LVL=0
      010034FFH   LINE      ---      ---       #1090
      010034FFH   LINE      ---      ---       #1091
      010034FFH   LINE      ---      ---       #1092
      01003510H   LINE      ---      ---       #1094
      01003512H   LINE      ---      ---       #1095
      01003525H   LINE      ---      ---       #1097
      01003531H   LINE      ---      ---       #1099
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      ---         BLOCKEND  ---      ---       LVL=0

      01003684H   BLOCK     CODE     ---       LVL=0
      01003684H   LINE      ---      ---       #1130
      01003684H   LINE      ---      ---       #1131
      01003684H   LINE      ---      ---       #1132
      01003695H   LINE      ---      ---       #1134
      01003697H   LINE      ---      ---       #1135
      010036A9H   LINE      ---      ---       #1138
      010036ACH   LINE      ---      ---       #1141
      010036AFH   LINE      ---      ---       #1144
      010036B2H   LINE      ---      ---       #1146
      010036B9H   LINE      ---      ---       #1149
      010036BCH   LINE      ---      ---       #1151
      010036C3H   LINE      ---      ---       #1153
      010036CAH   LINE      ---      ---       #1156
      010036CDH   LINE      ---      ---       #1159
      010036D0H   LINE      ---      ---       #1161
      010036E4H   LINE      ---      ---       #1163
      010036F0H   LINE      ---      ---       #1165
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      00000001H   SYMBOL    DATA     ---       ulpubData
      ---         BLOCKEND  ---      ---       LVL=0

      01003082H   BLOCK     CODE     ---       LVL=0
      01003082H   LINE      ---      ---       #1194
      01003082H   LINE      ---      ---       #1195
      01003082H   LINE      ---      ---       #1197
      01003093H   LINE      ---      ---       #1198
      01003095H   LINE      ---      ---       #1199
      010030A2H   LINE      ---      ---       #1201
      010030AEH   LINE      ---      ---       #1202
      00000007H   SYMBOL    DATA     BYTE      ubObjNr
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       T2
      01003810H   PUBLIC    CODE     ---       T2_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 55


      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 56


      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 57


      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 58


      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 59


      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 60


      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 61


      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      01003810H   BLOCK     CODE     ---       LVL=0
      01003810H   LINE      ---      ---       #131
      01003810H   LINE      ---      ---       #132
      01003810H   LINE      ---      ---       #151
      01003816H   LINE      ---      ---       #154
      0100381CH   LINE      ---      ---       #156
      0100381FH   LINE      ---      ---       #164
      01003821H   LINE      ---      ---       #166
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       WDT
      01003822H   PUBLIC    CODE     ---       WDT_vDisable
      010026F5H   PUBLIC    CODE     ---       WDT_vInit
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 62


      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 63


      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 64


      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 65


      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 66


      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 67


      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 68


      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      010026F5H   BLOCK     CODE     ---       LVL=0
      010026F5H   LINE      ---      ---       #131
      010026F5H   LINE      ---      ---       #132
      010026F5H   LINE      ---      ---       #145
      010026F7H   LINE      ---      ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      01003822H   BLOCK     CODE     ---       LVL=0
      01003822H   LINE      ---      ---       #184
      01003822H   LINE      ---      ---       #185
      01003822H   LINE      ---      ---       #186
      01003825H   LINE      ---      ---       #188
      01003828H   LINE      ---      ---       #189
      0100382BH   LINE      ---      ---       #191
      0100382EH   LINE      ---      ---       #192
      01003831H   LINE      ---      ---       #193
      01003834H   LINE      ---      ---       #194
      01003837H   LINE      ---      ---       #196
      0100383AH   LINE      ---      ---       #197
      0100383CH   LINE      ---      ---       #198
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       SWDRIVER
      0200F01EH   PUBLIC    XDATA    BYTE      ?_Mc33993CmdSend?BYTE
      01002853H   PUBLIC    CODE     ---       _Mc33993CmdSend
      01002822H   PUBLIC    CODE     ---       _Mc33993DataSendRcv
      00000020H.5 PUBLIC    BIT      BIT       ?_ChangeCsMc33993?BIT
      01002E73H   PUBLIC    CODE     ---       _ChangeCsMc33993
      00000020H.4 PUBLIC    BIT      BIT       ?_Mc33993CmdSendOneBit?BIT
      0100383DH   PUBLIC    CODE     ---       _Mc33993CmdSendOneBit
      0100295FH   PUBLIC    CODE     ---       InitSW
      010029A7H   PUBLIC    CODE     ---       InitMc33993Sec
      01002B0FH   PUBLIC    CODE     ---       _LedLight
      01002A46H   PUBLIC    CODE     ---       _SwStaSample
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 69


      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 70


      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 71


      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 72


      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 73


      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 74


      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 75


      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH
      0200F055H   SYMBOL    XDATA    ---       PntSta

      01002A46H   BLOCK     CODE     ---       LVL=0
      01002A46H   LINE      ---      ---       #240
      01002A4BH   LINE      ---      ---       #241
      0200F008H   SYMBOL    XDATA    ---       ptmp
      01002A4BH   BLOCK     CODE     NEAR LAB  LVL=1
      01002A4BH   LINE      ---      ---       #245
      01002A4DH   LINE      ---      ---       #247
      01002A54H   LINE      ---      ---       #248
      01002A62H   LINE      ---      ---       #249
      01002A7EH   LINE      ---      ---       #254
      01002AA7H   LINE      ---      ---       #255
      01002AC1H   LINE      ---      ---       #257
      01002ADBH   LINE      ---      ---       #258
      01002AFAH   LINE      ---      ---       #259
      0200F00BH   SYMBOL    XDATA    ---       pStaSwIn
      0200F00EH   SYMBOL    XDATA    WORD      i
      00000006H   SYMBOL    DATA     WORD      k
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01002B0FH   BLOCK     CODE     ---       LVL=0
      01002B0FH   LINE      ---      ---       #277
      01002B14H   LINE      ---      ---       #278
      0200F014H   SYMBOL    XDATA    ---       tmpUn
      01002B14H   BLOCK     CODE     NEAR LAB  LVL=1
      01002B14H   LINE      ---      ---       #283
      01002B17H   LINE      ---      ---       #284
      01002B17H   LINE      ---      ---       #285
      01002B28H   LINE      ---      ---       #286
      01002B28H   LINE      ---      ---       #287
      01002B79H   LINE      ---      ---       #288
      01002B79H   LINE      ---      ---       #289
      01002B93H   LINE      ---      ---       #290
      01002BAFH   LINE      ---      ---       #291
      01002BC8H   LINE      ---      ---       #292
      01002BE1H   LINE      ---      ---       #293
      01002BFEH   LINE      ---      ---       #294
      01002C17H   LINE      ---      ---       #295
      01002C36H   LINE      ---      ---       #296
      01002C58H   LINE      ---      ---       #297
      01002C6EH   LINE      ---      ---       #298
      01002C87H   LINE      ---      ---       #299
      01002CA2H   LINE      ---      ---       #300
      01002CBEH   LINE      ---      ---       #301
      01002CD8H   LINE      ---      ---       #302
      01002CF3H   LINE      ---      ---       #303
      01002D0FH   LINE      ---      ---       #304
      01002D2CH   LINE      ---      ---       #305
      01002D48H   LINE      ---      ---       #306
      01002D66H   LINE      ---      ---       #307
      01002D81H   LINE      ---      ---       #308
      01002D9CH   LINE      ---      ---       #309
      01002DBBH   LINE      ---      ---       #310
      01002DD7H   LINE      ---      ---       #311
      01002DD7H   LINE      ---      ---       #312
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 76


      01002DD7H   LINE      ---      ---       #313
      01002DDFH   LINE      ---      ---       #314
      01002DEDH   LINE      ---      ---       #315
      0200F017H   SYMBOL    XDATA    BYTE      i
      0200F018H   SYMBOL    XDATA    BYTE      j
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010029A7H   BLOCK     CODE     ---       LVL=0
      010029A7H   LINE      ---      ---       #335
      010029A7H   LINE      ---      ---       #336
      010029A7H   BLOCK     CODE     NEAR LAB  LVL=1
      010029A7H   LINE      ---      ---       #338
      010029ACH   LINE      ---      ---       #339
      010029ACH   LINE      ---      ---       #340
      01002A16H   LINE      ---      ---       #350
      01002A22H   LINE      ---      ---       #351
      0200F010H   SYMBOL    XDATA    BYTE      i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      0100295FH   BLOCK     CODE     ---       LVL=0
      0100295FH   LINE      ---      ---       #369
      0100295FH   LINE      ---      ---       #370
      0100295FH   BLOCK     CODE     NEAR LAB  LVL=1
      0100295FH   LINE      ---      ---       #372
      0100296EH   LINE      ---      ---       #373
      0100296EH   LINE      ---      ---       #374
      0100299EH   LINE      ---      ---       #385
      010029A6H   LINE      ---      ---       #386
      0200F000H   SYMBOL    XDATA    BYTE      i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      0100383DH   BLOCK     CODE     ---       LVL=0
      0100383DH   LINE      ---      ---       #405
      0100384AH   LINE      ---      ---       #406
      0200F019H   SYMBOL    XDATA    BYTE      NumMc
      0200F01AH   SYMBOL    XDATA    BYTE      CmdBit
      0200F01BH   SYMBOL    XDATA    BYTE      Sp0ToSg13
      00000020H.4 SYMBOL    BIT      BIT       Status
      0100384AH   BLOCK     CODE     NEAR LAB  LVL=1
      0100384AH   LINE      ---      ---       #409
      01003852H   LINE      ---      ---       #410
      01003862H   LINE      ---      ---       #411
      01003862H   LINE      ---      ---       #413
      01003862H   LINE      ---      ---       #414
      0100386DH   LINE      ---      ---       #415
      01003887H   LINE      ---      ---       #416
      0100389DH   LINE      ---      ---       #417
      010038B7H   LINE      ---      ---       #418
      010038C1H   LINE      ---      ---       #419
      010038DBH   LINE      ---      ---       #420
      010038F1H   LINE      ---      ---       #421
      01003902H   LINE      ---      ---       #422
      01003915H   LINE      ---      ---       #424
      01003919H   LINE      ---      ---       #426
      0100391BH   LINE      ---      ---       #428
      0100391BH   LINE      ---      ---       #429
      0100392DH   LINE      ---      ---       #430
      01003942H   LINE      ---      ---       #431
      01003954H   LINE      ---      ---       #432
      0100396AH   LINE      ---      ---       #433
      0100397DH   LINE      ---      ---       #434
      01003990H   LINE      ---      ---       #435
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 77


      010039ABH   LINE      ---      ---       #436
      010039C7H   LINE      ---      ---       #437
      010039DFH   LINE      ---      ---       #438
      010039FBH   LINE      ---      ---       #439
      01003A07H   LINE      ---      ---       #440
      01003A23H   LINE      ---      ---       #441
      01003A3BH   LINE      ---      ---       #442
      01003A4BH   LINE      ---      ---       #443
      01003A5EH   LINE      ---      ---       #445
      01003A73H   LINE      ---      ---       #447
      01003A73H   LINE      ---      ---       #448
      01003A73H   LINE      ---      ---       #449
      01003A73H   LINE      ---      ---       #450
      01003A75H   LINE      ---      ---       #451
      0200F01CH   SYMBOL    XDATA    ---       RcvSoMc33993
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01002E73H   BLOCK     CODE     ---       LVL=0
      01002E73H   LINE      ---      ---       #455
      01002E73H   LINE      ---      ---       #456
      01002E73H   LINE      ---      ---       #457
      01002E7AH   LINE      ---      ---       #458
      01002E7AH   LINE      ---      ---       #459
      01002E7FH   LINE      ---      ---       #460
      01002E83H   LINE      ---      ---       #461
      01002E83H   LINE      ---      ---       #462
      01002E83H   LINE      ---      ---       #463
      00000007H   SYMBOL    DATA     BYTE      NumMc
      00000020H.5 SYMBOL    BIT      BIT       HorL
      ---         BLOCKEND  ---      ---       LVL=0

      01002822H   BLOCK     CODE     ---       LVL=0
      01002822H   LINE      ---      ---       #481
      01002822H   LINE      ---      ---       #482
      00000007H   SYMBOL    DATA     BYTE      Data
      01002822H   BLOCK     CODE     NEAR LAB  LVL=1
      01002822H   LINE      ---      ---       #484
      01002824H   LINE      ---      ---       #485
      01002837H   LINE      ---      ---       #486
      0100283AH   LINE      ---      ---       #487
      0100284DH   LINE      ---      ---       #488
      00000006H   SYMBOL    DATA     WORD      i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01002853H   BLOCK     CODE     ---       LVL=0
      01002853H   LINE      ---      ---       #508
      0100285DH   LINE      ---      ---       #509
      0200F01EH   SYMBOL    XDATA    BYTE      NumMc
      00000005H   SYMBOL    DATA     BYTE      CmdBit
      0200F020H   SYMBOL    XDATA    BYTE      SetBitH
      0200F021H   SYMBOL    XDATA    BYTE      SetBitL
      0100285DH   BLOCK     CODE     NEAR LAB  LVL=1
      0100285DH   LINE      ---      ---       #514
      01002861H   LINE      ---      ---       #515
      01002869H   LINE      ---      ---       #516
      0100289EH   LINE      ---      ---       #517
      0100289EH   LINE      ---      ---       #518
      010028A5H   LINE      ---      ---       #519
      010028ACH   LINE      ---      ---       #520
      010028B8H   LINE      ---      ---       #521
      010028BFH   LINE      ---      ---       #522
      010028CBH   LINE      ---      ---       #523
      010028D2H   LINE      ---      ---       #524
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 78


      010028D9H   LINE      ---      ---       #525
      010028E5H   LINE      ---      ---       #526
      010028F0H   LINE      ---      ---       #527
      01002901H   LINE      ---      ---       #528
      01002901H   LINE      ---      ---       #529
      01002901H   LINE      ---      ---       #532
      0100290DH   LINE      ---      ---       #533
      01002916H   LINE      ---      ---       #535
      01002917H   LINE      ---      ---       #536
      01002918H   LINE      ---      ---       #537
      01002919H   LINE      ---      ---       #538
      0100291AH   LINE      ---      ---       #542
      01002923H   LINE      ---      ---       #544
      0100292FH   LINE      ---      ---       #546
      0100293BH   LINE      ---      ---       #548
      01002944H   LINE      ---      ---       #550
      01002945H   LINE      ---      ---       #551
      01002946H   LINE      ---      ---       #552
      01002947H   LINE      ---      ---       #553
      01002948H   LINE      ---      ---       #555
      0100294EH   LINE      ---      ---       #556
      0100294EH   LINE      ---      ---       #557
      0200F022H   SYMBOL    XDATA    ---       RcvSoMc
      00000001H   SYMBOL    DATA     ---       Pnt
      0200F026H   SYMBOL    XDATA    ---       RcvSoMc33993W
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      ---         MODULE    ---      ---       USER
      0200F051H   PUBLIC    XDATA    ---       UnSwOut_1
      00000020H.3 PUBLIC    BIT      BIT       FlgOneTime
      0200F04FH   PUBLIC    XDATA    WORD      CntCan_1
      0200F04DH   PUBLIC    XDATA    WORD      RgCanPerid
      00000020H.2 PUBLIC    BIT      BIT       FlgCan_1
      0200F03DH   PUBLIC    XDATA    ---       UnInfCan_1
      0200F039H   PUBLIC    XDATA    ---       UnSwSample_1
      01003143H   PUBLIC    CODE     ---       SwSample
      01003709H   PUBLIC    CODE     ---       CanRXTX
      01003378H   PUBLIC    CODE     ---       CanTX
      01003B83H   PUBLIC    CODE     ---       LedDr
      010034EDH   PUBLIC    CODE     ---       CanTransmit
      010035A1H   PUBLIC    CODE     ---       Password
      01003B07H   PUBLIC    CODE     ---       CanErrorProcess
      01003AD4H   PUBLIC    CODE     ---       RTCProcess
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 79


      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 80


      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 81


      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 82


      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 83


      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 84


      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 85


      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      01003AD4H   BLOCK     CODE     ---       LVL=0
      01003AD4H   LINE      ---      ---       #18
      01003AD4H   LINE      ---      ---       #19
      01003AD4H   LINE      ---      ---       #22
      01003B06H   LINE      ---      ---       #25
      ---         BLOCKEND  ---      ---       LVL=0

      01003B07H   BLOCK     CODE     ---       LVL=0
      01003B07H   LINE      ---      ---       #51
      01003B07H   LINE      ---      ---       #52
      01003B07H   BLOCK     CODE     NEAR LAB  LVL=1
      01003B07H   LINE      ---      ---       #55
      01003B0DH   LINE      ---      ---       #57
      01003B0FH   LINE      ---      ---       #59
      01003B20H   LINE      ---      ---       #60
      01003B23H   LINE      ---      ---       #62
      01003B29H   LINE      ---      ---       #63
      01003B31H   LINE      ---      ---       #64
      01003B37H   LINE      ---      ---       #65
      01003B3BH   LINE      ---      ---       #66
      01003B42H   LINE      ---      ---       #67
      01003B42H   LINE      ---      ---       #68
      01003B45H   LINE      ---      ---       #69
      01003B45H   LINE      ---      ---       #70
      01003B4EH   LINE      ---      ---       #71
      01003B4EH   LINE      ---      ---       #72
      01003B54H   LINE      ---      ---       #73
      01003B54H   LINE      ---      ---       #75
      01003B5AH   LINE      ---      ---       #76
      01003B62H   LINE      ---      ---       #77
      01003B69H   LINE      ---      ---       #78
      01003B71H   LINE      ---      ---       #80
      01003B74H   LINE      ---      ---       #81
      01003B80H   LINE      ---      ---       #83
      01003B82H   LINE      ---      ---       #84
      0200F000H   SYMBOL    XDATA    BYTE      ubNSRL
      0200F001H   SYMBOL    XDATA    BYTE      ubNSRH
      0200F002H   SYMBOL    XDATA    BYTE      ubResetLEC
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010035A1H   BLOCK     CODE     ---       LVL=0
      010035A1H   LINE      ---      ---       #88
      010035A1H   LINE      ---      ---       #89
      010035A1H   BLOCK     CODE     NEAR LAB  LVL=1
      010035A1H   LINE      ---      ---       #91
      010035AFH   LINE      ---      ---       #92
      010035BFH   LINE      ---      ---       #94
      010035C1H   LINE      ---      ---       #95
      010035C1H   LINE      ---      ---       #96
      010035FAH   LINE      ---      ---       #102
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 86


      01003600H   LINE      ---      ---       #103
      00000001H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      010034EDH   BLOCK     CODE     ---       LVL=0
      010034EDH   LINE      ---      ---       #106
      010034EDH   LINE      ---      ---       #107
      010034EDH   LINE      ---      ---       #110
      010034F1H   LINE      ---      ---       #111
      010034F3H   LINE      ---      ---       #113
      010034FDH   LINE      ---      ---       #114
      ---         BLOCKEND  ---      ---       LVL=0

      01003B83H   BLOCK     CODE     ---       LVL=0
      01003B83H   LINE      ---      ---       #123
      01003B83H   LINE      ---      ---       #124
      01003B83H   LINE      ---      ---       #125
      01003B97H   LINE      ---      ---       #126
      01003BABH   LINE      ---      ---       #127
      01003BBFH   LINE      ---      ---       #128
      01003BD3H   LINE      ---      ---       #129
      01003BE7H   LINE      ---      ---       #130
      01003BFBH   LINE      ---      ---       #131
      01003C0FH   LINE      ---      ---       #132
      01003C23H   LINE      ---      ---       #133
      01003C32H   LINE      ---      ---       #134
      01003C41H   LINE      ---      ---       #135
      01003C55H   LINE      ---      ---       #136
      01003C69H   LINE      ---      ---       #137
      01003C7DH   LINE      ---      ---       #138
      01003C91H   LINE      ---      ---       #139
      01003CA0H   LINE      ---      ---       #140
      01003CB4H   LINE      ---      ---       #141
      01003CC8H   LINE      ---      ---       #142
      01003CDCH   LINE      ---      ---       #143
      01003CF0H   LINE      ---      ---       #144
      01003D04H   LINE      ---      ---       #146
      ---         BLOCKEND  ---      ---       LVL=0

      01003378H   BLOCK     CODE     ---       LVL=0
      01003378H   LINE      ---      ---       #152
      01003378H   LINE      ---      ---       #153
      01003378H   LINE      ---      ---       #154
      01003384H   LINE      ---      ---       #155
      01003390H   LINE      ---      ---       #156
      010033A3H   LINE      ---      ---       #157
      010033B5H   LINE      ---      ---       #158
      010033C8H   LINE      ---      ---       #159
      010033DBH   LINE      ---      ---       #160
      010033EEH   LINE      ---      ---       #161
      01003401H   LINE      ---      ---       #162
      0100341CH   LINE      ---      ---       #163
      01003437H   LINE      ---      ---       #164
      0100344DH   LINE      ---      ---       #165
      01003463H   LINE      ---      ---       #166
      01003474H   LINE      ---      ---       #167
      01003488H   LINE      ---      ---       #168
      0100349EH   LINE      ---      ---       #169
      010034B9H   LINE      ---      ---       #170
      010034C5H   LINE      ---      ---       #171
      010034D1H   LINE      ---      ---       #172
      010034E1H   LINE      ---      ---       #173
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 87


      01003709H   BLOCK     CODE     ---       LVL=0
      01003709H   LINE      ---      ---       #179
      01003709H   LINE      ---      ---       #180
      01003709H   BLOCK     CODE     NEAR LAB  LVL=1
      01003709H   LINE      ---      ---       #184
      01003710H   LINE      ---      ---       #185
      01003710H   LINE      ---      ---       #186
      0100371EH   LINE      ---      ---       #189
      01003726H   LINE      ---      ---       #190
      0100372EH   LINE      ---      ---       #191
      01003736H   LINE      ---      ---       #192
      0100373EH   LINE      ---      ---       #193
      01003746H   LINE      ---      ---       #194
      0100374EH   LINE      ---      ---       #195
      01003756H   LINE      ---      ---       #197
      0100375AH   LINE      ---      ---       #198
      0100376DH   LINE      ---      ---       #200
      01003770H   LINE      ---      ---       #201
      01003770H   LINE      ---      ---       #203
      01003773H   LINE      ---      ---       #204
      01003773H   LINE      ---      ---       #205
      01003776H   LINE      ---      ---       #206
      01003776H   LINE      ---      ---       #207
      01003778H   LINE      ---      ---       #208
      0100377AH   LINE      ---      ---       #209
      0100377AH   LINE      ---      ---       #210
      0100377AH   LINE      ---      ---       #211
      0200F000H   SYMBOL    XDATA    ---       StrCanRx
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01003143H   BLOCK     CODE     ---       LVL=0
      01003143H   LINE      ---      ---       #232
      01003143H   LINE      ---      ---       #233
      01003143H   BLOCK     CODE     NEAR LAB  LVL=1
      01003143H   LINE      ---      ---       #242
      0100314CH   LINE      ---      ---       #243
      0100314EH   LINE      ---      ---       #244
      0100315EH   LINE      ---      ---       #245
      0100315EH   LINE      ---      ---       #246
      0100316CH   LINE      ---      ---       #247
      0100316CH   LINE      ---      ---       #248
      0100318DH   LINE      ---      ---       #249
      0100318DH   LINE      ---      ---       #250
      0100318FH   LINE      ---      ---       #251
      0100318FH   LINE      ---      ---       #252
      01003199H   LINE      ---      ---       #253
      010031A6H   LINE      ---      ---       #255
      010031A9H   LINE      ---      ---       #256
      010031A9H   LINE      ---      ---       #257
      010031B7H   LINE      ---      ---       #258
      010031B9H   LINE      ---      ---       #260
      010031B9H   LINE      ---      ---       #261
      010031C9H   LINE      ---      ---       #262
      010031C9H   LINE      ---      ---       #263
      010031D7H   LINE      ---      ---       #264
      010031D7H   LINE      ---      ---       #265
      010031FEH   LINE      ---      ---       #267
      0100320BH   LINE      ---      ---       #268
      0100320DH   LINE      ---      ---       #269
      0100320FH   LINE      ---      ---       #270
      0100320FH   LINE      ---      ---       #271
      01003222H   LINE      ---      ---       #272
      01003222H   LINE      ---      ---       #274
      01003232H   LINE      ---      ---       #275
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 88


      01003232H   LINE      ---      ---       #276
      01003240H   LINE      ---      ---       #277
      01003240H   LINE      ---      ---       #278
      01003267H   LINE      ---      ---       #280
      01003274H   LINE      ---      ---       #281
      01003276H   LINE      ---      ---       #283
      0100327BH   LINE      ---      ---       #286
      0100327DH   LINE      ---      ---       #287
      0100328DH   LINE      ---      ---       #288
      0100328DH   LINE      ---      ---       #289
      0100329BH   LINE      ---      ---       #290
      0100329BH   LINE      ---      ---       #291
      010032BCH   LINE      ---      ---       #292
      010032BCH   LINE      ---      ---       #293
      010032BEH   LINE      ---      ---       #294
      010032BEH   LINE      ---      ---       #295
      010032C8H   LINE      ---      ---       #296
      010032D5H   LINE      ---      ---       #298
      010032D8H   LINE      ---      ---       #299
      010032D8H   LINE      ---      ---       #301
      010032DAH   LINE      ---      ---       #303
      010032EAH   LINE      ---      ---       #304
      010032EAH   LINE      ---      ---       #305
      010032F8H   LINE      ---      ---       #306
      010032F8H   LINE      ---      ---       #307
      0100331FH   LINE      ---      ---       #309
      0100332CH   LINE      ---      ---       #310
      0100332EH   LINE      ---      ---       #311
      0100332EH   LINE      ---      ---       #313
      0100332EH   LINE      ---      ---       #316
      0200F000H   SYMBOL    XDATA    DWORD     i
      0200F004H   SYMBOL    XDATA    DWORD     j
      0200F02BH   SYMBOL    XDATA    ---       tmpSwSample1
      0200F02FH   SYMBOL    XDATA    ---       tmpSwSample2
      0200F033H   SYMBOL    XDATA    ---       tmpSwSample3
      0200F037H   SYMBOL    XDATA    INT       CntTremble
      00000020H.0 SYMBOL    BIT      BIT       FlgSample
      00000020H.1 SYMBOL    BIT      BIT       FlgSample1
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      ---         MODULE    ---      ---       BOOT
      00000080H   PUBLIC    IDATA    ---       WLBuf
      01002742H   PUBLIC    CODE     ---       BootMain
      0100271DH   PUBLIC    CODE     ---       Flash_Wait
      01003647H   PUBLIC    CODE     ---       _CAN_sendAck
      010030F8H   PUBLIC    CODE     ---       _CAN_waitTransmit
      01002707H   PUBLIC    CODE     ---       CAN_setWDTReset
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 89


      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
      000000BBH   SFRSYM    DATA     BYTE      MEM_NMICON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      00000080H   SFRSYM    DATA     BIT       P0_0
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      00000090H   SFRSYM    DATA     BIT       P1_0
      00000080H.1 SFRSYM    DATA     BIT       P0_1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      000000A0H   SFRSYM    DATA     BIT       P2_0
      00000090H.1 SFRSYM    DATA     BIT       P1_1
      00000080H.2 SFRSYM    DATA     BIT       P0_2
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000B0H   SFRSYM    DATA     BIT       P3_0
      000000A0H.1 SFRSYM    DATA     BIT       P2_1
      00000090H.2 SFRSYM    DATA     BIT       P1_2
      00000080H.3 SFRSYM    DATA     BIT       P0_3
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000C8H   SFRSYM    DATA     BIT       P4_0
      000000B0H.1 SFRSYM    DATA     BIT       P3_1
      000000A0H.2 SFRSYM    DATA     BIT       P2_2
      00000090H.3 SFRSYM    DATA     BIT       P1_3
      00000080H.4 SFRSYM    DATA     BIT       P0_4
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000C8H.1 SFRSYM    DATA     BIT       P4_1
      000000B0H.2 SFRSYM    DATA     BIT       P3_2
      000000A0H.3 SFRSYM    DATA     BIT       P2_3
      00000090H.4 SFRSYM    DATA     BIT       P1_4
      00000080H.5 SFRSYM    DATA     BIT       P0_5
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000B0H.3 SFRSYM    DATA     BIT       P3_3
      000000A0H.4 SFRSYM    DATA     BIT       P2_4
      00000090H.5 SFRSYM    DATA     BIT       P1_5
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 90


      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000C8H.3 SFRSYM    DATA     BIT       P4_3
      000000B0H.4 SFRSYM    DATA     BIT       P3_4
      000000A0H.5 SFRSYM    DATA     BIT       P2_5
      00000090H.6 SFRSYM    DATA     BIT       P1_6
      00000080H.7 SFRSYM    DATA     BIT       P0_7
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000B0H.5 SFRSYM    DATA     BIT       P3_5
      000000A0H.6 SFRSYM    DATA     BIT       P2_6
      00000090H.7 SFRSYM    DATA     BIT       P1_7
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000B0H.6 SFRSYM    DATA     BIT       P3_6
      000000A0H.7 SFRSYM    DATA     BIT       P2_7
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B0H.7 SFRSYM    DATA     BIT       P3_7
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      00000083H   SFRSYM    DATA     BYTE      MEM_DPH
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      00000082H   SFRSYM    DATA     BYTE      MEM_DPL
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 91


      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 92


      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      000000BCH   SFRSYM    DATA     BYTE      MEM_NMISR
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 93


      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 94


      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 95


      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH
      01000000H   SYMBOL    CONST    ---       _?ix1000

      01002707H   BLOCK     CODE     ---       LVL=0
      01002707H   LINE      ---      ---       #5
      01002707H   LINE      ---      ---       #6
      01002707H   LINE      ---      ---       #7
      01002709H   LINE      ---      ---       #12
      0100270CH   LINE      ---      ---       #13
      0100270EH   LINE      ---      ---       #19
      ---         BLOCKEND  ---      ---       LVL=0

      010030F8H   BLOCK     CODE     ---       LVL=0
      010030F8H   LINE      ---      ---       #23
      010030FDH   LINE      ---      ---       #24
      0200F026H   SYMBOL    XDATA    BYTE      RgMsgobj
      010030FDH   BLOCK     CODE     NEAR LAB  LVL=1
      010030FDH   LINE      ---      ---       #26
      0100312AH   LINE      ---      ---       #27
      0200F027H   SYMBOL    XDATA    DWORD     i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01003647H   BLOCK     CODE     ---       LVL=0
      01003647H   LINE      ---      ---       #34
      0100364FH   LINE      ---      ---       #35
      0200F01CH   SYMBOL    XDATA    BYTE      Ack0
      0200F01DH   SYMBOL    XDATA    BYTE      Ack1
      0100364FH   BLOCK     CODE     NEAR LAB  LVL=1
      0100364FH   LINE      ---      ---       #36
      01003662H   LINE      ---      ---       #38
      0100366AH   LINE      ---      ---       #39
      01003672H   LINE      ---      ---       #41
      0100367CH   LINE      ---      ---       #42
      01003680H   LINE      ---      ---       #43
      0200F01EH   SYMBOL    XDATA    ---       Arrtmp
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      0100271DH   BLOCK     CODE     ---       LVL=0
      0100271DH   LINE      ---      ---       #52
      0100271DH   LINE      ---      ---       #53
      0100271DH   BLOCK     CODE     NEAR LAB  LVL=1
      0100271DH   LINE      ---      ---       #56
      01002720H   LINE      ---      ---       #57
      01002723H   LINE      ---      ---       #58
      01002724H   LINE      ---      ---       #59
      01002724H   LINE      ---      ---       #60
      01002726H   LINE      ---      ---       #61
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 96


      01002726H   LINE      ---      ---       #62
      01002728H   LINE      ---      ---       #63
      01002728H   LINE      ---      ---       #64
      0100272DH   LINE      ---      ---       #65
      0100272DH   LINE      ---      ---       #66
      01002731H   LINE      ---      ---       #68
      01002731H   LINE      ---      ---       #69
      01002736H   LINE      ---      ---       #70
      0100273BH   LINE      ---      ---       #71
      01002740H   LINE      ---      ---       #72
      00000005H   SYMBOL    DATA     BYTE      ubCount0
      00000006H   SYMBOL    DATA     BYTE      ubCount1
      00000007H   SYMBOL    DATA     BYTE      ubCount2
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      01002742H   BLOCK     CODE     ---       LVL=0
      01002742H   LINE      ---      ---       #77
      01002742H   LINE      ---      ---       #78
      01002742H   BLOCK     CODE     NEAR LAB  LVL=1
      01002742H   LINE      ---      ---       #84
      0100274CH   LINE      ---      ---       #85
      0100274CH   LINE      ---      ---       #86
      0100275BH   LINE      ---      ---       #88
      01002763H   LINE      ---      ---       #89
      0100276BH   LINE      ---      ---       #90
      01002773H   LINE      ---      ---       #91
      0100277BH   LINE      ---      ---       #92
      01002783H   LINE      ---      ---       #93
      0100278BH   LINE      ---      ---       #94
      01002793H   LINE      ---      ---       #95
      01002798H   LINE      ---      ---       #97
      0100279AH   LINE      ---      ---       #98
      0100279CH   LINE      ---      ---       #99
      0100279FH   LINE      ---      ---       #101
      010027ABH   LINE      ---      ---       #102
      010027ABH   LINE      ---      ---       #103
      010027B2H   LINE      ---      ---       #104
      010027B4H   LINE      ---      ---       #105
      010027B4H   LINE      ---      ---       #107
      010027C2H   LINE      ---      ---       #108
      010027C2H   LINE      ---      ---       #109
      010027C4H   LINE      ---      ---       #110
      010027C6H   LINE      ---      ---       #111
      010027CDH   LINE      ---      ---       #112
      010027CFH   LINE      ---      ---       #114
      010027CFH   LINE      ---      ---       #115
      010027CFH   LINE      ---      ---       #116
      010027D8H   LINE      ---      ---       #117
      010027D8H   LINE      ---      ---       #118
      010027DCH   LINE      ---      ---       #119
      010027DFH   LINE      ---      ---       #120
      010027E2H   LINE      ---      ---       #121
      010027E6H   LINE      ---      ---       #122
      010027E8H   LINE      ---      ---       #123
      010027EFH   LINE      ---      ---       #124
      010027EFH   LINE      ---      ---       #127
      010027EFH   LINE      ---      ---       #128
      0200F000H   SYMBOL    XDATA    ---       StrBootRx
      0200F014H   SYMBOL    XDATA    ---       ulCANData
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  CODE     ---       LVL=1
      ---         BLOCKEND  CODE     ---       LVL=0

      ---         MODULE    ---      ---       XC88X_FLHANDLER
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 97


      010024CDH   PUBLIC    CODE     NEAR LAB  _DFLERASE
      010024FAH   PUBLIC    CODE     NEAR LAB  _FLPROG
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000F0H   SFRSYM    DATA     BYTE      B
      000000D0H   SFRSYM    DATA     BYTE      PSW
      010024F2H   SYMBOL    CODE     NEAR LAB  _DERASE_FAIL
      01002512H   SYMBOL    CODE     NEAR LAB  _PROG_FAIL
      0100DFF9H   SYMBOL    CODE     BYTE      FLASH_ERASE
      0100DFF3H   SYMBOL    CODE     BYTE      FLASH_ERASE_ABORT
      0100DFF6H   SYMBOL    CODE     BYTE      FLASH_PROG
      0100DB06H   SYMBOL    CODE     BYTE      FLASH_PROTECT
      0100DFF0H   SYMBOL    CODE     BYTE      FLASH_READ_STATUS

      010024CDH   BLOCK     CODE     NEAR LAB  LVL=0
      010024CDH   LINE      CODE     ---       #64
      010024CFH   LINE      CODE     ---       #65
      010024D1H   LINE      CODE     ---       #67
      010024D3H   LINE      CODE     ---       #68
      010024D5H   LINE      CODE     ---       #70
      010024D6H   LINE      CODE     ---       #71
      010024D8H   LINE      CODE     ---       #72
      010024DAH   LINE      CODE     ---       #73
      010024DCH   LINE      CODE     ---       #75
      010024DEH   LINE      CODE     ---       #76
      010024E0H   LINE      CODE     ---       #77
      010024E2H   LINE      CODE     ---       #87
      010024E5H   LINE      CODE     ---       #90
      010024E8H   LINE      CODE     ---       #91
      010024EAH   LINE      CODE     ---       #92
      010024ECH   LINE      CODE     ---       #93
      010024EEH   LINE      CODE     ---       #94
      010024F0H   LINE      CODE     ---       #95
      010024F1H   LINE      CODE     ---       #96
      010024F2H   LINE      CODE     ---       #98
      010024F4H   LINE      CODE     ---       #99
      010024F6H   LINE      CODE     ---       #100
      010024F8H   LINE      CODE     ---       #101
      010024F9H   LINE      CODE     ---       #102
      ---         BLOCKEND  ---      ---       LVL=0

      010024FAH   BLOCK     CODE     NEAR LAB  LVL=0
      010024FAH   LINE      CODE     ---       #121
      010024FCH   LINE      CODE     ---       #129
      010024FEH   LINE      CODE     ---       #130
      01002500H   LINE      CODE     ---       #131
      01002502H   LINE      CODE     ---       #141
      01002505H   LINE      CODE     ---       #144
      01002508H   LINE      CODE     ---       #145
      0100250AH   LINE      CODE     ---       #146
      0100250CH   LINE      CODE     ---       #147
      0100250EH   LINE      CODE     ---       #148
      01002510H   LINE      CODE     ---       #149
      01002511H   LINE      CODE     ---       #150
      01002512H   LINE      CODE     ---       #152
      01002514H   LINE      CODE     ---       #153
      01002516H   LINE      CODE     ---       #154
      01002518H   LINE      CODE     ---       #155
      01002519H   LINE      CODE     ---       #156
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C_INIT
      01002196H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?CLDPTR
      01002200H   PUBLIC    CODE     ---       ?C?CLDPTR
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 98



      ---         MODULE    ---      ---       ?C?CLDOPTR
      01002219H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CILDOPTR
      01002246H   PUBLIC    CODE     ---       ?C?CILDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01002279H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      0100228BH   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?ULCMP
      010022ADH   PUBLIC    CODE     ---       ?C?ULCMP

      ---         MODULE    ---      ---       ?C?ULSHR
      010022BEH   PUBLIC    CODE     ---       ?C?ULSHR

      ---         MODULE    ---      ---       ?C?LLDPTR
      010022D1H   PUBLIC    CODE     ---       ?C?LLDPTR

      ---         MODULE    ---      ---       ?C?LSTXDATA
      010022F1H   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      010022FDH   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?OFFXADD
      0100232EH   PUBLIC    CODE     ---       ?C?OFFXADD

      ---         MODULE    ---      ---       ?C?COPY517
      01002406H   PUBLIC    CODE     ---       ?C?COPY517

      ---         MODULE    ---      ---       ?C?LLDIDATA
      0100242CH   PUBLIC    CODE     ---       ?C?LLDIDATA

      ---         MODULE    ---      ---       ?C?LLDXDATA
      01002438H   PUBLIC    CODE     ---       ?C?LLDXDATA

      ---         MODULE    ---      ---       ?C?LLDPDATA
      01002444H   PUBLIC    CODE     ---       ?C?LLDPDATA

      ---         MODULE    ---      ---       ?C?LLDCODE
      01002450H   PUBLIC    CODE     ---       ?C?LLDCODE



INTER-MODULE CROSS-REFERENCE LISTING


NAME . . . . . . . . . . . . CLASS    MODULE NAMES
==================================================

?_ChangeCsMc33993?BIT. . . . BIT      SWDRIVER  
?_Mc33993CmdSend?BYTE. . . . XDATA    SWDRIVER  
?_Mc33993CmdSendOneBit?BIT . BIT      SWDRIVER  
?C?CILDOPTR. . . . . . . . . CODE     ?C?CILDOPTR  CAN  
?C?CLDOPTR . . . . . . . . . CODE     ?C?CLDOPTR  CAN  SWDRIVER  
?C?CLDPTR. . . . . . . . . . CODE     ?C?CLDPTR  CAN  SWDRIVER  
?C?CODESEG . . . . . . . . . NUMBER   ** LX51 GENERATED **  
?C?COPY517 . . . . . . . . . CODE     ?C?COPY517  BOOT  
?C?CSTOPTR . . . . . . . . . CODE     ?C?CSTOPTR  CAN  SWDRIVER  
?C?CSTPTR. . . . . . . . . . CODE     ?C?CSTPTR  CAN  
?C?DPSEL . . . . . . . . . . DATA     ?C_STARTUP  ?C?COPY517  SHARED_INT  
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 99


?C?LLDCODE . . . . . . . . . CODE     ?C?LLDCODE  ?C?LLDPTR  
?C?LLDIDATA. . . . . . . . . CODE     ?C?LLDIDATA  ?C?LLDPTR  
?C?LLDPDATA. . . . . . . . . CODE     ?C?LLDPDATA  ?C?LLDPTR  
?C?LLDPTR. . . . . . . . . . CODE     ?C?LLDPTR  CAN  
?C?LLDXDATA. . . . . . . . . CODE     ?C?LLDXDATA  ?C?LLDPTR  
?C?LSTKXDATA . . . . . . . . CODE     ?C?LSTKXDATA  BOOT  USER  
?C?LSTXDATA. . . . . . . . . CODE     ?C?LSTXDATA  BOOT  CAN  USER  
?C?OFFXADD . . . . . . . . . CODE     ?C?OFFXADD  USER  
?C?ULCMP . . . . . . . . . . CODE     ?C?ULCMP  BOOT  USER  
?C?ULSHR . . . . . . . . . . CODE     ?C?ULSHR  CAN  
?C?XDATASEG. . . . . . . . . NUMBER   ** LX51 GENERATED **  
?C_START . . . . . . . . . . CODE     ?C_INIT  ?C_STARTUP  
?C_STARTUP . . . . . . . . . CODE     ?C_STARTUP  MAIN  
_CAN_sendAck . . . . . . . . CODE     BOOT  
_CAN_ubNewData . . . . . . . CODE     CAN  BOOT  USER  
_CAN_ubRequestMsgObj . . . . CODE     CAN  BOOT  
_CAN_vGetMsgObj. . . . . . . CODE     CAN  BOOT  USER  
_CAN_vLoadData . . . . . . . CODE     CAN  BOOT  USER  
_CAN_vReleaseObj . . . . . . CODE     CAN  BOOT  USER  
_CAN_vSetListCommand . . . . CODE     CAN  
_CAN_vTransmit . . . . . . . CODE     CAN  BOOT  USER  
_CAN_vWriteAMData. . . . . . CODE     CAN  
_CAN_waitTransmit. . . . . . CODE     BOOT  USER  
_ChangeCsMc33993 . . . . . . CODE     SWDRIVER  
_DFlErase. . . . . . . . . . CODE     XC88X_FLHANDLER  BOOT  
_FlProg. . . . . . . . . . . CODE     XC88X_FLHANDLER  BOOT  
_LedLight. . . . . . . . . . CODE     SWDRIVER  USER  
_Mc33993CmdSend. . . . . . . CODE     SWDRIVER  
_Mc33993CmdSendOneBit. . . . CODE     SWDRIVER  
_Mc33993DataSendRcv. . . . . CODE     SWDRIVER  
_SSC_vSendData . . . . . . . CODE     SSC  SWDRIVER  
_SwStaSample . . . . . . . . CODE     SWDRIVER  USER  
AC . . . . . . . . . . . . . DATA     MAIN  
ACC. . . . . . . . . . . . . DATA     ?C_STARTUP  
ADC_CHCTR0 . . . . . . . . . DATA     MAIN  
ADC_CHCTR1 . . . . . . . . . DATA     MAIN  
ADC_CHCTR2 . . . . . . . . . DATA     MAIN  
ADC_CHCTR3 . . . . . . . . . DATA     MAIN  
ADC_CHCTR4 . . . . . . . . . DATA     MAIN  
ADC_CHCTR5 . . . . . . . . . DATA     MAIN  
ADC_CHCTR6 . . . . . . . . . DATA     MAIN  
ADC_CHCTR7 . . . . . . . . . DATA     MAIN  
ADC_CHINCR . . . . . . . . . DATA     MAIN  
ADC_CHINFR . . . . . . . . . DATA     MAIN  
ADC_CHINPR . . . . . . . . . DATA     MAIN  
ADC_CHINSR . . . . . . . . . DATA     MAIN  
ADC_CRCR1. . . . . . . . . . DATA     MAIN  
ADC_CRMR1. . . . . . . . . . DATA     MAIN  
ADC_CRPR1. . . . . . . . . . DATA     MAIN  
ADC_ETRCR. . . . . . . . . . DATA     MAIN  
ADC_EVINCR . . . . . . . . . DATA     MAIN  
ADC_EVINFR . . . . . . . . . DATA     MAIN  
ADC_EVINPR . . . . . . . . . DATA     MAIN  
ADC_EVINSR . . . . . . . . . DATA     MAIN  
ADC_GLOBCTR. . . . . . . . . DATA     MAIN  
ADC_GLOBSTR. . . . . . . . . DATA     MAIN  
ADC_INPCR0 . . . . . . . . . DATA     MAIN  
ADC_LCBR . . . . . . . . . . DATA     MAIN  
ADC_PAGE . . . . . . . . . . DATA     MAIN  
ADC_PRAR . . . . . . . . . . DATA     MAIN  
ADC_Q0R0 . . . . . . . . . . DATA     MAIN  
ADC_QBUR0. . . . . . . . . . DATA     MAIN  
ADC_QINR0. . . . . . . . . . DATA     MAIN  
ADC_QMR0 . . . . . . . . . . DATA     MAIN  
ADC_QSR0 . . . . . . . . . . DATA     MAIN  
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 100


ADC_RCR0 . . . . . . . . . . DATA     MAIN  
ADC_RCR1 . . . . . . . . . . DATA     MAIN  
ADC_RCR2 . . . . . . . . . . DATA     MAIN  
ADC_RCR3 . . . . . . . . . . DATA     MAIN  
ADC_RESR0H . . . . . . . . . DATA     MAIN  
ADC_RESR0L . . . . . . . . . DATA     MAIN  
ADC_RESR0LH. . . . . . . . . DATA     MAIN  
ADC_RESR1H . . . . . . . . . DATA     MAIN  
ADC_RESR1L . . . . . . . . . DATA     MAIN  
ADC_RESR1LH. . . . . . . . . DATA     MAIN  
ADC_RESR2H . . . . . . . . . DATA     MAIN  
ADC_RESR2L . . . . . . . . . DATA     MAIN  
ADC_RESR2LH. . . . . . . . . DATA     MAIN  
ADC_RESR3H . . . . . . . . . DATA     MAIN  
ADC_RESR3L . . . . . . . . . DATA     MAIN  
ADC_RESR3LH. . . . . . . . . DATA     MAIN  
ADC_RESRA0H. . . . . . . . . DATA     MAIN  
ADC_RESRA0L. . . . . . . . . DATA     MAIN  
ADC_RESRA0LH . . . . . . . . DATA     MAIN  
ADC_RESRA1H. . . . . . . . . DATA     MAIN  
ADC_RESRA1L. . . . . . . . . DATA     MAIN  
ADC_RESRA1LH . . . . . . . . DATA     MAIN  
ADC_RESRA2H. . . . . . . . . DATA     MAIN  
ADC_RESRA2L. . . . . . . . . DATA     MAIN  
ADC_RESRA2LH . . . . . . . . DATA     MAIN  
ADC_RESRA3H. . . . . . . . . DATA     MAIN  
ADC_RESRA3L. . . . . . . . . DATA     MAIN  
ADC_RESRA3LH . . . . . . . . DATA     MAIN  
ADC_VFCR . . . . . . . . . . DATA     MAIN  
B. . . . . . . . . . . . . . DATA     ?C_STARTUP  
BCON . . . . . . . . . . . . DATA     MAIN  
BG . . . . . . . . . . . . . DATA     MAIN  
BootMain . . . . . . . . . . CODE     BOOT  MAIN  
C_T2 . . . . . . . . . . . . DATA     MAIN  
CAN_ADCON. . . . . . . . . . DATA     MAIN  
CAN_ADH. . . . . . . . . . . DATA     MAIN  
CAN_ADL. . . . . . . . . . . DATA     MAIN  
CAN_ADLH . . . . . . . . . . DATA     MAIN  
CAN_DATA0. . . . . . . . . . DATA     MAIN  
CAN_DATA01 . . . . . . . . . DATA     MAIN  
CAN_DATA1. . . . . . . . . . DATA     MAIN  
CAN_DATA2. . . . . . . . . . DATA     MAIN  
CAN_DATA23 . . . . . . . . . DATA     MAIN  
CAN_DATA3. . . . . . . . . . DATA     MAIN  
CAN_setWDTReset. . . . . . . CODE     BOOT  
CAN_vInit. . . . . . . . . . CODE     CAN  MAIN  USER  
CanErrorProcess. . . . . . . CODE     USER  MAIN  
CanRXTX. . . . . . . . . . . CODE     USER  MAIN  
CanTransmit. . . . . . . . . CODE     USER  
CanTX. . . . . . . . . . . . CODE     USER  
CCU6_CC60RH. . . . . . . . . DATA     MAIN  
CCU6_CC60RL. . . . . . . . . DATA     MAIN  
CCU6_CC60RLH . . . . . . . . DATA     MAIN  
CCU6_CC60SRH . . . . . . . . DATA     MAIN  
CCU6_CC60SRL . . . . . . . . DATA     MAIN  
CCU6_CC60SRLH. . . . . . . . DATA     MAIN  
CCU6_CC61RH. . . . . . . . . DATA     MAIN  
CCU6_CC61RL. . . . . . . . . DATA     MAIN  
CCU6_CC61RLH . . . . . . . . DATA     MAIN  
CCU6_CC61SRH . . . . . . . . DATA     MAIN  
CCU6_CC61SRL . . . . . . . . DATA     MAIN  
CCU6_CC61SRLH. . . . . . . . DATA     MAIN  
CCU6_CC62RH. . . . . . . . . DATA     MAIN  
CCU6_CC62RL. . . . . . . . . DATA     MAIN  
CCU6_CC62RLH . . . . . . . . DATA     MAIN  
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 101


CCU6_CC62SRH . . . . . . . . DATA     MAIN  
CCU6_CC62SRL . . . . . . . . DATA     MAIN  
CCU6_CC62SRLH. . . . . . . . DATA     MAIN  
CCU6_CC63RH. . . . . . . . . DATA     MAIN  
CCU6_CC63RL. . . . . . . . . DATA     MAIN  
CCU6_CC63RLH . . . . . . . . DATA     MAIN  
CCU6_CC63SRH . . . . . . . . DATA     MAIN  
CCU6_CC63SRL . . . . . . . . DATA     MAIN  
CCU6_CC63SRLH. . . . . . . . DATA     MAIN  
CCU6_CMPMODIFH . . . . . . . DATA     MAIN  
CCU6_CMPMODIFL . . . . . . . DATA     MAIN  
CCU6_CMPSTATH. . . . . . . . DATA     MAIN  
CCU6_CMPSTATL. . . . . . . . DATA     MAIN  
CCU6_IENH. . . . . . . . . . DATA     MAIN  
CCU6_IENL. . . . . . . . . . DATA     MAIN  
CCU6_INPH. . . . . . . . . . DATA     MAIN  
CCU6_INPL. . . . . . . . . . DATA     MAIN  
CCU6_ISH . . . . . . . . . . DATA     MAIN  
CCU6_ISL . . . . . . . . . . DATA     MAIN  
CCU6_ISRH. . . . . . . . . . DATA     MAIN  
CCU6_ISRL. . . . . . . . . . DATA     MAIN  
CCU6_ISSH. . . . . . . . . . DATA     MAIN  
CCU6_ISSL. . . . . . . . . . DATA     MAIN  
CCU6_MCMCTR. . . . . . . . . DATA     MAIN  
CCU6_MCMOUTH . . . . . . . . DATA     MAIN  
CCU6_MCMOUTL . . . . . . . . DATA     MAIN  
CCU6_MCMOUTSH. . . . . . . . DATA     MAIN  
CCU6_MCMOUTSL. . . . . . . . DATA     MAIN  
CCU6_MODCTRH . . . . . . . . DATA     MAIN  
CCU6_MODCTRL . . . . . . . . DATA     MAIN  
CCU6_PAGE. . . . . . . . . . DATA     MAIN  
CCU6_PISEL0H . . . . . . . . DATA     MAIN  
CCU6_PISEL0L . . . . . . . . DATA     MAIN  
CCU6_PISEL2. . . . . . . . . DATA     MAIN  
CCU6_PSLR. . . . . . . . . . DATA     MAIN  
CCU6_T12DTCH . . . . . . . . DATA     MAIN  
CCU6_T12DTCL . . . . . . . . DATA     MAIN  
CCU6_T12H. . . . . . . . . . DATA     MAIN  
CCU6_T12L. . . . . . . . . . DATA     MAIN  
CCU6_T12LH . . . . . . . . . DATA     MAIN  
CCU6_T12MSELH. . . . . . . . DATA     MAIN  
CCU6_T12MSELL. . . . . . . . DATA     MAIN  
CCU6_T12PRH. . . . . . . . . DATA     MAIN  
CCU6_T12PRL. . . . . . . . . DATA     MAIN  
CCU6_T12PRLH . . . . . . . . DATA     MAIN  
CCU6_T13H. . . . . . . . . . DATA     MAIN  
CCU6_T13L. . . . . . . . . . DATA     MAIN  
CCU6_T13LH . . . . . . . . . DATA     MAIN  
CCU6_T13PRH. . . . . . . . . DATA     MAIN  
CCU6_T13PRL. . . . . . . . . DATA     MAIN  
CCU6_T13PRLH . . . . . . . . DATA     MAIN  
CCU6_TCTR0H. . . . . . . . . DATA     MAIN  
CCU6_TCTR0L. . . . . . . . . DATA     MAIN  
CCU6_TCTR2H. . . . . . . . . DATA     MAIN  
CCU6_TCTR2L. . . . . . . . . DATA     MAIN  
CCU6_TCTR4H. . . . . . . . . DATA     MAIN  
CCU6_TCTR4L. . . . . . . . . DATA     MAIN  
CCU6_TRPCTRH . . . . . . . . DATA     MAIN  
CCU6_TRPCTRL . . . . . . . . DATA     MAIN  
CD_BSY . . . . . . . . . . . DATA     MAIN  
CD_CON . . . . . . . . . . . DATA     MAIN  
CD_CORDXH. . . . . . . . . . DATA     MAIN  
CD_CORDXL. . . . . . . . . . DATA     MAIN  
CD_CORDYH. . . . . . . . . . DATA     MAIN  
CD_CORDYL. . . . . . . . . . DATA     MAIN  
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 102


CD_CORDZH. . . . . . . . . . DATA     MAIN  
CD_CORDZL. . . . . . . . . . DATA     MAIN  
CD_STATC . . . . . . . . . . DATA     MAIN  
CMCON. . . . . . . . . . . . DATA     ?C_STARTUP  
CntCan_1 . . . . . . . . . . XDATA    USER  
COCON. . . . . . . . . . . . DATA     MAIN  
CP_RL2 . . . . . . . . . . . DATA     MAIN  
CY . . . . . . . . . . . . . DATA     MAIN  
DMAP . . . . . . . . . . . . DATA     MAIN  
DPH. . . . . . . . . . . . . DATA     ?C_STARTUP  
DPL. . . . . . . . . . . . . DATA     ?C_STARTUP  
EA . . . . . . . . . . . . . DATA     MAIN  
EADC . . . . . . . . . . . . DATA     MAIN  
ECCIP0 . . . . . . . . . . . DATA     MAIN  
ECCIP1 . . . . . . . . . . . DATA     MAIN  
ECCIP2 . . . . . . . . . . . DATA     MAIN  
ECCIP3 . . . . . . . . . . . DATA     MAIN  
EO . . . . . . . . . . . . . DATA     MAIN  
EOC. . . . . . . . . . . . . DATA     MAIN  
ERROR. . . . . . . . . . . . DATA     MAIN  
ES . . . . . . . . . . . . . DATA     MAIN  
ESSC . . . . . . . . . . . . DATA     MAIN  
ET0. . . . . . . . . . . . . DATA     MAIN  
ET1. . . . . . . . . . . . . DATA     MAIN  
ET2. . . . . . . . . . . . . DATA     MAIN  
EX0. . . . . . . . . . . . . DATA     MAIN  
EX1. . . . . . . . . . . . . DATA     MAIN  
EX2. . . . . . . . . . . . . DATA     MAIN  
EXEN2. . . . . . . . . . . . DATA     MAIN  
EXF2 . . . . . . . . . . . . DATA     MAIN  
EXICON0. . . . . . . . . . . DATA     MAIN  
EXICON1. . . . . . . . . . . DATA     MAIN  
EXM. . . . . . . . . . . . . DATA     MAIN  
F0 . . . . . . . . . . . . . DATA     MAIN  
F1 . . . . . . . . . . . . . DATA     MAIN  
FDCON. . . . . . . . . . . . DATA     MAIN  
FDRES. . . . . . . . . . . . DATA     MAIN  
FDSTEP . . . . . . . . . . . DATA     MAIN  
FEAH . . . . . . . . . . . . DATA     MAIN  
FEAL . . . . . . . . . . . . DATA     MAIN  
Flash_Wait . . . . . . . . . CODE     BOOT  
FlgCan_1 . . . . . . . . . . BIT      USER  
FlgOneTime . . . . . . . . . BIT      USER  
HWBPDR . . . . . . . . . . . DATA     MAIN  
HWBPSR . . . . . . . . . . . DATA     MAIN  
ID . . . . . . . . . . . . . DATA     MAIN  
IE0. . . . . . . . . . . . . DATA     MAIN  
IE1. . . . . . . . . . . . . DATA     MAIN  
IEN0 . . . . . . . . . . . . DATA     MAIN  
IEN1 . . . . . . . . . . . . DATA     MAIN  
IERR . . . . . . . . . . . . DATA     MAIN  
InitMc33993Sec . . . . . . . CODE     SWDRIVER  
InitSW . . . . . . . . . . . CODE     SWDRIVER  MAIN  
INT_EN . . . . . . . . . . . DATA     MAIN  
IO_vInit . . . . . . . . . . CODE     IO  MAIN  
IP . . . . . . . . . . . . . DATA     MAIN  
IP1. . . . . . . . . . . . . DATA     MAIN  
IPH. . . . . . . . . . . . . DATA     MAIN  
IPH1 . . . . . . . . . . . . DATA     MAIN  
IRCON0 . . . . . . . . . . . DATA     MAIN  
IRCON1 . . . . . . . . . . . DATA     MAIN  
IRCON2 . . . . . . . . . . . DATA     MAIN  
IRCON3 . . . . . . . . . . . DATA     MAIN  
IRCON4 . . . . . . . . . . . DATA     MAIN  
IRDY . . . . . . . . . . . . DATA     MAIN  
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 103


IT0. . . . . . . . . . . . . DATA     MAIN  
IT1. . . . . . . . . . . . . DATA     MAIN  
KEEPX. . . . . . . . . . . . DATA     MAIN  
KEEPY. . . . . . . . . . . . DATA     MAIN  
KEEPZ. . . . . . . . . . . . DATA     MAIN  
LedDr. . . . . . . . . . . . CODE     USER  
main . . . . . . . . . . . . CODE     MAIN  ?C_INIT  
MAIN_vInit . . . . . . . . . CODE     MAIN  
MDU_BSY. . . . . . . . . . . DATA     MAIN  
MDU_MD0. . . . . . . . . . . DATA     MAIN  
MDU_MD1. . . . . . . . . . . DATA     MAIN  
MDU_MD2. . . . . . . . . . . DATA     MAIN  
MDU_MD3. . . . . . . . . . . DATA     MAIN  
MDU_MD4. . . . . . . . . . . DATA     MAIN  
MDU_MD5. . . . . . . . . . . DATA     MAIN  
MDU_MDUCON . . . . . . . . . DATA     MAIN  
MDU_MDUSTAT. . . . . . . . . DATA     MAIN  
MDU_MR0. . . . . . . . . . . DATA     MAIN  
MDU_MR1. . . . . . . . . . . DATA     MAIN  
MDU_MR2. . . . . . . . . . . DATA     MAIN  
MDU_MR3. . . . . . . . . . . DATA     MAIN  
MDU_MR4. . . . . . . . . . . DATA     MAIN  
MDU_MR5. . . . . . . . . . . DATA     MAIN  
MEM_DPH. . . . . . . . . . . DATA     MAIN  
MEM_DPL. . . . . . . . . . . DATA     MAIN  
MEM_NMICON . . . . . . . . . DATA     MAIN  
MEM_NMISR. . . . . . . . . . DATA     MAIN  
MEX3 . . . . . . . . . . . . DATA     ?C_STARTUP  
MISC_CON . . . . . . . . . . DATA     MAIN  
MMBPCR . . . . . . . . . . . DATA     MAIN  
MMCR . . . . . . . . . . . . DATA     MAIN  
MMCR2. . . . . . . . . . . . DATA     MAIN  
MMDR . . . . . . . . . . . . DATA     MAIN  
MMICR. . . . . . . . . . . . DATA     MAIN  
MMSR . . . . . . . . . . . . DATA     MAIN  
MMWR1. . . . . . . . . . . . DATA     MAIN  
MMWR2. . . . . . . . . . . . DATA     MAIN  
MODPISEL . . . . . . . . . . DATA     MAIN  
MODPISEL1. . . . . . . . . . DATA     MAIN  
MODPISEL2. . . . . . . . . . DATA     MAIN  
MODSUSP. . . . . . . . . . . DATA     MAIN  
NMICON . . . . . . . . . . . DATA     MAIN  
NMISR. . . . . . . . . . . . DATA     MAIN  
OSC_CON. . . . . . . . . . . DATA     ?C_STARTUP  
OV . . . . . . . . . . . . . DATA     MAIN  
P. . . . . . . . . . . . . . DATA     MAIN  
P0_0 . . . . . . . . . . . . DATA     MAIN  
P0_1 . . . . . . . . . . . . DATA     MAIN  
P0_2 . . . . . . . . . . . . DATA     MAIN  
P0_3 . . . . . . . . . . . . DATA     MAIN  
P0_4 . . . . . . . . . . . . DATA     MAIN  
P0_5 . . . . . . . . . . . . DATA     MAIN  
P0_7 . . . . . . . . . . . . DATA     MAIN  
P0_ALTSEL0 . . . . . . . . . DATA     MAIN  
P0_ALTSEL1 . . . . . . . . . DATA     MAIN  
P0_DATA. . . . . . . . . . . DATA     MAIN  
P0_DIR . . . . . . . . . . . DATA     MAIN  
P0_OD. . . . . . . . . . . . DATA     MAIN  
P0_PUDEN . . . . . . . . . . DATA     MAIN  
P0_PUDSEL. . . . . . . . . . DATA     MAIN  
P1_0 . . . . . . . . . . . . DATA     MAIN  
P1_1 . . . . . . . . . . . . DATA     MAIN  
P1_2 . . . . . . . . . . . . DATA     MAIN  
P1_3 . . . . . . . . . . . . DATA     MAIN  
P1_4 . . . . . . . . . . . . DATA     MAIN  
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 104


P1_5 . . . . . . . . . . . . DATA     MAIN  
P1_6 . . . . . . . . . . . . DATA     MAIN  
P1_7 . . . . . . . . . . . . DATA     MAIN  
P1_ALTSEL0 . . . . . . . . . DATA     MAIN  
P1_ALTSEL1 . . . . . . . . . DATA     MAIN  
P1_DATA. . . . . . . . . . . DATA     MAIN  
P1_DIR . . . . . . . . . . . DATA     MAIN  
P1_OD. . . . . . . . . . . . DATA     MAIN  
P1_PUDEN . . . . . . . . . . DATA     MAIN  
P1_PUDSEL. . . . . . . . . . DATA     MAIN  
P2_0 . . . . . . . . . . . . DATA     MAIN  
P2_1 . . . . . . . . . . . . DATA     MAIN  
P2_2 . . . . . . . . . . . . DATA     MAIN  
P2_3 . . . . . . . . . . . . DATA     MAIN  
P2_4 . . . . . . . . . . . . DATA     MAIN  
P2_5 . . . . . . . . . . . . DATA     MAIN  
P2_6 . . . . . . . . . . . . DATA     MAIN  
P2_7 . . . . . . . . . . . . DATA     MAIN  
P2_DATA. . . . . . . . . . . DATA     MAIN  
P2_DIR . . . . . . . . . . . DATA     MAIN  
P2_PUDEN . . . . . . . . . . DATA     MAIN  
P2_PUDSEL. . . . . . . . . . DATA     MAIN  
P3_0 . . . . . . . . . . . . DATA     MAIN  
P3_1 . . . . . . . . . . . . DATA     MAIN  
P3_2 . . . . . . . . . . . . DATA     MAIN  
P3_3 . . . . . . . . . . . . DATA     MAIN  
P3_4 . . . . . . . . . . . . DATA     MAIN  
P3_5 . . . . . . . . . . . . DATA     MAIN  
P3_6 . . . . . . . . . . . . DATA     MAIN  
P3_7 . . . . . . . . . . . . DATA     MAIN  
P3_ALTSEL0 . . . . . . . . . DATA     MAIN  
P3_ALTSEL1 . . . . . . . . . DATA     MAIN  
P3_DATA. . . . . . . . . . . DATA     MAIN  
P3_DIR . . . . . . . . . . . DATA     MAIN  
P3_OD. . . . . . . . . . . . DATA     MAIN  
P3_PUDEN . . . . . . . . . . DATA     MAIN  
P3_PUDSEL. . . . . . . . . . DATA     MAIN  
P4_0 . . . . . . . . . . . . DATA     MAIN  
P4_1 . . . . . . . . . . . . DATA     MAIN  
P4_3 . . . . . . . . . . . . DATA     MAIN  
P4_ALTSEL0 . . . . . . . . . DATA     MAIN  
P4_ALTSEL1 . . . . . . . . . DATA     MAIN  
P4_DATA. . . . . . . . . . . DATA     MAIN  
P4_DIR . . . . . . . . . . . DATA     MAIN  
P4_OD. . . . . . . . . . . . DATA     MAIN  
P4_PUDEN . . . . . . . . . . DATA     MAIN  
P4_PUDSEL. . . . . . . . . . DATA     MAIN  
P5_ALTSEL0 . . . . . . . . . DATA     MAIN  
P5_ALTSEL1 . . . . . . . . . DATA     MAIN  
P5_DATA. . . . . . . . . . . DATA     MAIN  
P5_DIR . . . . . . . . . . . DATA     MAIN  
P5_OD. . . . . . . . . . . . DATA     MAIN  
P5_PUDEN . . . . . . . . . . DATA     MAIN  
P5_PUDSEL. . . . . . . . . . DATA     MAIN  
PADC . . . . . . . . . . . . DATA     MAIN  
PASSWD . . . . . . . . . . . DATA     ?C_STARTUP  
Password . . . . . . . . . . CODE     USER  
PCCIP0 . . . . . . . . . . . DATA     MAIN  
PCCIP1 . . . . . . . . . . . DATA     MAIN  
PCCIP2 . . . . . . . . . . . DATA     MAIN  
PCCIP3 . . . . . . . . . . . DATA     MAIN  
PCON . . . . . . . . . . . . DATA     MAIN  
PLL_CON. . . . . . . . . . . DATA     ?C_STARTUP  
PLL_CON1 . . . . . . . . . . DATA     ?C_STARTUP  
PMCON0 . . . . . . . . . . . DATA     MAIN  
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 105


PMCON1 . . . . . . . . . . . DATA     MAIN  
PMCON2 . . . . . . . . . . . DATA     MAIN  
PORT_PAGE. . . . . . . . . . DATA     MAIN  
PS . . . . . . . . . . . . . DATA     MAIN  
PSSC . . . . . . . . . . . . DATA     MAIN  
PSW. . . . . . . . . . . . . DATA     MAIN  
PT0. . . . . . . . . . . . . DATA     MAIN  
PT1. . . . . . . . . . . . . DATA     MAIN  
PT2. . . . . . . . . . . . . DATA     MAIN  
PX0. . . . . . . . . . . . . DATA     MAIN  
PX1. . . . . . . . . . . . . DATA     MAIN  
PX2. . . . . . . . . . . . . DATA     MAIN  
PXM. . . . . . . . . . . . . DATA     MAIN  
RB8. . . . . . . . . . . . . DATA     MAIN  
RB8_1. . . . . . . . . . . . DATA     MAIN  
REN. . . . . . . . . . . . . DATA     MAIN  
REN_1. . . . . . . . . . . . DATA     MAIN  
RgCanPerid . . . . . . . . . XDATA    USER  
RI . . . . . . . . . . . . . DATA     MAIN  
RI_1 . . . . . . . . . . . . DATA     MAIN  
RS0. . . . . . . . . . . . . DATA     MAIN  
RS1. . . . . . . . . . . . . DATA     MAIN  
RTCProcess . . . . . . . . . CODE     USER  SHARED_INT  
SBUF . . . . . . . . . . . . DATA     MAIN  
SCON . . . . . . . . . . . . DATA     MAIN  
SCU_PAGE . . . . . . . . . . DATA     ?C_STARTUP  
SHINT_vInit. . . . . . . . . CODE     SHARED_INT  MAIN  
SHINT_viXINTR5Isr. . . . . . CODE     SHARED_INT  
SM0. . . . . . . . . . . . . DATA     MAIN  
SM0_1. . . . . . . . . . . . DATA     MAIN  
SM1. . . . . . . . . . . . . DATA     MAIN  
SM1_1. . . . . . . . . . . . DATA     MAIN  
SM2. . . . . . . . . . . . . DATA     MAIN  
SM2_1. . . . . . . . . . . . DATA     MAIN  
SP . . . . . . . . . . . . . DATA     ?C_STARTUP  
SSC_BRH. . . . . . . . . . . DATA     MAIN  
SSC_BRL. . . . . . . . . . . DATA     MAIN  
SSC_CONH_O . . . . . . . . . DATA     MAIN  
SSC_CONH_P . . . . . . . . . DATA     MAIN  
SSC_CONL_O . . . . . . . . . DATA     MAIN  
SSC_CONL_P . . . . . . . . . DATA     MAIN  
SSC_PISEL. . . . . . . . . . DATA     MAIN  
SSC_RBL. . . . . . . . . . . DATA     MAIN  
SSC_TBL. . . . . . . . . . . DATA     MAIN  
SSC_vGetData . . . . . . . . CODE     SSC  SWDRIVER  
SSC_vInit. . . . . . . . . . CODE     SSC  MAIN  
SwSample . . . . . . . . . . CODE     USER  MAIN  
SYSCON0. . . . . . . . . . . DATA     MAIN  
T21_RC2H . . . . . . . . . . DATA     MAIN  
T21_RC2L . . . . . . . . . . DATA     MAIN  
T21_RC2LH. . . . . . . . . . DATA     MAIN  
T21_T2CON. . . . . . . . . . DATA     MAIN  
T21_T2H. . . . . . . . . . . DATA     MAIN  
T21_T2L. . . . . . . . . . . DATA     MAIN  
T21_T2LH . . . . . . . . . . DATA     MAIN  
T21_T2MOD. . . . . . . . . . DATA     MAIN  
T2_RC2H. . . . . . . . . . . DATA     MAIN  
T2_RC2L. . . . . . . . . . . DATA     MAIN  
T2_RC2LH . . . . . . . . . . DATA     MAIN  
T2_T2CON . . . . . . . . . . DATA     MAIN  
T2_T2H . . . . . . . . . . . DATA     MAIN  
T2_T2L . . . . . . . . . . . DATA     MAIN  
T2_T2LH. . . . . . . . . . . DATA     MAIN  
T2_T2MOD . . . . . . . . . . DATA     MAIN  
T2_vInit . . . . . . . . . . CODE     T2  MAIN  
LX51 LINKER/LOCATER V4.58                                                             07/13/2013  18:09:34  PAGE 106


TB8. . . . . . . . . . . . . DATA     MAIN  
TB8_1. . . . . . . . . . . . DATA     MAIN  
TCON . . . . . . . . . . . . DATA     MAIN  
TF0. . . . . . . . . . . . . DATA     MAIN  
TF1. . . . . . . . . . . . . DATA     MAIN  
TF2. . . . . . . . . . . . . DATA     MAIN  
TH0. . . . . . . . . . . . . DATA     MAIN  
TH1. . . . . . . . . . . . . DATA     MAIN  
TI . . . . . . . . . . . . . DATA     MAIN  
TI_1 . . . . . . . . . . . . DATA     MAIN  
TL0. . . . . . . . . . . . . DATA     MAIN  
TL1. . . . . . . . . . . . . DATA     MAIN  
TMOD . . . . . . . . . . . . DATA     MAIN  
TR0. . . . . . . . . . . . . DATA     MAIN  
TR1. . . . . . . . . . . . . DATA     MAIN  
TR2. . . . . . . . . . . . . DATA     MAIN  
UART1_BCON . . . . . . . . . DATA     MAIN  
UART1_BG . . . . . . . . . . DATA     MAIN  
UART1_FDCON. . . . . . . . . DATA     MAIN  
UART1_FDRES. . . . . . . . . DATA     MAIN  
UART1_FDSTEP . . . . . . . . DATA     MAIN  
UART1_SBUF . . . . . . . . . DATA     MAIN  
UART1_SCON . . . . . . . . . DATA     MAIN  
UnInfCan_1 . . . . . . . . . XDATA    USER  
UnSwOut_1. . . . . . . . . . XDATA    USER  
UnSwSample_1 . . . . . . . . XDATA    USER  
WDT_vDisable . . . . . . . . CODE     WDT  BOOT  
WDT_vInit. . . . . . . . . . CODE     WDT  MAIN  
WDTCON . . . . . . . . . . . DATA     MAIN  
WDTH . . . . . . . . . . . . DATA     MAIN  
WDTL . . . . . . . . . . . . DATA     MAIN  
WDTREL . . . . . . . . . . . DATA     MAIN  
WDTWINB. . . . . . . . . . . DATA     MAIN  
WLBuf. . . . . . . . . . . . IDATA    BOOT  
XADDRH . . . . . . . . . . . DATA     ?C_STARTUP  

Program Size: data=57.6 xdata=113 const=8 code=7312
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)

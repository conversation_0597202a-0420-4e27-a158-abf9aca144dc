;************************************************************************************
;*  FILE        : XC88x_FLHANDLER.ASM                                               *
;*  DESCRIPTION : Flash handler routine for XC88x AA and AB                                *
;*  COPYRIGHT   : (c) 2006 Infineon Technologies AG                                 *
;*  UPDATED     : 14/03/2006                                                        *
;************************************************************************************
$ NOMOD51
$ DEBUG
#include "XC88x_FLADDR.H"  

FLASH_ERASE        CODE  FLASH_ERASE_BROM_ENTRY
FLASH_PROG         CODE  FLASH_PROGRAM_BROM_ENTRY
FLASH_ERASE_ABORT  CODE  FLASH_ERASE_ABORT_BROM_ENTRY
FLASH_READ_STATUS  CODE  FLASH_READ_STATUS_BROM_ENTRY
FLASH_PROTECT      CODE  FLASH_PROTECT_BROM_ENTRY
     
DSEG AT  MAGIC_MEMORY
DS   RESERVED_BYTES                        ; Reserved for FLASH Handler in Boot ROM




;------------------------------------------------------------------------------------------
; Function Name   : bit DFlErase (unsigned int DFlash0Sector, unsigned int DFlash1Sector)
; Description     : Erase DFlash Sectors (Bank3 and Bank4)
; Input Parameter : DFlash0Sector => Bit0 = Erase Sector0
;                                    Bit1 = Erase Sector1
;                                    Bit2 = Erase Sector2
;                                    Bit3 = Erase Sector3
;                                    Bit4 = Erase Sector4
;                                    Bit5 = Erase Sector5
;                                    Bit6 = Erase Sector6
;                                    Bit7 = Erase Sector7
;                                    Bit8 = Erase Sector8
;                                    Bit9 = Erase Sector9
;                   DFlash1Sector => Bit0 = Erase Sector0
;                                    Bit1 = Erase Sector1
;                                    Bit2 = Erase Sector2
;                                    Bit3 = Erase Sector3
;                                    Bit4 = Erase Sector4
;                                    Bit5 = Erase Sector5
;                                    Bit6 = Erase Sector6
;                                    Bit7 = Erase Sector7
;                                    Bit8 = Erase Sector8
;                                    Bit9 = Erase Sector9
;                                                   
;                   e.g: DFlash0Sector = 0x202
;                        DFlash1Sector = 0x380
;                         ==> Will erase Bank3 Sector 1
;                                  erase Bank3 Sector 9
;                                  erase Bank4 Sector 7
;                                  erase Bank4 Sector 8
;                                  erase Bank4 Sector 9
; Return Value    : Bit => 1 = Successfull
;                          0 = Fail
; Required Stack Size = 12
;------------------------------------------------------------------------------------------
#if USE_DLFERASE
?PR?_DFlErase?XC88x_FLHANDLER SEGMENT CODE    
RSEG ?PR?_DFlErase?XC88x_FLHANDLER
PUBLIC _DFlErase
_DFlErase:
  USING  USE_BANK
  MOV  AR1,R6  ; BANK3
  MOV  AR0,R7  ; BANK3

  MOV  AR4,R4 ; BANK4
  MOV  AR3,R5 ; BANK4

  CLR    A
  MOV    AR5, A ; BANK0
  MOV    AR6, A ; BANK1
  MOV    AR7, A ; BANK2

  PUSH   PSW
	PUSH   ACC
	PUSH   B
#if (USE_BANK == 0)
  ANL    PSW, #0xE7      ; Set to Bank0
#elseif (USE_BANK == 1)
  SETB   PSW.3
  CLR    PSW.4
#elif (USE_BANK == 2)
  CLR    PSW.3
  SETB   PSW.4
#elif (USE_BANK == 3)
  ORL    PSW, #0x18
#endif 

  LCALL  FLASH_ERASE
  JC     _derase_fail
	POP    B
	POP    ACC
  POP    PSW
  SETB   C
  RET
_derase_fail:
	POP    B
	POP    ACC
  POP    PSW
  CLR    C
RET
#endif

;------------------------------------------------------------------------------------------
; Function Name:   bit FlProg(unsigned char idata *WlBuf)
; Description:     Program the 32byte data in the WlBuf to the EEPROM
;                  When calling this function, user should already programmed
;                  the DPTR with the EEPROM address (aligned to 32byte boundary)
; Input Parameter: *WlBuf  ==> The pointer to the starting location of the buffer
;                              in the IDATA memory.
; Return Value:    Bit => 1 = Successfull
;                         0 = Fail
; Required Stack Size = 12
;------------------------------------------------------------------------------------------
?PR?_FlProg?XC88x_FLHANDLER SEGMENT CODE    
RSEG ?PR?_FlProg?XC88x_FLHANDLER
PUBLIC _FlProg
_FlProg:
using USE_BANK
  MOV   AR0,R7          ; move C51 parameter registers to AR0
                       ; this is the pointer to the WLBuf
											 
;	MOV		A,#0x6F			
;	MOV		DPH,A
;	MOV		A,#0x0		; the DPL will = DPL+0x20 after one successful program	
;	MOV		DPL,A											 
											 
  PUSH   PSW
	PUSH   ACC
	PUSH   B
#if (USE_BANK == 0)
  ANL    PSW, #0xE7      ; Set to Bank0
#elif (USE_BANK == 1)
  SETB   PSW.3
  CLR    PSW.4
#elif (USE_BANK == 2)
  CLR    PSW.3
  SETB   PSW.4
#elif (USE_BANK == 3)
  ORL    PSW, #0x18
#endif
	
  LCALL  FLASH_PROG     ; LCALL FLASH_PROG
  JC     _prog_fail
	POP    B
	POP    ACC
  POP    PSW
  SETB   C
  RET
_prog_fail:
	POP    B
	POP    ACC
  POP    PSW
  CLR    C
RET







END
        
;;#endif    ;;#if (XC88xAA)

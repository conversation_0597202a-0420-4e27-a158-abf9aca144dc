LX51 LINKER/LOCATER V4.58                                                               02/07/2015  14:09:42  PAGE 1


LX51 LINKER/LOCATER V4.58, INVOKED BY:
C:\KEIL\C51\BIN\LX51.EXE MAIN.obj, vectorMap.obj, START_XC.obj TO JumpPro CLASSES (CODE (C:0X0100-C:0X1FFF)) SEGMENTS (?
>> C_C51STARTUP (C:0X0100), ?PR?MAIN?MAIN)


CPU MODE:     8051 MODE
MEMORY MODEL: SMALL


INPUT MODULES INCLUDED:
  MAIN.obj (MAIN)
         COMMENT TYPE 0: C51 V9.50a
  vectorMap.obj (VECTORMAP)
         COMMENT TYPE 0: AX51 V3.07d
  START_XC.obj (?C_STARTUP)
         COMMENT TYPE 0: AX51 V3.07d


ACTIVE MEMORY CLASSES OF MODULE:  JumpPro (MAIN)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000100H   C:001FFFH   000099H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
I:000000H   I:000000H   I:00007FH   000008H   DATA


MEMORY MAP OF MODULE:  JumpPro (MAIN)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   000008H   000001H   BYTE   UNIT     IDATA          ?STACK

* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO?START_XC?3
000003H   000005H   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?1
000006H   00000AH   000005H   ---    ---      **GAP**
00000BH   00000DH   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?2
00000EH   000012H   000005H   ---    ---      **GAP**
000013H   000015H   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?3
000016H   00001AH   000005H   ---    ---      **GAP**
00001BH   00001DH   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?4
00001EH   000022H   000005H   ---    ---      **GAP**
000023H   000025H   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?5
000026H   00002AH   000005H   ---    ---      **GAP**
00002BH   00002DH   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?6
00002EH   000032H   000005H   ---    ---      **GAP**
000033H   000035H   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?7
000036H   00003AH   000005H   ---    ---      **GAP**
00003BH   00003DH   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?8
00003EH   000042H   000005H   ---    ---      **GAP**
000043H   000045H   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?9
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?10
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?11
000056H   00005AH   000005H   ---    ---      **GAP**
00005BH   00005DH   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?12
00005EH   000062H   000005H   ---    ---      **GAP**
000063H   000065H   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?13
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?14
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 2


00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   ---    OFFS..   CODE           ?CO?VECTORMAP?15
000076H   0000FFH   00008AH   ---    ---      **GAP**
000100H   00015AH   00005BH   BYTE   UNIT     CODE           ?C_C51STARTUP
00015BH   000168H   00000EH   BYTE   UNIT     CODE           ?PR?MAIN?MAIN



OVERLAY MAP OF MODULE:   JumpPro (MAIN)


FUNCTION/MODULE              BIT_GROUP   DATA_GROUP 
--> CALLED FUNCTION/MODULE  START  STOP  START  STOP
====================================================
?C_C51STARTUP               ----- -----  ----- -----
  +--> MAIN/MAIN

MAIN/MAIN                   ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  JumpPro (MAIN)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
      00000000H   NUMBER   ---       ?C?CODESEG
      000000A2H   DATA     BYTE      ?C?DPSEL
      00000000H   NUMBER   ---       ?C?XDATASEG
      0100015BH   CODE     ---       ?C_START
      01000000H   CODE     NEAR LAB  ?C_STARTUP
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
*SFR* 000000CAH   DATA     BYTE      ADC_CHCTR0
*SFR* 000000CBH   DATA     BYTE      ADC_CHCTR1
*SFR* 000000CCH   DATA     BYTE      ADC_CHCTR2
*SFR* 000000CDH   DATA     BYTE      ADC_CHCTR3
*SFR* 000000CEH   DATA     BYTE      ADC_CHCTR4
*SFR* 000000CFH   DATA     BYTE      ADC_CHCTR5
*SFR* 000000D2H   DATA     BYTE      ADC_CHCTR6
*SFR* 000000D3H   DATA     BYTE      ADC_CHCTR7
*SFR* 000000CBH   DATA     BYTE      ADC_CHINCR
*SFR* 000000CAH   DATA     BYTE      ADC_CHINFR
*SFR* 000000CDH   DATA     BYTE      ADC_CHINPR
*SFR* 000000CCH   DATA     BYTE      ADC_CHINSR
*SFR* 000000CAH   DATA     BYTE      ADC_CRCR1
*SFR* 000000CCH   DATA     BYTE      ADC_CRMR1
*SFR* 000000CBH   DATA     BYTE      ADC_CRPR1
*SFR* 000000CFH   DATA     BYTE      ADC_ETRCR
*SFR* 000000CFH   DATA     BYTE      ADC_EVINCR
*SFR* 000000CEH   DATA     BYTE      ADC_EVINFR
*SFR* 000000D3H   DATA     BYTE      ADC_EVINPR
*SFR* 000000D2H   DATA     BYTE      ADC_EVINSR
*SFR* 000000CAH   DATA     BYTE      ADC_GLOBCTR
*SFR* 000000CBH   DATA     BYTE      ADC_GLOBSTR
*SFR* 000000CEH   DATA     BYTE      ADC_INPCR0
*SFR* 000000CDH   DATA     BYTE      ADC_LCBR
*SFR* 000000D1H   DATA     BYTE      ADC_PAGE
*SFR* 000000CCH   DATA     BYTE      ADC_PRAR
*SFR* 000000CFH   DATA     BYTE      ADC_Q0R0
*SFR* 000000D2H   DATA     BYTE      ADC_QBUR0
*SFR* 000000D2H   DATA     BYTE      ADC_QINR0
*SFR* 000000CDH   DATA     BYTE      ADC_QMR0
*SFR* 000000CEH   DATA     BYTE      ADC_QSR0
*SFR* 000000CAH   DATA     BYTE      ADC_RCR0
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 3


*SFR* 000000CBH   DATA     BYTE      ADC_RCR1
*SFR* 000000CCH   DATA     BYTE      ADC_RCR2
*SFR* 000000CDH   DATA     BYTE      ADC_RCR3
*SFR* 000000CBH   DATA     BYTE      ADC_RESR0H
*SFR* 000000CAH   DATA     BYTE      ADC_RESR0L
*SFR* 000000CAH   DATA     WORD      ADC_RESR0LH
*SFR* 000000CDH   DATA     BYTE      ADC_RESR1H
*SFR* 000000CCH   DATA     BYTE      ADC_RESR1L
*SFR* 000000CCH   DATA     WORD      ADC_RESR1LH
*SFR* 000000CFH   DATA     BYTE      ADC_RESR2H
*SFR* 000000CEH   DATA     BYTE      ADC_RESR2L
*SFR* 000000CEH   DATA     WORD      ADC_RESR2LH
*SFR* 000000D3H   DATA     BYTE      ADC_RESR3H
*SFR* 000000D2H   DATA     BYTE      ADC_RESR3L
*SFR* 000000D2H   DATA     WORD      ADC_RESR3LH
*SFR* 000000CBH   DATA     BYTE      ADC_RESRA0H
*SFR* 000000CAH   DATA     BYTE      ADC_RESRA0L
*SFR* 000000CAH   DATA     WORD      ADC_RESRA0LH
*SFR* 000000CDH   DATA     BYTE      ADC_RESRA1H
*SFR* 000000CCH   DATA     BYTE      ADC_RESRA1L
*SFR* 000000CCH   DATA     WORD      ADC_RESRA1LH
*SFR* 000000CFH   DATA     BYTE      ADC_RESRA2H
*SFR* 000000CEH   DATA     BYTE      ADC_RESRA2L
*SFR* 000000CEH   DATA     WORD      ADC_RESRA2LH
*SFR* 000000D3H   DATA     BYTE      ADC_RESRA3H
*SFR* 000000D2H   DATA     BYTE      ADC_RESRA3L
*SFR* 000000D2H   DATA     WORD      ADC_RESRA3LH
*SFR* 000000CEH   DATA     BYTE      ADC_VFCR
*SFR* 000000F0H   DATA     BYTE      B
*SFR* 000000BDH   DATA     BYTE      BCON
*SFR* 000000BEH   DATA     BYTE      BG
*SFR* 000000C0H.1 DATA     BIT       C_T2
*SFR* 000000D8H   DATA     BYTE      CAN_ADCON
*SFR* 000000DAH   DATA     BYTE      CAN_ADH
*SFR* 000000D9H   DATA     BYTE      CAN_ADL
*SFR* 000000D9H   DATA     WORD      CAN_ADLH
*SFR* 000000DBH   DATA     BYTE      CAN_DATA0
*SFR* 000000DBH   DATA     WORD      CAN_DATA01
*SFR* 000000DCH   DATA     BYTE      CAN_DATA1
*SFR* 000000DDH   DATA     BYTE      CAN_DATA2
*SFR* 000000DDH   DATA     WORD      CAN_DATA23
*SFR* 000000DEH   DATA     BYTE      CAN_DATA3
*SFR* 000000FBH   DATA     BYTE      CCU6_CC60RH
*SFR* 000000FAH   DATA     BYTE      CCU6_CC60RL
*SFR* 000000FAH   DATA     WORD      CCU6_CC60RLH
*SFR* 000000FBH   DATA     BYTE      CCU6_CC60SRH
*SFR* 000000FAH   DATA     BYTE      CCU6_CC60SRL
*SFR* 000000FAH   DATA     WORD      CCU6_CC60SRLH
*SFR* 000000FDH   DATA     BYTE      CCU6_CC61RH
*SFR* 000000FCH   DATA     BYTE      CCU6_CC61RL
*SFR* 000000FCH   DATA     WORD      CCU6_CC61RLH
*SFR* 000000FDH   DATA     BYTE      CCU6_CC61SRH
*SFR* 000000FCH   DATA     BYTE      CCU6_CC61SRL
*SFR* 000000FCH   DATA     WORD      CCU6_CC61SRLH
*SFR* 000000FFH   DATA     BYTE      CCU6_CC62RH
*SFR* 000000FEH   DATA     BYTE      CCU6_CC62RL
*SFR* 000000FEH   DATA     WORD      CCU6_CC62RLH
*SFR* 000000FFH   DATA     BYTE      CCU6_CC62SRH
*SFR* 000000FEH   DATA     BYTE      CCU6_CC62SRL
*SFR* 000000FEH   DATA     WORD      CCU6_CC62SRLH
*SFR* 0000009BH   DATA     BYTE      CCU6_CC63RH
*SFR* 0000009AH   DATA     BYTE      CCU6_CC63RL
*SFR* 0000009AH   DATA     WORD      CCU6_CC63RLH
*SFR* 0000009BH   DATA     BYTE      CCU6_CC63SRH
*SFR* 0000009AH   DATA     BYTE      CCU6_CC63SRL
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 4


*SFR* 0000009AH   DATA     WORD      CCU6_CC63SRLH
*SFR* 000000A7H   DATA     BYTE      CCU6_CMPMODIFH
*SFR* 000000A6H   DATA     BYTE      CCU6_CMPMODIFL
*SFR* 000000FFH   DATA     BYTE      CCU6_CMPSTATH
*SFR* 000000FEH   DATA     BYTE      CCU6_CMPSTATL
*SFR* 0000009DH   DATA     BYTE      CCU6_IENH
*SFR* 0000009CH   DATA     BYTE      CCU6_IENL
*SFR* 0000009FH   DATA     BYTE      CCU6_INPH
*SFR* 0000009EH   DATA     BYTE      CCU6_INPL
*SFR* 0000009DH   DATA     BYTE      CCU6_ISH
*SFR* 0000009CH   DATA     BYTE      CCU6_ISL
*SFR* 000000A5H   DATA     BYTE      CCU6_ISRH
*SFR* 000000A4H   DATA     BYTE      CCU6_ISRL
*SFR* 000000A5H   DATA     BYTE      CCU6_ISSH
*SFR* 000000A4H   DATA     BYTE      CCU6_ISSL
*SFR* 000000A7H   DATA     BYTE      CCU6_MCMCTR
*SFR* 0000009BH   DATA     BYTE      CCU6_MCMOUTH
*SFR* 0000009AH   DATA     BYTE      CCU6_MCMOUTL
*SFR* 0000009FH   DATA     BYTE      CCU6_MCMOUTSH
*SFR* 0000009EH   DATA     BYTE      CCU6_MCMOUTSL
*SFR* 000000FDH   DATA     BYTE      CCU6_MODCTRH
*SFR* 000000FCH   DATA     BYTE      CCU6_MODCTRL
*SFR* 000000A3H   DATA     BYTE      CCU6_PAGE
*SFR* 0000009FH   DATA     BYTE      CCU6_PISEL0H
*SFR* 0000009EH   DATA     BYTE      CCU6_PISEL0L
*SFR* 000000A4H   DATA     BYTE      CCU6_PISEL2
*SFR* 000000A6H   DATA     BYTE      CCU6_PSLR
*SFR* 000000A5H   DATA     BYTE      CCU6_T12DTCH
*SFR* 000000A4H   DATA     BYTE      CCU6_T12DTCL
*SFR* 000000FBH   DATA     BYTE      CCU6_T12H
*SFR* 000000FAH   DATA     BYTE      CCU6_T12L
*SFR* 000000FAH   DATA     WORD      CCU6_T12LH
*SFR* 0000009BH   DATA     BYTE      CCU6_T12MSELH
*SFR* 0000009AH   DATA     BYTE      CCU6_T12MSELL
*SFR* 0000009DH   DATA     BYTE      CCU6_T12PRH
*SFR* 0000009CH   DATA     BYTE      CCU6_T12PRL
*SFR* 0000009CH   DATA     WORD      CCU6_T12PRLH
*SFR* 000000FDH   DATA     BYTE      CCU6_T13H
*SFR* 000000FCH   DATA     BYTE      CCU6_T13L
*SFR* 000000FCH   DATA     WORD      CCU6_T13LH
*SFR* 0000009FH   DATA     BYTE      CCU6_T13PRH
*SFR* 0000009EH   DATA     BYTE      CCU6_T13PRL
*SFR* 0000009EH   DATA     WORD      CCU6_T13PRLH
*SFR* 000000A7H   DATA     BYTE      CCU6_TCTR0H
*SFR* 000000A6H   DATA     BYTE      CCU6_TCTR0L
*SFR* 000000FBH   DATA     BYTE      CCU6_TCTR2H
*SFR* 000000FAH   DATA     BYTE      CCU6_TCTR2L
*SFR* 0000009DH   DATA     BYTE      CCU6_TCTR4H
*SFR* 0000009CH   DATA     BYTE      CCU6_TCTR4L
*SFR* 000000FFH   DATA     BYTE      CCU6_TRPCTRH
*SFR* 000000FEH   DATA     BYTE      CCU6_TRPCTRL
*SFR* 000000A0H   DATA     BIT       CD_BSY
*SFR* 000000A1H   DATA     BYTE      CD_CON
*SFR* 0000009BH   DATA     BYTE      CD_CORDXH
*SFR* 0000009AH   DATA     BYTE      CD_CORDXL
*SFR* 0000009DH   DATA     BYTE      CD_CORDYH
*SFR* 0000009CH   DATA     BYTE      CD_CORDYL
*SFR* 0000009FH   DATA     BYTE      CD_CORDZH
*SFR* 0000009EH   DATA     BYTE      CD_CORDZL
*SFR* 000000A0H   DATA     BYTE      CD_STATC
*SFR* 000000BAH   DATA     BYTE      CMCON
*SFR* 000000BEH   DATA     BYTE      COCON
*SFR* 000000C0H   DATA     BIT       CP_RL2
*SFR* 000000D0H.7 DATA     BIT       CY
*SFR* 000000A0H.4 DATA     BIT       DMAP
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 5


*SFR* 00000083H   DATA     BYTE      DPH
*SFR* 00000082H   DATA     BYTE      DPL
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000E8H   DATA     BIT       EADC
*SFR* 000000E8H.4 DATA     BIT       ECCIP0
*SFR* 000000E8H.5 DATA     BIT       ECCIP1
*SFR* 000000E8H.6 DATA     BIT       ECCIP2
*SFR* 000000E8H.7 DATA     BIT       ECCIP3
*SFR* 000000A2H   DATA     BYTE      EO
*SFR* 000000A0H.2 DATA     BIT       EOC
*SFR* 000000A0H.1 DATA     BIT       ERROR
*SFR* 000000A8H.4 DATA     BIT       ES
*SFR* 000000E8H.1 DATA     BIT       ESSC
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H   DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000E8H.2 DATA     BIT       EX2
*SFR* 000000C0H.3 DATA     BIT       EXEN2
*SFR* 000000C0H.6 DATA     BIT       EXF2
*SFR* 000000B7H   DATA     BYTE      EXICON0
*SFR* 000000BAH   DATA     BYTE      EXICON1
*SFR* 000000E8H.3 DATA     BIT       EXM
*SFR* 000000D0H.5 DATA     BIT       F0
*SFR* 000000D0H.1 DATA     BIT       F1
*SFR* 000000E9H   DATA     BYTE      FDCON
*SFR* 000000EBH   DATA     BYTE      FDRES
*SFR* 000000EAH   DATA     BYTE      FDSTEP
*SFR* 000000BDH   DATA     BYTE      FEAH
*SFR* 000000BCH   DATA     BYTE      FEAL
*SFR* 000000F7H   DATA     BYTE      HWBPDR
*SFR* 000000F6H   DATA     BYTE      HWBPSR
*SFR* 000000B3H   DATA     BYTE      ID
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*SFR* 000000A8H   DATA     BYTE      IEN0
*SFR* 000000E8H   DATA     BYTE      IEN1
*SFR* 000000B0H.1 DATA     BIT       IERR
*SFR* 000000A0H.3 DATA     BIT       INT_EN
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 000000F8H   DATA     BYTE      IP1
*SFR* 000000B9H   DATA     BYTE      IPH
*SFR* 000000F9H   DATA     BYTE      IPH1
*SFR* 000000B4H   DATA     BYTE      IRCON0
*SFR* 000000B5H   DATA     BYTE      IRCON1
*SFR* 000000B6H   DATA     BYTE      IRCON2
*SFR* 000000B4H   DATA     BYTE      IRCON3
*SFR* 000000B5H   DATA     BYTE      IRCON4
*SFR* 000000B0H   DATA     BIT       IRDY
*SFR* 00000088H   DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
*SFR* 000000A0H.5 DATA     BIT       KEEPX
*SFR* 000000A0H.6 DATA     BIT       KEEPY
*SFR* 000000A0H.7 DATA     BIT       KEEPZ
      0100015BH   CODE     ---       main
*SFR* 000000B0H.2 DATA     BIT       MDU_BSY
*SFR* 000000B2H   DATA     BYTE      MDU_MD0
*SFR* 000000B3H   DATA     BYTE      MDU_MD1
*SFR* 000000B4H   DATA     BYTE      MDU_MD2
*SFR* 000000B5H   DATA     BYTE      MDU_MD3
*SFR* 000000B6H   DATA     BYTE      MDU_MD4
*SFR* 000000B7H   DATA     BYTE      MDU_MD5
*SFR* 000000B1H   DATA     BYTE      MDU_MDUCON
*SFR* 000000B0H   DATA     BYTE      MDU_MDUSTAT
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 6


*SFR* 000000B2H   DATA     BYTE      MDU_MR0
*SFR* 000000B3H   DATA     BYTE      MDU_MR1
*SFR* 000000B4H   DATA     BYTE      MDU_MR2
*SFR* 000000B5H   DATA     BYTE      MDU_MR3
*SFR* 000000B6H   DATA     BYTE      MDU_MR4
*SFR* 000000B7H   DATA     BYTE      MDU_MR5
*SFR* 00000096H   DATA     BYTE      MEX3
*SFR* 000000E9H   DATA     BYTE      MISC_CON
*SFR* 000000F3H   DATA     BYTE      MMBPCR
*SFR* 000000F1H   DATA     BYTE      MMCR
*SFR* 000000E9H   DATA     BYTE      MMCR2
*SFR* 000000F5H   DATA     BYTE      MMDR
*SFR* 000000F4H   DATA     BYTE      MMICR
*SFR* 000000F2H   DATA     BYTE      MMSR
*SFR* 000000EBH   DATA     BYTE      MMWR1
*SFR* 000000ECH   DATA     BYTE      MMWR2
*SFR* 000000B3H   DATA     BYTE      MODPISEL
*SFR* 000000B7H   DATA     BYTE      MODPISEL1
*SFR* 000000BAH   DATA     BYTE      MODPISEL2
*SFR* 000000BDH   DATA     BYTE      MODSUSP
*SFR* 000000BBH   DATA     BYTE      NMICON
*SFR* 000000BCH   DATA     BYTE      NMISR
*SFR* 000000B6H   DATA     BYTE      OSC_CON
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H   DATA     BIT       P
*SFR* 00000080H   DATA     BYTE      P0_ALTSEL0
*SFR* 00000086H   DATA     BYTE      P0_ALTSEL1
*SFR* 00000080H   DATA     BYTE      P0_DATA
*SFR* 00000086H   DATA     BYTE      P0_DIR
*SFR* 00000080H   DATA     BYTE      P0_OD
*SFR* 00000086H   DATA     BYTE      P0_PUDEN
*SFR* 00000080H   DATA     BYTE      P0_PUDSEL
*SFR* 00000090H   DATA     BYTE      P1_ALTSEL0
*SFR* 00000091H   DATA     BYTE      P1_ALTSEL1
*SFR* 00000090H   DATA     BYTE      P1_DATA
*SFR* 00000091H   DATA     BYTE      P1_DIR
*SFR* 00000090H   DATA     BYTE      P1_OD
*SFR* 00000091H   DATA     BYTE      P1_PUDEN
*SFR* 00000090H   DATA     BYTE      P1_PUDSEL
*SFR* 000000A0H   DATA     BYTE      P2_DATA
*SFR* 000000A1H   DATA     BYTE      P2_DIR
*SFR* 000000A1H   DATA     BYTE      P2_PUDEN
*SFR* 000000A0H   DATA     BYTE      P2_PUDSEL
*SFR* 000000B0H   DATA     BYTE      P3_ALTSEL0
*SFR* 000000B1H   DATA     BYTE      P3_ALTSEL1
*SFR* 000000B0H   DATA     BYTE      P3_DATA
*SFR* 000000B1H   DATA     BYTE      P3_DIR
*SFR* 000000B0H   DATA     BYTE      P3_OD
*SFR* 000000B1H   DATA     BYTE      P3_PUDEN
*SFR* 000000B0H   DATA     BYTE      P3_PUDSEL
*SFR* 000000C8H   DATA     BYTE      P4_ALTSEL0
*SFR* 000000C9H   DATA     BYTE      P4_ALTSEL1
*SFR* 000000C8H   DATA     BYTE      P4_DATA
*SFR* 000000C9H   DATA     BYTE      P4_DIR
*SFR* 000000C8H   DATA     BYTE      P4_OD
*SFR* 000000C9H   DATA     BYTE      P4_PUDEN
*SFR* 000000C8H   DATA     BYTE      P4_PUDSEL
*SFR* 00000092H   DATA     BYTE      P5_ALTSEL0
*SFR* 00000093H   DATA     BYTE      P5_ALTSEL1
*SFR* 00000092H   DATA     BYTE      P5_DATA
*SFR* 00000093H   DATA     BYTE      P5_DIR
*SFR* 00000092H   DATA     BYTE      P5_OD
*SFR* 00000093H   DATA     BYTE      P5_PUDEN
*SFR* 00000092H   DATA     BYTE      P5_PUDSEL
*SFR* 000000F8H   DATA     BIT       PADC
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 7


*SFR* 000000BBH   DATA     BYTE      PASSWD
*SFR* 000000F8H.4 DATA     BIT       PCCIP0
*SFR* 000000F8H.5 DATA     BIT       PCCIP1
*SFR* 000000F8H.6 DATA     BIT       PCCIP2
*SFR* 000000F8H.7 DATA     BIT       PCCIP3
*SFR* 00000087H   DATA     BYTE      PCON
*SFR* 000000B7H   DATA     BYTE      PLL_CON
*SFR* 000000EAH   DATA     BYTE      PLL_CON1
*SFR* 000000B4H   DATA     BYTE      PMCON0
*SFR* 000000B5H   DATA     BYTE      PMCON1
*SFR* 000000BBH   DATA     BYTE      PMCON2
*SFR* 000000B2H   DATA     BYTE      PORT_PAGE
*SFR* 000000B8H.4 DATA     BIT       PS
*SFR* 000000F8H.1 DATA     BIT       PSSC
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H   DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
*SFR* 000000F8H.2 DATA     BIT       PX2
*SFR* 000000F8H.3 DATA     BIT       PXM
*SFR* 00000098H.2 DATA     BIT       RB8
*SFR* 000000C8H.2 DATA     BIT       RB8_1
*SFR* 00000098H.4 DATA     BIT       REN
*SFR* 000000C8H.4 DATA     BIT       REN_1
*SFR* 00000098H   DATA     BIT       RI
*SFR* 000000C8H   DATA     BIT       RI_1
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000098H   DATA     BYTE      SCON
*SFR* 000000BFH   DATA     BYTE      SCU_PAGE
*SFR* 00000098H.7 DATA     BIT       SM0
*SFR* 000000C8H.7 DATA     BIT       SM0_1
*SFR* 00000098H.6 DATA     BIT       SM1
*SFR* 000000C8H.6 DATA     BIT       SM1_1
*SFR* 00000098H.5 DATA     BIT       SM2
*SFR* 000000C8H.5 DATA     BIT       SM2_1
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000AFH   DATA     BYTE      SSC_BRH
*SFR* 000000AEH   DATA     BYTE      SSC_BRL
*SFR* 000000ABH   DATA     BYTE      SSC_CONH_O
*SFR* 000000ABH   DATA     BYTE      SSC_CONH_P
*SFR* 000000AAH   DATA     BYTE      SSC_CONL_O
*SFR* 000000AAH   DATA     BYTE      SSC_CONL_P
*SFR* 000000A9H   DATA     BYTE      SSC_PISEL
*SFR* 000000ADH   DATA     BYTE      SSC_RBL
*SFR* 000000ACH   DATA     BYTE      SSC_TBL
*SFR* 0000008FH   DATA     BYTE      SYSCON0
*SFR* 000000C3H   DATA     BYTE      T21_RC2H
*SFR* 000000C2H   DATA     BYTE      T21_RC2L
*SFR* 000000C2H   DATA     WORD      T21_RC2LH
*SFR* 000000C0H   DATA     BYTE      T21_T2CON
*SFR* 000000C5H   DATA     BYTE      T21_T2H
*SFR* 000000C4H   DATA     BYTE      T21_T2L
*SFR* 000000C4H   DATA     WORD      T21_T2LH
*SFR* 000000C1H   DATA     BYTE      T21_T2MOD
*SFR* 000000C3H   DATA     BYTE      T2_RC2H
*SFR* 000000C2H   DATA     BYTE      T2_RC2L
*SFR* 000000C2H   DATA     WORD      T2_RC2LH
*SFR* 000000C0H   DATA     BYTE      T2_T2CON
*SFR* 000000C5H   DATA     BYTE      T2_T2H
*SFR* 000000C4H   DATA     BYTE      T2_T2L
*SFR* 000000C4H   DATA     WORD      T2_T2LH
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 8


*SFR* 000000C1H   DATA     BYTE      T2_T2MOD
*SFR* 00000098H.3 DATA     BIT       TB8
*SFR* 000000C8H.3 DATA     BIT       TB8_1
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 000000C0H.7 DATA     BIT       TF2
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 00000098H.1 DATA     BIT       TI
*SFR* 000000C8H.1 DATA     BIT       TI_1
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 00000089H   DATA     BYTE      TMOD
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 000000C0H.2 DATA     BIT       TR2
*SFR* 000000CAH   DATA     BYTE      UART1_BCON
*SFR* 000000CBH   DATA     BYTE      UART1_BG
*SFR* 000000CCH   DATA     BYTE      UART1_FDCON
*SFR* 000000CEH   DATA     BYTE      UART1_FDRES
*SFR* 000000CDH   DATA     BYTE      UART1_FDSTEP
*SFR* 000000C9H   DATA     BYTE      UART1_SBUF
*SFR* 000000C8H   DATA     BYTE      UART1_SCON
*SFR* 000000BBH   DATA     BYTE      WDTCON
*SFR* 000000BFH   DATA     BYTE      WDTH
*SFR* 000000BEH   DATA     BYTE      WDTL
*SFR* 000000BCH   DATA     BYTE      WDTREL
*SFR* 000000BDH   DATA     BYTE      WDTWINB
*SFR* 000000B3H   DATA     BYTE      XADDRH



SYMBOL TABLE OF MODULE:  JumpPro (MAIN)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       MAIN
      0100015BH   PUBLIC    CODE     ---       main
      000000A0H.5 SFRSYM    DATA     BIT       KEEPX
      000000B0H   SFRSYM    DATA     BYTE      P3_DATA
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60RL
      000000DBH   SFRSYM    DATA     BYTE      CAN_DATA0
      000000A0H.6 SFRSYM    DATA     BIT       KEEPY
      000000C1H   SFRSYM    DATA     BYTE      T21_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T21_T2CON
      000000C8H   SFRSYM    DATA     BYTE      P4_DATA
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61RL
      000000DCH   SFRSYM    DATA     BYTE      CAN_DATA1
      000000D3H   SFRSYM    DATA     BYTE      ADC_EVINPR
      000000A0H.7 SFRSYM    DATA     BIT       KEEPZ
      000000C9H   SFRSYM    DATA     BYTE      UART1_SBUF
      00000092H   SFRSYM    DATA     BYTE      P5_DATA
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62RL
      000000DDH   SFRSYM    DATA     BYTE      CAN_DATA2
      000000DAH   SFRSYM    DATA     BYTE      CAN_ADH
      000000C4H   SFRSYM    DATA     WORD      T2_T2LH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63RL
      000000DEH   SFRSYM    DATA     BYTE      CAN_DATA3
      000000D2H   SFRSYM    DATA     BYTE      ADC_EVINSR
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000C8H   SFRSYM    DATA     BYTE      UART1_SCON
      000000D9H   SFRSYM    DATA     BYTE      CAN_ADL
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000CAH   SFRSYM    DATA     BYTE      ADC_CRCR1
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 9


      000000FDH   SFRSYM    DATA     BYTE      CCU6_MODCTRH
      000000B2H   SFRSYM    DATA     BYTE      MDU_MD0
      000000BEH   SFRSYM    DATA     BYTE      BG
      000000B3H   SFRSYM    DATA     BYTE      MDU_MD1
      000000BCH   SFRSYM    DATA     BYTE      NMISR
      000000B4H   SFRSYM    DATA     BYTE      MDU_MD2
      000000FCH   SFRSYM    DATA     BYTE      CCU6_MODCTRL
      000000A0H.1 SFRSYM    DATA     BIT       ERROR
      000000B5H   SFRSYM    DATA     BYTE      MDU_MD3
      000000B6H   SFRSYM    DATA     BYTE      MDU_MD4
      000000A8H   SFRSYM    DATA     BYTE      IEN0
      000000B3H   SFRSYM    DATA     BYTE      ID
      000000E8H   SFRSYM    DATA     BIT       EADC
      000000B7H   SFRSYM    DATA     BYTE      MDU_MD5
      000000E8H   SFRSYM    DATA     BYTE      IEN1
      000000A7H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFH
      000000B1H   SFRSYM    DATA     BYTE      MDU_MDUCON
      000000CCH   SFRSYM    DATA     BYTE      ADC_CRMR1
      000000A3H   SFRSYM    DATA     BYTE      CCU6_PAGE
      0000008FH   SFRSYM    DATA     BYTE      SYSCON0
      000000B7H   SFRSYM    DATA     BYTE      MODPISEL1
      000000A6H   SFRSYM    DATA     BYTE      CCU6_CMPMODIFL
      000000BAH   SFRSYM    DATA     BYTE      MODPISEL2
      000000F9H   SFRSYM    DATA     BYTE      IPH1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_MCMOUTH
      000000CBH   SFRSYM    DATA     BYTE      ADC_CRPR1
      000000A2H   SFRSYM    DATA     BYTE      EO
      000000BDH   SFRSYM    DATA     BYTE      FEAH
      000000D2H   SFRSYM    DATA     BYTE      ADC_QINR0
      000000D2H   SFRSYM    DATA     BYTE      ADC_QBUR0
      000000C0H.6 SFRSYM    DATA     BIT       EXF2
      000000E8H.4 SFRSYM    DATA     BIT       ECCIP0
      0000009DH   SFRSYM    DATA     BYTE      CCU6_T12PRH
      000000E8H.5 SFRSYM    DATA     BIT       ECCIP1
      000000B2H   SFRSYM    DATA     BYTE      MDU_MR0
      0000009FH   SFRSYM    DATA     BYTE      CCU6_T13PRH
      0000009AH   SFRSYM    DATA     BYTE      CCU6_MCMOUTL
      000000E8H.6 SFRSYM    DATA     BIT       ECCIP2
      000000A8H.4 SFRSYM    DATA     BIT       ES
      000000CDH   SFRSYM    DATA     BYTE      UART1_FDSTEP
      000000B3H   SFRSYM    DATA     BYTE      MDU_MR1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_IENH
      000000F8H   SFRSYM    DATA     BIT       PADC
      000000E8H.7 SFRSYM    DATA     BIT       ECCIP3
      000000B4H   SFRSYM    DATA     BYTE      MDU_MR2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000BCH   SFRSYM    DATA     BYTE      FEAL
      000000B5H   SFRSYM    DATA     BYTE      MDU_MR3
      0000009CH   SFRSYM    DATA     BYTE      CCU6_T12PRL
      000000D8H   SFRSYM    DATA     BYTE      CAN_ADCON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60SRLH
      00000098H   SFRSYM    DATA     BIT       RI
      000000B6H   SFRSYM    DATA     BYTE      MDU_MR4
      0000009EH   SFRSYM    DATA     BYTE      CCU6_T13PRL
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61SRLH
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000B7H   SFRSYM    DATA     BYTE      MDU_MR5
      0000009CH   SFRSYM    DATA     BYTE      CCU6_IENL
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62SRLH
      00000098H.1 SFRSYM    DATA     BIT       TI
      000000FFH   SFRSYM    DATA     BYTE      CCU6_TRPCTRH
      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63SRLH
      000000F8H.4 SFRSYM    DATA     BIT       PCCIP0
      000000FEH   SFRSYM    DATA     BYTE      CCU6_TRPCTRL
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESRA0H
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 10


      000000F8H.5 SFRSYM    DATA     BIT       PCCIP1
      000000BDH   SFRSYM    DATA     BYTE      WDTWINB
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESRA1H
      000000B8H.4 SFRSYM    DATA     BIT       PS
      000000F8H.6 SFRSYM    DATA     BIT       PCCIP2
      000000A0H.4 SFRSYM    DATA     BIT       DMAP
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_O
      00000081H   SFRSYM    DATA     BYTE      SP
      0000009FH   SFRSYM    DATA     BYTE      CCU6_INPH
      000000BDH   SFRSYM    DATA     BYTE      BCON
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESRA2H
      000000F8H.7 SFRSYM    DATA     BIT       PCCIP3
      000000ABH   SFRSYM    DATA     BYTE      SSC_CONH_P
      000000A4H   SFRSYM    DATA     BYTE      CCU6_PISEL2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESRA3H
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESRA0L
      0000009BH   SFRSYM    DATA     BYTE      CCU6_T12MSELH
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESRA1L
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_O
      000000AFH   SFRSYM    DATA     BYTE      SSC_BRH
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_INPL
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESRA2L
      000000C3H   SFRSYM    DATA     BYTE      T21_RC2H
      000000AAH   SFRSYM    DATA     BYTE      SSC_CONL_P
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESRA3L
      000000C0H.1 SFRSYM    DATA     BIT       C_T2
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_TCTR0H
      0000009AH   SFRSYM    DATA     BYTE      CCU6_T12MSELL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISRH
      000000ADH   SFRSYM    DATA     BYTE      SSC_RBL
      000000AEH   SFRSYM    DATA     BYTE      SSC_BRL
      000000A5H   SFRSYM    DATA     BYTE      CCU6_ISSH
      000000CFH   SFRSYM    DATA     BYTE      ADC_ETRCR
      000000CAH   SFRSYM    DATA     WORD      ADC_RESR0LH
      000000C8H   SFRSYM    DATA     BIT       RI_1
      000000C2H   SFRSYM    DATA     BYTE      T21_RC2L
      000000FBH   SFRSYM    DATA     BYTE      CCU6_TCTR2H
      000000CCH   SFRSYM    DATA     WORD      ADC_RESR1LH
      000000ACH   SFRSYM    DATA     BYTE      SSC_TBL
      000000B4H   SFRSYM    DATA     BYTE      IRCON0
      000000CFH   SFRSYM    DATA     BYTE      ADC_Q0R0
      000000CEH   SFRSYM    DATA     WORD      ADC_RESR2LH
      000000C8H.1 SFRSYM    DATA     BIT       TI_1
      000000BDH   SFRSYM    DATA     BYTE      MODSUSP
      000000B5H   SFRSYM    DATA     BYTE      IRCON1
      0000009DH   SFRSYM    DATA     BYTE      CCU6_TCTR4H
      000000A6H   SFRSYM    DATA     BYTE      CCU6_TCTR0L
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISRL
      000000D2H   SFRSYM    DATA     WORD      ADC_RESR3LH
      000000E8H.1 SFRSYM    DATA     BIT       ESSC
      000000B4H   SFRSYM    DATA     BYTE      PMCON0
      000000B6H   SFRSYM    DATA     BYTE      IRCON2
      000000A4H   SFRSYM    DATA     BYTE      CCU6_ISSL
      000000DBH   SFRSYM    DATA     WORD      CAN_DATA01
      000000B5H   SFRSYM    DATA     BYTE      PMCON1
      00000080H   SFRSYM    DATA     BYTE      P0_PUDSEL
      000000F1H   SFRSYM    DATA     BYTE      MMCR
      000000B4H   SFRSYM    DATA     BYTE      IRCON3
      000000FAH   SFRSYM    DATA     BYTE      CCU6_TCTR2L
      00000099H   SFRSYM    DATA     BYTE      SBUF
      000000BBH   SFRSYM    DATA     BYTE      PMCON2
      00000087H   SFRSYM    DATA     BYTE      PCON
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 11


      00000090H   SFRSYM    DATA     BYTE      P1_PUDSEL
      000000F5H   SFRSYM    DATA     BYTE      MMDR
      000000B5H   SFRSYM    DATA     BYTE      IRCON4
      000000A0H   SFRSYM    DATA     BYTE      P2_PUDSEL
      0000009BH   SFRSYM    DATA     BYTE      CD_CORDXH
      0000009CH   SFRSYM    DATA     BYTE      CCU6_TCTR4L
      000000C4H   SFRSYM    DATA     WORD      T21_T2LH
      000000B0H.1 SFRSYM    DATA     BIT       IERR
      000000B0H   SFRSYM    DATA     BYTE      P3_PUDSEL
      0000009DH   SFRSYM    DATA     BYTE      CD_CORDYH
      000000DDH   SFRSYM    DATA     WORD      CAN_DATA23
      00000098H   SFRSYM    DATA     BYTE      SCON
      000000C8H   SFRSYM    DATA     BYTE      P4_PUDSEL
      0000009FH   SFRSYM    DATA     BYTE      CD_CORDZH
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000C5H   SFRSYM    DATA     BYTE      T2_T2H
      00000092H   SFRSYM    DATA     BYTE      P5_PUDSEL
      000000A6H   SFRSYM    DATA     BYTE      CCU6_PSLR
      000000B0H.2 SFRSYM    DATA     BIT       MDU_BSY
      0000009AH   SFRSYM    DATA     BYTE      CD_CORDXL
      0000009CH   SFRSYM    DATA     BYTE      CD_CORDYL
      000000BFH   SFRSYM    DATA     BYTE      WDTH
      0000009EH   SFRSYM    DATA     BYTE      CD_CORDZL
      000000B0H   SFRSYM    DATA     BIT       IRDY
      000000C4H   SFRSYM    DATA     BYTE      T2_T2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_GLOBCTR
      000000F8H.1 SFRSYM    DATA     BIT       PSSC
      000000CCH   SFRSYM    DATA     BYTE      UART1_FDCON
      000000A7H   SFRSYM    DATA     BYTE      CCU6_MCMCTR
      000000BEH   SFRSYM    DATA     BYTE      WDTL
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      00000088H.1 SFRSYM    DATA     BIT       IE0
      000000FAH   SFRSYM    DATA     WORD      CCU6_T12LH
      00000088H.3 SFRSYM    DATA     BIT       IE1
      000000FCH   SFRSYM    DATA     WORD      CCU6_T13LH
      00000086H   SFRSYM    DATA     BYTE      P0_DIR
      000000F2H   SFRSYM    DATA     BYTE      MMSR
      00000091H   SFRSYM    DATA     BYTE      P1_DIR
      000000F0H   SFRSYM    DATA     BYTE      B
      000000CAH   SFRSYM    DATA     BYTE      ADC_RCR0
      000000A1H   SFRSYM    DATA     BYTE      P2_DIR
      000000CBH   SFRSYM    DATA     BYTE      ADC_RCR1
      000000C2H   SFRSYM    DATA     WORD      T2_RC2LH
      000000B1H   SFRSYM    DATA     BYTE      P3_DIR
      000000F3H   SFRSYM    DATA     BYTE      MMBPCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_RCR2
      000000C0H   SFRSYM    DATA     BIT       CP_RL2
      000000C9H   SFRSYM    DATA     BYTE      P4_DIR
      000000CDH   SFRSYM    DATA     BYTE      ADC_RCR3
      000000CEH   SFRSYM    DATA     BYTE      UART1_FDRES
      000000B2H   SFRSYM    DATA     BYTE      PORT_PAGE
      00000093H   SFRSYM    DATA     BYTE      P5_DIR
      000000BBH   SFRSYM    DATA     BYTE      NMICON
      000000FAH   SFRSYM    DATA     WORD      CCU6_CC60RLH
      000000D1H   SFRSYM    DATA     BYTE      ADC_PAGE
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000FCH   SFRSYM    DATA     WORD      CCU6_CC61RLH
      000000EAH   SFRSYM    DATA     BYTE      FDSTEP
      000000A1H   SFRSYM    DATA     BYTE      CD_CON
      000000FEH   SFRSYM    DATA     WORD      CCU6_CC62RLH
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000A9H   SFRSYM    DATA     BYTE      SSC_PISEL
      000000F7H   SFRSYM    DATA     BYTE      HWBPDR
      000000CBH   SFRSYM    DATA     BYTE      ADC_GLOBSTR
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 12


      0000009AH   SFRSYM    DATA     WORD      CCU6_CC63RLH
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      000000F8H   SFRSYM    DATA     BYTE      IP1
      000000CDH   SFRSYM    DATA     BYTE      ADC_QMR0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      000000C0H.7 SFRSYM    DATA     BIT       TF2
      00000098H.2 SFRSYM    DATA     BIT       RB8
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000D9H   SFRSYM    DATA     WORD      CAN_ADLH
      00000088H   SFRSYM    DATA     BIT       IT0
      000000A8H   SFRSYM    DATA     BIT       EX0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000CDH   SFRSYM    DATA     BYTE      ADC_LCBR
      00000088H.2 SFRSYM    DATA     BIT       IT1
      00000098H.3 SFRSYM    DATA     BIT       TB8
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      000000C1H   SFRSYM    DATA     BYTE      T2_T2MOD
      000000C0H   SFRSYM    DATA     BYTE      T2_T2CON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60SRH
      000000D0H   SFRSYM    DATA     BIT       P
      000000E8H.2 SFRSYM    DATA     BIT       EX2
      000000A5H   SFRSYM    DATA     BYTE      CCU6_T12DTCH
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61SRH
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHCTR0
      00000098H.7 SFRSYM    DATA     BIT       SM0
      0000008AH   SFRSYM    DATA     BYTE      TL0
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62SRH
      000000CEH   SFRSYM    DATA     BYTE      ADC_QSR0
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHCTR1
      00000098H.6 SFRSYM    DATA     BIT       SM1
      000000BBH   SFRSYM    DATA     BYTE      WDTCON
      0000008BH   SFRSYM    DATA     BYTE      TL1
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63SRH
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHCTR2
      00000098H.5 SFRSYM    DATA     BIT       SM2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_CC60SRL
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHCTR3
      000000A4H   SFRSYM    DATA     BYTE      CCU6_T12DTCL
      000000FCH   SFRSYM    DATA     BYTE      CCU6_CC61SRL
      000000CEH   SFRSYM    DATA     BYTE      ADC_CHCTR4
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      000000BCH   SFRSYM    DATA     BYTE      WDTREL
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CC62SRL
      000000CFH   SFRSYM    DATA     BYTE      ADC_CHCTR5
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      0000009AH   SFRSYM    DATA     BYTE      CCU6_CC63SRL
      000000D2H   SFRSYM    DATA     BYTE      ADC_CHCTR6
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      000000A0H   SFRSYM    DATA     BIT       CD_BSY
      000000D3H   SFRSYM    DATA     BYTE      ADC_CHCTR7
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000A0H.2 SFRSYM    DATA     BIT       EOC
      0000009DH   SFRSYM    DATA     BYTE      CCU6_ISH
      000000CEH   SFRSYM    DATA     BYTE      ADC_INPCR0
      000000C0H.2 SFRSYM    DATA     BIT       TR2
      000000B8H   SFRSYM    DATA     BIT       PX0
      000000CBH   SFRSYM    DATA     BYTE      UART1_BG
      00000080H   SFRSYM    DATA     BYTE      P0_ALTSEL0
      000000F6H   SFRSYM    DATA     BYTE      HWBPSR
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 13


      000000B8H.2 SFRSYM    DATA     BIT       PX1
      00000090H   SFRSYM    DATA     BYTE      P1_ALTSEL0
      00000086H   SFRSYM    DATA     BYTE      P0_ALTSEL1
      000000F8H.2 SFRSYM    DATA     BIT       PX2
      00000091H   SFRSYM    DATA     BYTE      P1_ALTSEL1
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CMPSTATH
      000000B0H   SFRSYM    DATA     BYTE      P3_ALTSEL0
      0000009CH   SFRSYM    DATA     BYTE      CCU6_ISL
      000000CEH   SFRSYM    DATA     BYTE      ADC_VFCR
      000000C8H   SFRSYM    DATA     BYTE      P4_ALTSEL0
      000000B1H   SFRSYM    DATA     BYTE      P3_ALTSEL1
      00000083H   SFRSYM    DATA     BYTE      DPH
      000000C8H.2 SFRSYM    DATA     BIT       RB8_1
      00000092H   SFRSYM    DATA     BYTE      P5_ALTSEL0
      000000C9H   SFRSYM    DATA     BYTE      P4_ALTSEL1
      00000093H   SFRSYM    DATA     BYTE      P5_ALTSEL1
      00000086H   SFRSYM    DATA     BYTE      P0_PUDEN
      000000FEH   SFRSYM    DATA     BYTE      CCU6_CMPSTATL
      000000C8H.3 SFRSYM    DATA     BIT       TB8_1
      000000A0H.3 SFRSYM    DATA     BIT       INT_EN
      00000091H   SFRSYM    DATA     BYTE      P1_PUDEN
      000000CBH   SFRSYM    DATA     BYTE      ADC_RESR0H
      000000CCH   SFRSYM    DATA     BYTE      ADC_PRAR
      000000A1H   SFRSYM    DATA     BYTE      P2_PUDEN
      000000B3H   SFRSYM    DATA     BYTE      MODPISEL
      00000082H   SFRSYM    DATA     BYTE      DPL
      000000CDH   SFRSYM    DATA     BYTE      ADC_RESR1H
      000000C8H.7 SFRSYM    DATA     BIT       SM0_1
      000000B1H   SFRSYM    DATA     BYTE      P3_PUDEN
      000000B9H   SFRSYM    DATA     BYTE      IPH
      000000CFH   SFRSYM    DATA     BYTE      ADC_RESR2H
      0000009CH   SFRSYM    DATA     WORD      CCU6_T12PRLH
      000000C8H.6 SFRSYM    DATA     BIT       SM1_1
      000000C9H   SFRSYM    DATA     BYTE      P4_PUDEN
      000000E9H   SFRSYM    DATA     BYTE      MMCR2
      000000D3H   SFRSYM    DATA     BYTE      ADC_RESR3H
      000000CBH   SFRSYM    DATA     BYTE      ADC_CHINCR
      0000009EH   SFRSYM    DATA     WORD      CCU6_T13PRLH
      000000C8H.5 SFRSYM    DATA     BIT       SM2_1
      000000C0H.3 SFRSYM    DATA     BIT       EXEN2
      00000093H   SFRSYM    DATA     BYTE      P5_PUDEN
      000000CAH   SFRSYM    DATA     BYTE      ADC_RESR0L
      000000CCH   SFRSYM    DATA     BYTE      ADC_RESR1L
      00000098H.4 SFRSYM    DATA     BIT       REN
      000000CEH   SFRSYM    DATA     BYTE      ADC_RESR2L
      000000CAH   SFRSYM    DATA     BYTE      ADC_CHINFR
      000000C5H   SFRSYM    DATA     BYTE      T21_T2H
      0000009FH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSH
      000000D2H   SFRSYM    DATA     BYTE      ADC_RESR3L
      000000A0H   SFRSYM    DATA     BYTE      CD_STATC
      000000E8H.3 SFRSYM    DATA     BIT       EXM
      000000C4H   SFRSYM    DATA     BYTE      T21_T2L
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      0000009FH   SFRSYM    DATA     BYTE      CCU6_PISEL0H
      0000009EH   SFRSYM    DATA     BYTE      CCU6_MCMOUTSL
      000000E9H   SFRSYM    DATA     BYTE      FDCON
      000000B0H   SFRSYM    DATA     BYTE      MDU_MDUSTAT
      000000CAH   SFRSYM    DATA     WORD      ADC_RESRA0LH
      000000CCH   SFRSYM    DATA     WORD      ADC_RESRA1LH
      000000E9H   SFRSYM    DATA     BYTE      MISC_CON
      0000009EH   SFRSYM    DATA     BYTE      CCU6_PISEL0L
      000000CEH   SFRSYM    DATA     WORD      ADC_RESRA2LH
      000000CDH   SFRSYM    DATA     BYTE      ADC_CHINPR
      000000D2H   SFRSYM    DATA     WORD      ADC_RESRA3LH
      000000BAH   SFRSYM    DATA     BYTE      CMCON
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 14


      000000CAH   SFRSYM    DATA     BYTE      UART1_BCON
      000000FBH   SFRSYM    DATA     BYTE      CCU6_T12H
      000000CFH   SFRSYM    DATA     BYTE      ADC_EVINCR
      000000CCH   SFRSYM    DATA     BYTE      ADC_CHINSR
      00000080H   SFRSYM    DATA     BYTE      P0_OD
      000000BEH   SFRSYM    DATA     BYTE      COCON
      000000FDH   SFRSYM    DATA     BYTE      CCU6_T13H
      000000C2H   SFRSYM    DATA     WORD      T21_RC2LH
      00000090H   SFRSYM    DATA     BYTE      P1_OD
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000F8H.3 SFRSYM    DATA     BIT       PXM
      000000EBH   SFRSYM    DATA     BYTE      MMWR1
      000000EBH   SFRSYM    DATA     BYTE      FDRES
      000000CEH   SFRSYM    DATA     BYTE      ADC_EVINFR
      000000C8H.4 SFRSYM    DATA     BIT       REN_1
      000000D0H.1 SFRSYM    DATA     BIT       F1
      000000C3H   SFRSYM    DATA     BYTE      T2_RC2H
      000000B0H   SFRSYM    DATA     BYTE      P3_OD
      000000ECH   SFRSYM    DATA     BYTE      MMWR2
      000000FAH   SFRSYM    DATA     BYTE      CCU6_T12L
      000000C8H   SFRSYM    DATA     BYTE      P4_OD
      000000FCH   SFRSYM    DATA     BYTE      CCU6_T13L
      00000092H   SFRSYM    DATA     BYTE      P5_OD
      000000B7H   SFRSYM    DATA     BYTE      EXICON0
      000000F4H   SFRSYM    DATA     BYTE      MMICR
      000000BAH   SFRSYM    DATA     BYTE      EXICON1
      000000C2H   SFRSYM    DATA     BYTE      T2_RC2L
      000000D0H   SFRSYM    DATA     BYTE      PSW
      000000FBH   SFRSYM    DATA     BYTE      CCU6_CC60RH
      00000080H   SFRSYM    DATA     BYTE      P0_DATA
      000000FDH   SFRSYM    DATA     BYTE      CCU6_CC61RH
      00000090H   SFRSYM    DATA     BYTE      P1_DATA
      000000FFH   SFRSYM    DATA     BYTE      CCU6_CC62RH
      000000A0H   SFRSYM    DATA     BYTE      P2_DATA
      0000009BH   SFRSYM    DATA     BYTE      CCU6_CC63RH

      0100015BH   BLOCK     CODE     ---       LVL=0
      0100015BH   LINE      CODE     ---       #195
      0100015BH   LINE      CODE     ---       #196
      0100015BH   LINE      CODE     ---       #211
      01000162H   LINE      CODE     ---       #212
      01000162H   LINE      CODE     ---       #213
      01000165H   LINE      CODE     ---       #214
      01000165H   LINE      CODE     ---       #216
      01000165H   LINE      CODE     ---       #217
      01000168H   LINE      CODE     ---       #218
      01000168H   LINE      CODE     ---       #225
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       VECTORMAP

      01000003H   BLOCK     CODE     NEAR LAB  LVL=0
      01000003H   LINE      CODE     ---       #3
      ---         BLOCKEND  ---      ---       LVL=0

      0100000BH   BLOCK     CODE     NEAR LAB  LVL=0
      0100000BH   LINE      CODE     ---       #5
      ---         BLOCKEND  ---      ---       LVL=0

      01000013H   BLOCK     CODE     NEAR LAB  LVL=0
      01000013H   LINE      CODE     ---       #7
      ---         BLOCKEND  ---      ---       LVL=0

      0100001BH   BLOCK     CODE     NEAR LAB  LVL=0
      0100001BH   LINE      CODE     ---       #9
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 15


      ---         BLOCKEND  ---      ---       LVL=0

      01000023H   BLOCK     CODE     NEAR LAB  LVL=0
      01000023H   LINE      CODE     ---       #11
      ---         BLOCKEND  ---      ---       LVL=0

      0100002BH   BLOCK     CODE     NEAR LAB  LVL=0
      0100002BH   LINE      CODE     ---       #13
      ---         BLOCKEND  ---      ---       LVL=0

      01000033H   BLOCK     CODE     NEAR LAB  LVL=0
      01000033H   LINE      CODE     ---       #15
      ---         BLOCKEND  ---      ---       LVL=0

      0100003BH   BLOCK     CODE     NEAR LAB  LVL=0
      0100003BH   LINE      CODE     ---       #17
      ---         BLOCKEND  ---      ---       LVL=0

      01000043H   BLOCK     CODE     NEAR LAB  LVL=0
      01000043H   LINE      CODE     ---       #19
      ---         BLOCKEND  ---      ---       LVL=0

      0100004BH   BLOCK     CODE     NEAR LAB  LVL=0
      0100004BH   LINE      CODE     ---       #21
      ---         BLOCKEND  ---      ---       LVL=0

      01000053H   BLOCK     CODE     NEAR LAB  LVL=0
      01000053H   LINE      CODE     ---       #23
      ---         BLOCKEND  ---      ---       LVL=0

      0100005BH   BLOCK     CODE     NEAR LAB  LVL=0
      0100005BH   LINE      CODE     ---       #25
      ---         BLOCKEND  ---      ---       LVL=0

      01000063H   BLOCK     CODE     NEAR LAB  LVL=0
      01000063H   LINE      CODE     ---       #27
      ---         BLOCKEND  ---      ---       LVL=0

      0100006BH   BLOCK     CODE     NEAR LAB  LVL=0
      0100006BH   LINE      CODE     ---       #29
      ---         BLOCKEND  ---      ---       LVL=0

      01000073H   BLOCK     CODE     NEAR LAB  LVL=0
      01000073H   LINE      CODE     ---       #31
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C_STARTUP
      000000A2H   PUBLIC    DATA     BYTE      ?C?DPSEL
      01000000H   PUBLIC    CODE     NEAR LAB  ?C_STARTUP
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000F0H   SFRSYM    DATA     BYTE      B
      000000BAH   SFRSYM    DATA     BYTE      CMCON
      00000083H   SFRSYM    DATA     BYTE      DPH
      00000082H   SFRSYM    DATA     BYTE      DPL
      00000096H   SFRSYM    DATA     BYTE      MEX3
      000000B6H   SFRSYM    DATA     BYTE      OSC_CON
      000000BBH   SFRSYM    DATA     BYTE      PASSWD
      000000B7H   SFRSYM    DATA     BYTE      PLL_CON
      000000EAH   SFRSYM    DATA     BYTE      PLL_CON1
      000000BFH   SFRSYM    DATA     BYTE      SCU_PAGE
      00000081H   SFRSYM    DATA     BYTE      SP
      000000B3H   SFRSYM    DATA     BYTE      XADDRH
      01000113H   SYMBOL    CODE     NEAR LAB  DELAYXTAL
      01000111H   SYMBOL    CODE     NEAR LAB  DELAYXTAL0
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 16


      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01000144H   SYMBOL    CODE     NEAR LAB  IDATALOOP
      00000001H   SYMBOL    NUMBER   ---       LIN_BSL
      00000001H   SYMBOL    NUMBER   ---       LIN_NAC
      00000001H   SYMBOL    NUMBER   ---       LIN_NAD
      0000000AH   SYMBOL    NUMBER   ---       NDIV
      00000002H   SYMBOL    NUMBER   ---       NDIV_XC86X
      00000018H   SYMBOL    NUMBER   ---       NDIV_XC87X_PLL_CON
      00000020H   SYMBOL    NUMBER   ---       NDIV_XC87X_PLL_CON1
      0000000AH   SYMBOL    NUMBER   ---       NDIV_XC88X
      00000000H   SYMBOL    NUMBER   ---       NR_XC87X
      00000000H   SYMBOL    NUMBER   ---       OD_XC87X
      01000117H   SYMBOL    CODE     NEAR LAB  OSCR_NOTSET
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      0000F000H   SYMBOL    NUMBER   ---       PDATASTART
      000000F0H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      01000100H   SYMBOL    CODE     NEAR LAB  STARTUP1
      01000134H   SYMBOL    CODE     NEAR LAB  WAIT_LOCK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00010000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       XC82X_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC864_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC866_CHIP
      00000000H   SYMBOL    NUMBER   ---       XC874_CHIP_13FF
      00000000H   SYMBOL    NUMBER   ---       XC874_CHIP_16FF
      00000000H   SYMBOL    NUMBER   ---       XC878_CHIP_13FF
      00000000H   SYMBOL    NUMBER   ---       XC878_CHIP_16FF
      00000001H   SYMBOL    NUMBER   ---       XC88X_CHIP
      00000600H   SYMBOL    NUMBER   ---       XDATALEN
      0100014FH   SYMBOL    CODE     NEAR LAB  XDATALOOP
      0000F000H   SYMBOL    NUMBER   ---       XDATASTART
      00000001H   SYMBOL    NUMBER   ---       XTAL

      01000000H   BLOCK     CODE     NEAR LAB  LVL=0
      01000000H   LINE      CODE     ---       #312
      ---         BLOCKEND  ---      ---       LVL=0

      01000100H   BLOCK     CODE     NEAR LAB  LVL=0
      01000100H   LINE      CODE     ---       #367
      01000103H   LINE      CODE     ---       #368
      01000106H   LINE      CODE     ---       #369
      01000109H   LINE      CODE     ---       #370
      0100010CH   LINE      CODE     ---       #371
      0100010FH   LINE      CODE     ---       #373
      01000111H   LINE      CODE     ---       #375
      01000113H   LINE      CODE     ---       #377
      01000115H   LINE      CODE     ---       #378
      01000117H   LINE      CODE     ---       #382
      01000119H   LINE      CODE     ---       #383
      0100011CH   LINE      CODE     ---       #388
      0100011EH   LINE      CODE     ---       #389
      01000120H   LINE      CODE     ---       #390
      01000123H   LINE      CODE     ---       #394
      01000126H   LINE      CODE     ---       #395
      01000129H   LINE      CODE     ---       #396
      0100012CH   LINE      CODE     ---       #397
      0100012FH   LINE      CODE     ---       #400
      01000132H   LINE      CODE     ---       #401
      01000134H   LINE      CODE     ---       #409
      01000136H   LINE      CODE     ---       #410
      01000138H   LINE      CODE     ---       #411
LX51 LINKER/LOCATER V4.58                                                             02/07/2015  14:09:42  PAGE 17


      0100013BH   LINE      CODE     ---       #413
      0100013EH   LINE      CODE     ---       #414
      01000141H   LINE      CODE     ---       #419
      01000143H   LINE      CODE     ---       #420
      01000144H   LINE      CODE     ---       #421
      01000145H   LINE      CODE     ---       #422
      01000147H   LINE      CODE     ---       #439
      0100014AH   LINE      CODE     ---       #440
      0100014CH   LINE      CODE     ---       #444
      0100014EH   LINE      CODE     ---       #446
      0100014FH   LINE      CODE     ---       #447
      01000150H   LINE      CODE     ---       #448
      01000151H   LINE      CODE     ---       #449
      01000153H   LINE      CODE     ---       #450
      01000155H   LINE      CODE     ---       #486
      01000158H   LINE      CODE     ---       #488
      ---         BLOCKEND  ---      ---       LVL=0

Program Size: data=9.0 xdata=0 const=0 code=153
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)

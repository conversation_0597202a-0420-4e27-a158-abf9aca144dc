//****************************************************************************
// @Module        Shared Interrupt Routines
// @Filename      SHARED_INT.H
// @Project       CL2.0.dav
//----------------------------------------------------------------------------
// @Controller    Infineon XC886CLM-8FF
//
// @Compiler      Keil
//
// @Codegenerator 1.9
//
// @Description   This is the include header file for shared interrupts.
//
//----------------------------------------------------------------------------
// @Date          2013-7-5 07:00:09
//
//****************************************************************************

// USER CODE BEGIN (SHARED_Header,1)

// USER CODE END



#ifndef _SHARED_INT_H_
#define _SHARED_INT_H_

//****************************************************************************
// @Project Includes
//****************************************************************************

// USER CODE BEGIN (SHARED_INT_Header,2)

// USER CODE END


//****************************************************************************
// @Macros
//****************************************************************************

// USER CODE BEGIN (SHARED_INT_Header,3)

// USER CODE END


//****************************************************************************
// @Defines
//****************************************************************************

// USER CODE BEGIN (SHARED_INT_Header,4)

// USER CODE END

//****************************************************************************
// @Prototypes Of Global Functions
//****************************************************************************

void SHINT_vInit(void);


// USER CODE BEGIN (SHARED_INT_Header,5)

// USER CODE END


//****************************************************************************
// @Typedefs
//****************************************************************************

// USER CODE BEGIN (SHARED_INT_Header,6)

// USER CODE END


//****************************************************************************
// @Imported Global Variables
//****************************************************************************

// USER CODE BEGIN (SHARED_INT_Header,7)

// USER CODE END


//****************************************************************************
// @Global Variables
//****************************************************************************

// USER CODE BEGIN (SHARED_INT_Header,8)

// USER CODE END


//****************************************************************************
// @Interrupt Vectors
//****************************************************************************

#define  XINTR5INT   5

// USER CODE BEGIN (SHARED_INT_Header,9)

// USER CODE END


//****************************************************************************
// @Project Includes
//****************************************************************************






#include "MAIN.H"


// USER CODE BEGIN (SHARED_INT_Header,10)

// USER CODE END


#endif  // ifndef _SHARED_INT_H_
